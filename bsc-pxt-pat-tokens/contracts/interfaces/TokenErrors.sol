// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

library TokenErrors {
    error ZeroAddress();
    error Unauthorized();
    error InvalidOperation();
    error InvalidParameter();
    error TransferFailed();
    error EmptyString();
    error ZeroBalance();
    error InsufficientAllowance();

    error EmptyName();
    error EmptySymbol();
    error ZeroSupply();
    error WithdrawalFailed();
    error InvalidNameLength(uint256 minLength, uint256 maxLength, uint256 actualLength);

    error InsufficientUnlockedBalance();
    error InvalidLockAmount();
    error InvalidUnlockTime();
    error NoUnlockableTokens();
    error InvalidUnlockAmount();
    error ExceedsLockedBalance();
    error NoLockedTokens();
    error ExceedsTotalSupply();

    error NotMinter();
    error ExceedsInflationCap();
    error InflationRateOutOfBounds();
    error ExceedsMaxSupply();
    error CannotRecoverSelf();
    error ArrayLengthMismatch();

    error CycleNotEnded();
    error CycleAlreadyFinalized();
    error InvalidArrayLength();
    error InsufficientStakingAmount();
    error InsufficientStakingDuration();
    error InvalidDates();
    error InvalidCycleId();
    error CycleNotFinalized();
    error CycleNotPaid();
    error NoDividendToClaim();
    error DividendAlreadyClaimed();
    error InvalidWeight();
    error InvalidPercentage();

    error InvalidRevenueType();
    error ZeroAmount();
    error InvalidDistributionRatio();
    error RatioSumNotHundred();
    error IndexOutOfRange();
    error AlreadyClaimed();
    error NotUnlocked();

    error InvalidTimeRange();
    error EmptyReportURI();
    error InvalidRecordId();
    error RecordAlreadyVerified();
    error InvalidAuditId();
    error UpdateCycleTooShort();
    error CycleNotComplete();

    error PoolInactive();
    error MiningEnded();
    error PoolAlreadyExists();
    error InvalidRewardBoost();
    error InvalidHarvestInterval();
    error NoRewardToClaim();

    error CapturePeriodNotElapsed();
    error InvalidQuarter();
    error AlertNotActive();
    error InvalidAlertThreshold();
    error ExceedsAlertThreshold();

    error InvalidAddress(string entityName);
    error ZeroDuration();
    error PoolNotFound();
    error PoolAlreadyInactive();
    error PoolAlreadyActive();
    error AlreadyInitialized();
    error AmountTooLow(uint256 minAmount, uint256 actualAmount);
    error InsufficientStakedAmount(uint256 stakedAmount, uint256 requestedAmount);
    error InvalidUnlockPeriod(uint256 minPeriod, uint256 maxPeriod, uint256 actualPeriod);
    error UnlockRequestActive();
    error InvalidRequestId(uint256 requestId);
    error RequestAlreadyProcessed(uint256 requestId);
    error NoActiveUnlockRequest();
    error MissingRole(string requiredRole);
    error NoStakeFound();
    error InvalidAprRate(uint256 minRate, uint256 maxRate, uint256 actualRate);
    error InvalidStakingThresholdOrder(string description);
    error MinUnlockPeriodExceedsMax();
    error MaxUnlockPeriodTooLong(uint256 maxAllowed, uint256 actualPeriod);
    error PenaltyRateTooHigh(string rateName, uint256 maxRate, uint256 actualRate);
    error TooManyUnlockRequests(uint256 maxRequests);
    error InvalidMaxRequestsValue(uint256 minValue, uint256 maxValue, uint256 actualValue);
    error ClaimCooldownActive();

    error RequestExpired();
    error NonceAlreadyUsed();

    error ScenarioNotActive();
    error CombinedPaymentNotSupported();
    error InvalidPlanIndex();
    error InvalidPaymentCalculation();
    error InsufficientBalance();
    error ExceedsDiscountLimit();

    error NotDAOMember();
    error AlreadyDAOMember();
    error LockedProposal();
    error EmergencyModeActive();
    error EmergencyModeInactive();
    error NotInEmergencyMode();
    error ProposalNotFound();
    error ProposalCancelled();
    error ProposalAlreadyCancelled();
    error ProposalEnded();
    error ProposalExecuted();
    error VotingEnded();
    error VotingNotEnded();
    error AlreadyVoted();
    error VotingActive();
    error BelowThreshold();
    error MinParticipationNotReached();
    error InvalidVoteType();
    error ExecutionDelayNotMet();
    error ExecutionFailed();
    error VotingNotStarted();
    error InvalidProposalState();
    error InsufficientVotingPower();
    error CooldownPeriodNotElapsed();
    error MaxExpenditureLimitExceeded();
    error InsufficientQuorum();
    error NotProposalManager();
    error NotAuditor();
    error AuditorAlreadyExists();
    error AuditorNotFound();
    error NotProposer();
    error LockPeriodNotEnded();
    error NoActiveVote();

    error AlreadyRegistered();
    error NameAlreadyTaken();
    error NotRegistered();
    error InvalidArtistId();
    error InvalidArtistStatus();
    error CannotEndorseYourself();
    error AlreadyEndorsed();
    error InvalidExpertiseLevel();
    error InvalidExpertiseIndex();
    error SelfVerificationNotAllowed();
    error InvalidVerificationLevel();
    error InsufficientVerificationFee();
    error InsufficientRegistrationFee();
    error TooManyTags();

    error RoleAlreadyAssigned();
    error RoleNotAssigned();
    error RoleNotActive();
    error RoleAlreadyActive();
    error CannotUpgradeTwice();
    error InsufficientActiveDuration();
    error InsufficientReputationScore();
    error InsufficientPXTStaking();
    error PermissionAlreadyGranted();
    error PermissionNotFound();
    error InvalidRoleType();
    error InvalidRoleLevel();
    error InvalidUpgradeRequirement();
    error ContractPaused();
    error DivisionByZero();
    error ValueTooLarge();
    error InvalidSignature();
    error DuplicateSignature();

    // 内容相关错误
    error InvalidContentId();
    error InvalidContentType();
    error InvalidMetadata();
    error InvalidIPFSHash();
    error InvalidReviewers();
    error ContentAlreadyExists();
    error ContentLocked();
    error ContentNotActive();
    error InsufficientPayment();
    error ContentNotFound();
    error NotContentOwner();
    error ContentNotLocked();
    error InvalidMintPrice();
    error MintingNotAllowed();
    error ContentExpired();
    error InvalidContentStatus();

    // Factory相关错误
    error AlreadyDeployed();
    error SystemNotDeployed();
}