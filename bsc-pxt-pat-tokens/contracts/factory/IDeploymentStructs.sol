// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

/**
 * @title IDeploymentStructs
 * @dev 部署结构体接口 - 定义代币系统部署所需的地址结构
 * @notice 这个接口定义了部署PXT/PAT代币系统时需要的所有地址参数
 */
interface IDeploymentStructs {
    /**
     * @dev 部署地址结构体
     * @param communityAddress 社区地址 - 用于社区治理和奖励
     * @param teamAddress 团队地址 - 团队代币分配地址
     * @param platformFundAddress 平台基金地址 - 平台运营资金
     * @param privateSaleAddress 私募地址 - 私募投资者代币分配
     * @param strategicPartnerAddress 战略合作伙伴地址 - 战略投资者分配
     * @param marketingAddress 营销地址 - 营销推广资金
     * @param reserveAddress 储备地址 - 生态储备资金
     * @param chinaMainlandPool 中国大陆池地址 - 中国大陆用户专用池
     * @param globalPool 全球池地址 - 全球用户通用池
     * @param stakingRewardPool 质押奖励池地址 - 质押奖励分发池
     */
    struct DeploymentAddresses {
        address communityAddress;        // 社区地址
        address teamAddress;             // 团队地址
        address platformFundAddress;     // 平台基金地址
        address privateSaleAddress;      // 私募地址
        address strategicPartnerAddress; // 战略合作伙伴地址
        address marketingAddress;        // 营销地址
        address reserveAddress;          // 储备地址
        address chinaMainlandPool;       // 中国大陆池地址
        address globalPool;              // 全球池地址
        address stakingRewardPool;       // 质押奖励池地址
    }
}
