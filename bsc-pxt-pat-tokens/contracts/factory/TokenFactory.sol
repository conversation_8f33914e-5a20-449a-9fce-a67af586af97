// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/proxy/Clones.sol";
import "../interfaces/TokenErrors.sol";

/**
 * @title TokenFactory
 * @dev 代币工厂合约 - 使用克隆模式部署PXT/PAT代币系统
 * @notice 这个合约负责部署和管理整个代币生态系统
 * <AUTHOR> Team
 *
 * 功能特性:
 * - 使用OpenZeppelin Clones库实现低成本部署
 * - 支持一键部署完整的代币系统
 * - 提供推广质押池创建功能
 * - 完善的权限控制和安全检查
 * - 版本控制和部署状态跟踪
 */
contract TokenFactory is Ownable {
    
    struct DeploymentAddresses {
        address communityAddress;
        address teamAddress;
        address platformFundAddress;
        address privateSaleAddress;
        address strategicPartnerAddress;
        address marketingAddress;
        address reserveAddress;
        address chinaMainlandPool;
        address globalPool;
        address stakingRewardPool;
    }

    // 模板合约地址
    address public immutable pxtokenTemplate;
    address public immutable patokenTemplate;
    address public immutable stakingPoolTemplate;
    address public immutable miningPoolTemplate;
    
    // 部署的合约地址
    address public pxtoken;
    address public patoken;
    address public stakingPool;
    address public miningPool;
    bool public isDeployed;

    // 🔒 安全优化：添加部署时间戳和版本控制
    uint256 public deploymentTimestamp;
    uint256 public constant FACTORY_VERSION = 1;

    event SystemDeployed(
        address indexed pxtoken,
        address indexed patoken,
        address indexed stakingPool,
        address miningPool
    );

    constructor(
        address _pxtokenTemplate,
        address _patokenTemplate,
        address _stakingPoolTemplate,
        address _miningPoolTemplate
    ) {
        // 🔒 安全优化：添加零地址检查
        if (_pxtokenTemplate == address(0)) revert TokenErrors.ZeroAddress();
        if (_patokenTemplate == address(0)) revert TokenErrors.ZeroAddress();
        if (_stakingPoolTemplate == address(0)) revert TokenErrors.ZeroAddress();
        if (_miningPoolTemplate == address(0)) revert TokenErrors.ZeroAddress();

        pxtokenTemplate = _pxtokenTemplate;
        patokenTemplate = _patokenTemplate;
        stakingPoolTemplate = _stakingPoolTemplate;
        miningPoolTemplate = _miningPoolTemplate;
    }

    function deployTokenSystem(
        DeploymentAddresses calldata addresses,
        uint256 baseAPR
    ) external onlyOwner {
        // 🔒 安全优化：使用自定义错误替代require
        if (isDeployed) revert TokenErrors.AlreadyDeployed();
        if (baseAPR < 100 || baseAPR > 5000) revert TokenErrors.InvalidParameter();
        
        _validateAddresses(addresses);

        // 使用克隆模式部署合约
        pxtoken = Clones.clone(pxtokenTemplate);
        patoken = Clones.clone(patokenTemplate);
        stakingPool = Clones.clone(stakingPoolTemplate);
        miningPool = Clones.clone(miningPoolTemplate);

        // 初始化合约（需要在模板合约中实现initialize函数）
        _initializeContracts(addresses, baseAPR);

        // 🔒 安全优化：记录部署状态和时间戳
        isDeployed = true;
        deploymentTimestamp = block.timestamp;
        emit SystemDeployed(pxtoken, patoken, stakingPool, miningPool);
    }

    function _validateAddresses(DeploymentAddresses calldata addresses) private pure {
        // 🔒 安全优化：使用自定义错误替代require，提高Gas效率
        if (addresses.communityAddress == address(0)) revert TokenErrors.ZeroAddress();
        if (addresses.teamAddress == address(0)) revert TokenErrors.ZeroAddress();
        if (addresses.platformFundAddress == address(0)) revert TokenErrors.ZeroAddress();
        if (addresses.privateSaleAddress == address(0)) revert TokenErrors.ZeroAddress();
        if (addresses.strategicPartnerAddress == address(0)) revert TokenErrors.ZeroAddress();
        if (addresses.marketingAddress == address(0)) revert TokenErrors.ZeroAddress();
        if (addresses.reserveAddress == address(0)) revert TokenErrors.ZeroAddress();
        if (addresses.chinaMainlandPool == address(0)) revert TokenErrors.ZeroAddress();
        if (addresses.globalPool == address(0)) revert TokenErrors.ZeroAddress();
        if (addresses.stakingRewardPool == address(0)) revert TokenErrors.ZeroAddress();
    }

    function _initializeContracts(
        DeploymentAddresses calldata addresses,
        uint256 baseAPR
    ) private {
        // 这里需要调用各个合约的initialize函数
        // 具体实现取决于各个合约的接口
        
        // 示例：
        // IPXToken(pxtoken).initialize(...);
        // IPAToken(patoken).initialize(...);
        // IStakingPool(stakingPool).initialize(pxtoken, patoken, baseAPR);
        // IMiningPool(miningPool).initialize(...);
        
        // 为了简化，这里暂时留空
        // 实际使用时需要根据具体的初始化接口来实现
    }

    function getSystemAddresses() external view returns (
        address _pxtoken,
        address _patoken,
        address _stakingPool,
        address _miningPool
    ) {
        return (pxtoken, patoken, stakingPool, miningPool);
    }

    function createPromotionalStakingPool(uint256 baseAPR) external onlyOwner returns (address) {
        // 🔒 安全优化：使用自定义错误替代require
        if (!isDeployed) revert TokenErrors.SystemNotDeployed();
        if (baseAPR < 100 || baseAPR > 5000) revert TokenErrors.InvalidParameter();
        
        address promotionalPool = Clones.clone(stakingPoolTemplate);
        // 初始化推广池
        // IStakingPool(promotionalPool).initialize(pxtoken, patoken, baseAPR);
        
        return promotionalPool;
    }

    // 🔒 安全优化：添加实用的查询函数

    /**
     * @dev 获取工厂合约的详细信息
     */
    function getFactoryInfo() external view returns (
        uint256 version,
        bool deployed,
        uint256 timestamp,
        address factoryOwner
    ) {
        return (
            FACTORY_VERSION,
            isDeployed,
            deploymentTimestamp,
            owner()
        );
    }

    /**
     * @dev 检查模板合约地址是否有效
     */
    function validateTemplates() external view returns (bool) {
        return (
            pxtokenTemplate != address(0) &&
            patokenTemplate != address(0) &&
            stakingPoolTemplate != address(0) &&
            miningPoolTemplate != address(0)
        );
    }

    /**
     * @dev 获取所有模板合约地址
     */
    function getTemplateAddresses() external view returns (
        address _pxtokenTemplate,
        address _patokenTemplate,
        address _stakingPoolTemplate,
        address _miningPoolTemplate
    ) {
        return (
            pxtokenTemplate,
            patokenTemplate,
            stakingPoolTemplate,
            miningPoolTemplate
        );
    }
}
