// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

contract LiquidityMining is
    Ownable,
    ReentrancyGuard,
    Pausable
{
    using SafeMath for uint256;

    IPXT public immutable pxtoken;
    IPAT public immutable patoken;
    mapping(address => uint256) public lpTokenToPoolId;

    struct Pool {
        address lpToken;
        address rewardToken;
        uint128 rewardPerBlock;
        uint128 totalStaked;
        uint128 accRewardPerShare;
        uint32 lastRewardBlock;
        uint32 startBlock;
        uint32 endBlock;
        uint32 bonusEndBlock;
        uint16 bonusMultiplier;
        bool isActive;
    }

    struct UserInfo {
        uint128 amount;
        uint128 rewardDebt;
        uint128 pendingRewards;
        uint32 lastHarvestBlock;
    }

    Pool[] public pools;
    mapping(uint256 => mapping(address => UserInfo)) public userInfo;

    struct MarketMakerTier {
        uint128 minLiquidity;
        uint40 minDuration;
        uint16 rewardBoost;
    }

    MarketMakerTier[] public marketMakerTiers;
    mapping(address => uint8) public userMarketMakerTier;
    mapping(address => uint40) public userFirstStakeTime;

    event PoolAdded(
        uint256 indexed poolId,
        address indexed lpToken,
        address indexed rewardToken,
        uint128 rewardPerBlock
    );
    event PoolUpdated(
        uint256 indexed poolId,
        uint128 rewardPerBlock,
        uint32 endBlock
    );
    event Staked(
        uint256 indexed poolId,
        address indexed user,
        uint128 amount
    );
    event Unstaked(
        uint256 indexed poolId,
        address indexed user,
        uint128 amount
    );
    event RewardPaid(
        uint256 indexed poolId,
        address indexed user,
        address indexed rewardToken,
        uint128 amount
    );
    event MarketMakerTierAdded(
        uint8 indexed tierId,
        uint128 minLiquidity,
        uint40 minDuration,
        uint16 rewardBoost
    );
    event UserTierUpdated(
        address indexed user,
        uint8 oldTier,
        uint8 newTier
    );

    constructor(
        address _pxtoken,
        address _patoken
    ) {
        pxtoken = IPXT(_pxtoken);
        patoken = IPAT(_patoken);
        marketMakerTiers.push(MarketMakerTier({
            minLiquidity: 0,
            minDuration: 0,
            rewardBoost: 10000
        }));
        marketMakerTiers.push(MarketMakerTier({
            minLiquidity: 1000 * 10**18,
            minDuration: 7 days,
            rewardBoost: 11000
        }));
        marketMakerTiers.push(MarketMakerTier({
            minLiquidity: 5000 * 10**18,
            minDuration: 14 days,
            rewardBoost: 12500
        }));
        marketMakerTiers.push(MarketMakerTier({
            minLiquidity: 10000 * 10**18,
            minDuration: 30 days,
            rewardBoost: 15000
        }));
    }

    function addPool(
        address _lpToken,
        address _rewardToken,
        uint128 _rewardPerBlock,
        uint32 _startBlock,
        uint32 _endBlock,
        uint32 _bonusEndBlock,
        uint16 _bonusMultiplier
    ) external onlyOwner returns (uint256) {
        if (_lpToken == address(0)) revert TokenErrors.ZeroAddress();
        if (_rewardToken == address(0)) revert TokenErrors.ZeroAddress();
        if (lpTokenToPoolId[_lpToken] != 0) revert TokenErrors.PoolAlreadyExists();
        if (_startBlock >= _endBlock) revert TokenErrors.InvalidDates();
        if (_bonusEndBlock > _endBlock) revert TokenErrors.InvalidDates();

        pools.push(Pool({
            lpToken: _lpToken,
            rewardToken: _rewardToken,
            rewardPerBlock: _rewardPerBlock,
            totalStaked: 0,
            accRewardPerShare: 0,
            lastRewardBlock: _startBlock,
            startBlock: _startBlock,
            endBlock: _endBlock,
            bonusEndBlock: _bonusEndBlock,
            bonusMultiplier: _bonusMultiplier,
            isActive: true
        }));

        uint256 poolId = pools.length;
        lpTokenToPoolId[_lpToken] = poolId;

        emit PoolAdded(poolId - 1, _lpToken, _rewardToken, _rewardPerBlock);

        return poolId - 1;
    }
    
    function updatePool(
        uint256 _poolId,
        uint128 _rewardPerBlock,
        uint32 _endBlock,
        uint16 _bonusMultiplier,
        bool _isActive
    ) external onlyOwner {
        if (_poolId >= pools.length) revert TokenErrors.IndexOutOfRange();

        Pool storage pool = pools[_poolId];

        updatePoolReward(_poolId);

        pool.rewardPerBlock = _rewardPerBlock;

        if (_endBlock > block.number) {
            pool.endBlock = _endBlock;
        }

        pool.bonusMultiplier = _bonusMultiplier;
        pool.isActive = _isActive;

        emit PoolUpdated(_poolId, _rewardPerBlock, _endBlock);
    }
    
    function updatePoolReward(uint256 _poolId) public {
        if (_poolId >= pools.length) revert TokenErrors.IndexOutOfRange();

        Pool storage pool = pools[_poolId];

        if (block.number <= pool.lastRewardBlock) {
            return;
        }

        if (pool.totalStaked == 0) {
            pool.lastRewardBlock = uint32(block.number);
            return;
        }

        uint256 endBlock = block.number;
        if (endBlock > pool.endBlock) {
            endBlock = pool.endBlock;
        }

        if (endBlock <= pool.lastRewardBlock) {
            return;
        }

        uint256 blocksPassed = endBlock - pool.lastRewardBlock;
        uint256 reward = blocksPassed * pool.rewardPerBlock;

        if (pool.lastRewardBlock < pool.bonusEndBlock) {
            uint256 bonusBlocks;
            if (endBlock < pool.bonusEndBlock) {
                bonusBlocks = blocksPassed;
            } else {
                bonusBlocks = pool.bonusEndBlock - pool.lastRewardBlock;
            }

            uint256 regularBlocks = blocksPassed - bonusBlocks;

            reward = bonusBlocks * pool.rewardPerBlock * pool.bonusMultiplier / 10000
                  + (regularBlocks * pool.rewardPerBlock);
        }

        uint256 rewardPerShare = reward * 1e18 / pool.totalStaked;
        pool.accRewardPerShare = pool.accRewardPerShare + uint128(rewardPerShare);
        pool.lastRewardBlock = uint32(endBlock);
    }
    
    function stake(uint256 _poolId, uint128 _amount) external whenNotPaused nonReentrant {
        if (_poolId >= pools.length) revert TokenErrors.IndexOutOfRange();
        if (_amount == 0) revert TokenErrors.ZeroAmount();

        Pool storage pool = pools[_poolId];
        if (!pool.isActive) revert TokenErrors.PoolInactive();
        if (block.number >= pool.endBlock) revert TokenErrors.MiningEnded();

        UserInfo storage user = userInfo[_poolId][_msgSender()];

        updatePoolReward(_poolId);

        if (user.amount > 0) {
            uint256 pending = uint256(user.amount) * pool.accRewardPerShare / 1e18 - user.rewardDebt;
            if (pending > 0) {
                user.pendingRewards = user.pendingRewards + uint128(pending);
            }
        } else if (userFirstStakeTime[_msgSender()] == 0) {
            userFirstStakeTime[_msgSender()] = uint40(block.timestamp);
        }

        IERC20(pool.lpToken).transferFrom(_msgSender(), address(this), _amount);

        user.amount = user.amount + _amount;
        user.rewardDebt = uint128(uint256(user.amount) * pool.accRewardPerShare / 1e18);

        pool.totalStaked = pool.totalStaked + _amount;

        updateUserTier(_msgSender());

        emit Staked(_poolId, _msgSender(), _amount);
    }
    
    function unstake(uint256 _poolId, uint128 _amount) external nonReentrant {
        if (_poolId >= pools.length) revert TokenErrors.IndexOutOfRange();

        Pool storage pool = pools[_poolId];
        UserInfo storage user = userInfo[_poolId][_msgSender()];

        if (user.amount < _amount) revert TokenErrors.InsufficientStakingAmount();

        updatePoolReward(_poolId);

        uint256 pending = uint256(user.amount) * pool.accRewardPerShare / 1e18 - user.rewardDebt;
        if (pending > 0) {
            user.pendingRewards = user.pendingRewards + uint128(pending);
        }

        user.amount = user.amount - _amount;
        user.rewardDebt = uint128(uint256(user.amount) * pool.accRewardPerShare / 1e18);

        pool.totalStaked = pool.totalStaked - _amount;

        IERC20(pool.lpToken).transfer(_msgSender(), _amount);

        updateUserTier(_msgSender());

        emit Unstaked(_poolId, _msgSender(), _amount);
    }
    
    function harvest(uint256 _poolId) external nonReentrant {
        if (_poolId >= pools.length) revert TokenErrors.IndexOutOfRange();

        Pool storage pool = pools[_poolId];
        UserInfo storage user = userInfo[_poolId][_msgSender()];

        updatePoolReward(_poolId);

        uint256 pending = uint256(user.amount) * pool.accRewardPerShare / 1e18 - user.rewardDebt;
        uint256 totalRewards = user.pendingRewards + pending;

        if (totalRewards > 0) {
            uint8 tierId = userMarketMakerTier[_msgSender()];
            uint16 boostMultiplier = marketMakerTiers[tierId].rewardBoost;
            uint256 boostedRewards = totalRewards * boostMultiplier / 10000;

            if (pool.rewardToken == address(pxtoken)) {
                pxtoken.transfer(_msgSender(), boostedRewards);
            } else if (pool.rewardToken == address(patoken)) {
                patoken.transfer(_msgSender(), boostedRewards);
            } else {
                IERC20(pool.rewardToken).transfer(_msgSender(), boostedRewards);
            }

            user.pendingRewards = 0;
            user.lastHarvestBlock = uint32(block.number);

            emit RewardPaid(_poolId, _msgSender(), pool.rewardToken, uint128(boostedRewards));
        } else {
            revert TokenErrors.NoRewardToClaim();
        }

        user.rewardDebt = uint128(uint256(user.amount) * pool.accRewardPerShare / 1e18);
    }
    
    function pendingReward(uint256 _poolId, address _user) external view returns (uint256) {
        if (_poolId >= pools.length) revert TokenErrors.IndexOutOfRange();

        Pool memory pool = pools[_poolId];
        UserInfo memory user = userInfo[_poolId][_user];

        uint256 accRewardPerShare = pool.accRewardPerShare;

        if (block.number > pool.lastRewardBlock && pool.totalStaked > 0) {
            uint256 endBlock = block.number;
            if (endBlock > pool.endBlock) {
                endBlock = pool.endBlock;
            }

            if (endBlock > pool.lastRewardBlock) {
                uint256 blocksPassed = endBlock - pool.lastRewardBlock;
                uint256 reward = blocksPassed * pool.rewardPerBlock;

                if (pool.lastRewardBlock < pool.bonusEndBlock) {
                    uint256 bonusBlocks;
                    if (endBlock < pool.bonusEndBlock) {
                        bonusBlocks = blocksPassed;
                    } else {
                        bonusBlocks = pool.bonusEndBlock - pool.lastRewardBlock;
                    }

                    uint256 regularBlocks = blocksPassed - bonusBlocks;

                    reward = bonusBlocks * pool.rewardPerBlock * pool.bonusMultiplier / 10000
                          + (regularBlocks * pool.rewardPerBlock);
                }

                accRewardPerShare = accRewardPerShare + (reward * 1e18 / pool.totalStaked);
            }
        }

        uint256 pending = uint256(user.amount) * accRewardPerShare / 1e18 - user.rewardDebt;
        uint256 totalRewards = user.pendingRewards + pending;

        uint8 tierId = userMarketMakerTier[_user];
        uint16 boostMultiplier = marketMakerTiers[tierId].rewardBoost;

        return totalRewards * boostMultiplier / 10000;
    }
    
    function addMarketMakerTier(
        uint128 _minLiquidity,
        uint40 _minDuration,
        uint16 _rewardBoost
    ) external onlyOwner returns (uint8) {
        if (_rewardBoost == 0) revert TokenErrors.InvalidRewardBoost();

        marketMakerTiers.push(MarketMakerTier({
            minLiquidity: _minLiquidity,
            minDuration: _minDuration,
            rewardBoost: _rewardBoost
        }));

        uint8 tierId = uint8(marketMakerTiers.length - 1);

        emit MarketMakerTierAdded(tierId, _minLiquidity, _minDuration, _rewardBoost);

        return tierId;
    }

    function updateUserTier(address _user) public {
        uint256 totalLiquidity = 0;
        uint256 poolsLength = pools.length;

        // 🔒 安全优化：使用unchecked进行流动性累加
        for (uint256 i = 0; i < poolsLength;) {
            UserInfo storage userPool = userInfo[i][_user];
            unchecked {
                totalLiquidity += userPool.amount;
                i++;
            }
        }

        uint256 stakeDuration = 0;
        if (userFirstStakeTime[_user] > 0) {
            stakeDuration = block.timestamp - userFirstStakeTime[_user];
        }

        uint8 currentTier = 0;
        uint256 tiersLength = marketMakerTiers.length;

        for (uint256 i = tiersLength - 1; i > 0;) {
            if (totalLiquidity >= marketMakerTiers[i].minLiquidity &&
                stakeDuration >= marketMakerTiers[i].minDuration) {
                currentTier = uint8(i);
                break;
            }
            unchecked { i--; }
        }

        if (currentTier != userMarketMakerTier[_user]) {
            uint8 oldTier = userMarketMakerTier[_user];
            userMarketMakerTier[_user] = currentTier;

            emit UserTierUpdated(_user, oldTier, currentTier);
        }
    }
    
    function setUserTier(address _user, uint8 _tierId) external onlyOwner {
        if (_user == address(0)) revert TokenErrors.ZeroAddress();
        if (_tierId >= marketMakerTiers.length) revert TokenErrors.IndexOutOfRange();

        uint8 oldTier = userMarketMakerTier[_user];
        userMarketMakerTier[_user] = _tierId;

        emit UserTierUpdated(_user, oldTier, _tierId);
    }

    function massUpdatePools(uint256[] calldata _poolIds) external {
        uint256 poolIdsLength = _poolIds.length;
        for (uint256 i = 0; i < poolIdsLength;) {
            updatePoolReward(_poolIds[i]);
            unchecked { i++; }
        }
    }

    function emergencyWithdraw(uint256 _poolId) external nonReentrant {
        if (_poolId >= pools.length) revert TokenErrors.IndexOutOfRange();

        Pool storage pool = pools[_poolId];
        UserInfo storage user = userInfo[_poolId][_msgSender()];

        uint128 amount = user.amount;

        user.amount = 0;
        user.rewardDebt = 0;
        user.pendingRewards = 0;

        pool.totalStaked = pool.totalStaked - amount;

        IERC20(pool.lpToken).transfer(_msgSender(), amount);

        emit Unstaked(_poolId, _msgSender(), amount);
    }

    function emergencyRewardWithdraw(
        address _token,
        uint256 _amount,
        address _to
    ) external onlyOwner {
        if (_to == address(0)) revert TokenErrors.ZeroAddress();

        IERC20(_token).transfer(_to, _amount);
    }

    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    function getPoolsLength() external view returns (uint256) {
        return pools.length;
    }

    function getMarketMakerTiersLength() external view returns (uint256) {
        return marketMakerTiers.length;
    }

    function getPoolsPaged(uint256 offset, uint256 limit) external view returns (Pool[] memory) {
        uint256 total = pools.length;
        if (offset >= total) return new Pool[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        Pool[] memory result = new Pool[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = pools[i];
            unchecked { i++; }
        }
        return result;
    }

    function getMarketMakerTiersPaged(uint256 offset, uint256 limit) external view returns (MarketMakerTier[] memory) {
        uint256 total = marketMakerTiers.length;
        if (offset >= total) return new MarketMakerTier[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        MarketMakerTier[] memory result = new MarketMakerTier[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = marketMakerTiers[i];
            unchecked { i++; }
        }
        return result;
    }
}