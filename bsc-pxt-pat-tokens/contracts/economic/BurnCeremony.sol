// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/IPXT.sol";

contract BurnCeremony is
    Ownable,
    ReentrancyGuard,
    Pausable
{
    IPAT public immutable patoken;
    IPXT public immutable pxtoken;
    address public immutable burnManager;
    uint256 public constant MAX_RANKING_SIZE = 1000;

    struct Ceremony {
        uint32 id;
        string name;
        string description;
        uint40 startTime;
        uint40 endTime;
        uint128 targetAmount;
        uint128 currentAmount;
        address tokenAddress;
        bool isActive;
        bool isCompleted;
        address[] participants;
        uint32 participantCount;
    }

    struct Participant {
        address user;
        uint128 amount;
        uint40 timestamp;
        string message;
    }

    struct BurnRecord {
        uint32 ceremonyId;
        address user;
        uint128 amount;
        uint40 timestamp;
        string message;
        bytes32 transactionHash;
        bool verified;
    }

    Ceremony[] public ceremonies;
    mapping(uint256 => mapping(address => Participant)) public ceremoniesParticipants;
    mapping(uint256 => BurnRecord[]) public ceremonyBurnRecords;

    struct RankingEntry {
        address user;
        uint128 totalBurned;
        uint40 lastBurnTime;
        uint32 burnCount;
    }

    mapping(address => RankingEntry) public userRankings;
    address[] public rankingAddresses;
    mapping(address => uint256) private userRankIndex;

    struct IncentiveConfig {
        bool enableRewards;
        uint16 rewardPercentage;
        uint128 minBurnAmount;
        address rewardToken;
    }

    IncentiveConfig public incentiveConfig;
    address public constant DEAD_ADDRESS = 0x000000000000000000000000000000000000dEaD;

    event CeremonyCreated(
        uint256 indexed ceremonyId,
        string name,
        uint256 startTime,
        uint256 endTime,
        uint256 targetAmount
    );
    event CeremonyCompleted(uint256 indexed ceremonyId, uint256 totalBurned, uint256 participantCount);
    event TokensBurned(
        uint256 indexed ceremonyId,
        address indexed user,
        uint256 amount,
        string message
    );
    event RankingUpdated(address indexed user, uint256 totalBurned, uint256 newRank);
    event RewardDistributed(address indexed user, uint256 amount);
    event IncentiveConfigUpdated(
        bool enableRewards,
        uint256 rewardPercentage,
        uint256 minBurnAmount,
        address rewardToken
    );
    event ContractPaused(address indexed operator);
    event ContractUnpaused(address indexed operator);

    constructor(
        address _patoken,
        address _pxtoken,
        address _burnManager
    ) {
        require(_patoken != address(0), "BurnCeremony: PAT token address cannot be zero");
        require(_pxtoken != address(0), "BurnCeremony: PXT token address cannot be zero");
        require(_burnManager != address(0), "BurnCeremony: Burn manager address cannot be zero");
        patoken = IPAT(_patoken);
        pxtoken = IPXT(_pxtoken);
        burnManager = _burnManager;
        incentiveConfig = IncentiveConfig({
            enableRewards: true,
            rewardPercentage: 500,
            minBurnAmount: 1000 * 10**18,
            rewardToken: _patoken
        });
    }

    function createCeremony(
        string memory _name,
        string memory _description,
        uint256 _startTime,
        uint256 _duration,
        uint256 _targetAmount,
        address _tokenAddress
    ) external onlyOwner whenNotPaused returns (uint256) {
        require(_startTime >= block.timestamp, "BurnCeremony: Start time must be in the future");
        require(_duration > 0, "BurnCeremony: Duration must be greater than 0");
        require(_targetAmount > 0, "BurnCeremony: Target amount must be greater than 0");
        require(
            _tokenAddress == address(patoken) || _tokenAddress == address(pxtoken),
            "BurnCeremony: Unsupported token"
        );
        require(bytes(_name).length > 0, "BurnCeremony: Name cannot be empty");

        if (_startTime > type(uint40).max || _startTime + _duration > type(uint40).max)
            revert("BurnCeremony: Time value out of range");
        if (_targetAmount > type(uint128).max)
            revert("BurnCeremony: Target amount too large");
        if (ceremonies.length >= type(uint32).max)
            revert("BurnCeremony: Too many ceremonies");

        uint256 endTime = _startTime + _duration;

        address[] memory emptyArray = new address[](0);

        ceremonies.push(Ceremony({
            id: uint32(ceremonies.length),
            name: _name,
            description: _description,
            startTime: uint40(_startTime),
            endTime: uint40(endTime),
            targetAmount: uint128(_targetAmount),
            currentAmount: 0,
            tokenAddress: _tokenAddress,
            isActive: true,
            isCompleted: false,
            participants: emptyArray,
            participantCount: 0
        }));

        uint256 ceremonyId = ceremonies.length - 1;

        emit CeremonyCreated(
            ceremonyId,
            _name,
            _startTime,
            endTime,
            _targetAmount
        );

        return ceremonyId;
    }
    
    function participateInCeremony(
        uint256 _ceremonyId,
        uint256 _amount,
        string memory _message
    ) external nonReentrant whenNotPaused {
        require(_ceremonyId < ceremonies.length, "BurnCeremony: Invalid ceremony ID");

        Ceremony storage ceremony = ceremonies[_ceremonyId];

        require(ceremony.isActive, "BurnCeremony: Ceremony is not active");
        require(!ceremony.isCompleted, "BurnCeremony: Ceremony is already completed");
        require(block.timestamp >= ceremony.startTime, "BurnCeremony: Ceremony has not started yet");
        require(block.timestamp <= ceremony.endTime, "BurnCeremony: Ceremony has ended");
        require(_amount > 0, "BurnCeremony: Burn amount must be greater than 0");

        if (_amount > type(uint128).max)
            revert("BurnCeremony: Amount too large");

        uint256 newTotalAmount = uint256(ceremony.currentAmount) + _amount;
        if (newTotalAmount > type(uint128).max)
            revert("BurnCeremony: Total amount would overflow");

        IERC20 token = IERC20(ceremony.tokenAddress);

        require(token.balanceOf(_msgSender()) >= _amount, "BurnCeremony: Insufficient balance");
        require(token.allowance(_msgSender(), address(this)) >= _amount, "BurnCeremony: Insufficient allowance");

        ceremony.currentAmount = uint128(newTotalAmount);

        if (ceremoniesParticipants[_ceremonyId][_msgSender()].amount == 0) {
            if (uint256(ceremony.participantCount) + 1 > type(uint32).max)
                revert("BurnCeremony: Too many participants");

            unchecked {
                ceremony.participantCount++;
            }

            ceremoniesParticipants[_ceremonyId][_msgSender()] = Participant({
                user: _msgSender(),
                amount: uint128(_amount),
                timestamp: uint40(block.timestamp),
                message: _message
            });
        } else {
            uint256 newAmount = uint256(ceremoniesParticipants[_ceremonyId][_msgSender()].amount) + _amount;
            if (newAmount > type(uint128).max)
                revert("BurnCeremony: User amount would overflow");

            ceremoniesParticipants[_ceremonyId][_msgSender()].amount = uint128(newAmount);
            ceremoniesParticipants[_ceremonyId][_msgSender()].timestamp = uint40(block.timestamp);

            if (bytes(_message).length > 0) {
                ceremoniesParticipants[_ceremonyId][_msgSender()].message = _message;
            }
        }

        ceremonyBurnRecords[_ceremonyId].push(BurnRecord({
            ceremonyId: uint32(_ceremonyId),
            user: _msgSender(),
            amount: uint128(_amount),
            timestamp: uint40(block.timestamp),
            message: _message,
            transactionHash: blockhash(block.number - 1),
            verified: true
        }));

        updateUserRanking(_msgSender(), _amount);

        if (ceremony.currentAmount >= ceremony.targetAmount) {
            completeCeremony(_ceremonyId);
        }

        token.transferFrom(_msgSender(), DEAD_ADDRESS, _amount);

        if (
            incentiveConfig.enableRewards &&
            _amount >= incentiveConfig.minBurnAmount
        ) {
            distributeReward(_msgSender(), _amount);
        }

        emit TokensBurned(_ceremonyId, _msgSender(), _amount, _message);
    }

    function completeCeremony(uint256 _ceremonyId) public whenNotPaused {
        require(_ceremonyId < ceremonies.length, "BurnCeremony: Invalid ceremony ID");

        Ceremony storage ceremony = ceremonies[_ceremonyId];

        require(ceremony.isActive, "BurnCeremony: Ceremony is not active");
        require(!ceremony.isCompleted, "BurnCeremony: Ceremony is already completed");

        require(
            ceremony.currentAmount >= ceremony.targetAmount || block.timestamp > ceremony.endTime,
            "BurnCeremony: Completion conditions not met"
        );

        ceremony.isCompleted = true;

        emit CeremonyCompleted(_ceremonyId, ceremony.currentAmount, ceremony.participantCount);
    }

    function updateUserRanking(address _user, uint256 _amount) internal {
        if (_amount > type(uint128).max)
            revert("BurnCeremony: Amount too large for ranking");

        if (userRankings[_user].totalBurned == 0) {
            userRankings[_user] = RankingEntry({
                user: _user,
                totalBurned: uint128(_amount),
                lastBurnTime: uint40(block.timestamp),
                burnCount: 1
            });

            if (rankingAddresses.length < MAX_RANKING_SIZE) {
                rankingAddresses.push(_user);
                userRankIndex[_user] = rankingAddresses.length - 1;
            } else {
                address lastUser = rankingAddresses[rankingAddresses.length - 1];
                if (userRankings[lastUser].totalBurned < _amount) {
                    delete userRankIndex[lastUser];
                    rankingAddresses[rankingAddresses.length - 1] = _user;
                    userRankIndex[_user] = rankingAddresses.length - 1;
                }
            }
        } else {
            uint256 newTotal = uint256(userRankings[_user].totalBurned) + _amount;
            if (newTotal > type(uint128).max)
                revert("BurnCeremony: User total burn would overflow");

            uint256 newCount = uint256(userRankings[_user].burnCount) + 1;
            if (newCount > type(uint32).max)
                revert("BurnCeremony: User burn count would overflow");

            userRankings[_user].totalBurned = uint128(newTotal);
            userRankings[_user].lastBurnTime = uint40(block.timestamp);
            userRankings[_user].burnCount = uint32(newCount);
        }

        uint256 userIndex = userRankIndex[_user];

        if (userIndex < rankingAddresses.length) {
            uint256 i = userIndex;

            while (i > 0 &&
                  userRankings[rankingAddresses[i]].totalBurned > userRankings[rankingAddresses[i-1]].totalBurned) {
                address temp = rankingAddresses[i-1];
                rankingAddresses[i-1] = rankingAddresses[i];
                rankingAddresses[i] = temp;

                userRankIndex[rankingAddresses[i-1]] = i-1;
                userRankIndex[rankingAddresses[i]] = i;

                i--;
            }
        }

        uint256 newRank = 0;
        for (uint256 i = 0; i < rankingAddresses.length;) {
            if (rankingAddresses[i] == _user) {
                newRank = i + 1;
                break;
            }
            unchecked { i++; }
        }

        emit RankingUpdated(_user, userRankings[_user].totalBurned, newRank);
    }

    function distributeReward(address _user, uint256 _burnAmount) internal {
        if (!incentiveConfig.enableRewards) {
            return;
        }

        // 🔒 安全优化：防止溢出的奖励计算
        uint256 rewardAmount;
        unchecked {
            rewardAmount = (_burnAmount * incentiveConfig.rewardPercentage) / 10000;
        }

        if (rewardAmount > 0) {
            IERC20 token = IERC20(incentiveConfig.rewardToken);

            uint256 contractBalance = token.balanceOf(address(this));
            if (contractBalance >= rewardAmount) {
                bool success = token.transfer(_user, rewardAmount);
                require(success, "BurnCeremony: Reward transfer failed");

                emit RewardDistributed(_user, rewardAmount);
            }
        }
    }

    function setCeremonyStatus(uint256 _ceremonyId, bool _isActive) external onlyOwner {
        require(_ceremonyId < ceremonies.length, "BurnCeremony: Invalid ceremony ID");

        ceremonies[_ceremonyId].isActive = _isActive;
    }

    function updateIncentiveConfig(
        bool _enableRewards,
        uint256 _rewardPercentage,
        uint256 _minBurnAmount,
        address _rewardToken
    ) external onlyOwner {
        require(_rewardPercentage <= 10000, "BurnCeremony: Reward percentage exceeds range");
        require(_rewardToken != address(0), "BurnCeremony: Reward token address cannot be zero");

        if (_rewardPercentage > type(uint16).max)
            revert("BurnCeremony: Reward percentage too large");
        if (_minBurnAmount > type(uint128).max)
            revert("BurnCeremony: Min burn amount too large");

        incentiveConfig.enableRewards = _enableRewards;
        incentiveConfig.rewardPercentage = uint16(_rewardPercentage);
        incentiveConfig.minBurnAmount = uint128(_minBurnAmount);
        incentiveConfig.rewardToken = _rewardToken;

        emit IncentiveConfigUpdated(
            _enableRewards,
            _rewardPercentage,
            _minBurnAmount,
            _rewardToken
        );
    }

    function recoverToken(
        address _token,
        uint256 _amount,
        address _to
    ) external onlyOwner nonReentrant {
        require(_to != address(0), "BurnCeremony: Recipient address cannot be zero");
        require(_token != address(0), "BurnCeremony: Token address cannot be zero");

        IERC20 token = IERC20(_token);
        bool success = token.transfer(_to, _amount);
        require(success, "BurnCeremony: Token transfer failed");
    }

    function pause() external onlyOwner {
        _pause();
        emit ContractPaused(msg.sender);
    }

    function unpause() external onlyOwner {
        _unpause();
        emit ContractUnpaused(msg.sender);
    }

    function getCeremoniesCount() external view returns (uint256) {
        return ceremonies.length;
    }

    function getCeremonyParticipantsCount(uint256 _ceremonyId) external view returns (uint256) {
        require(_ceremonyId < ceremonies.length, "BurnCeremony: Invalid ceremony ID");

        return ceremonies[_ceremonyId].participantCount;
    }

    function getCeremonyBurnRecordsCount(uint256 _ceremonyId) external view returns (uint256) {
        return ceremonyBurnRecords[_ceremonyId].length;
    }

    function getBurnRanking(uint256 _start, uint256 _count) external view returns (
        address[] memory users,
        uint256[] memory amounts,
        uint256[] memory ranks
    ) {
        require(_start < rankingAddresses.length, "BurnCeremony: Start index out of range");

        uint256 endIndex = _start + _count;
        if (endIndex > rankingAddresses.length) {
            endIndex = rankingAddresses.length;
        }

        uint256 resultCount = endIndex - _start;

        users = new address[](resultCount);
        amounts = new uint256[](resultCount);
        ranks = new uint256[](resultCount);

        for (uint256 i = 0; i < resultCount;) {
            uint256 index = _start + i;
            users[i] = rankingAddresses[index];
            amounts[i] = userRankings[rankingAddresses[index]].totalBurned;
            ranks[i] = index + 1;

            unchecked { i++; }
        }

        return (users, amounts, ranks);
    }

    function getUserRanking(address _user) external view returns (
        uint256 rank,
        uint256 totalBurned,
        uint256 burnCount
    ) {
        if (userRankings[_user].totalBurned == 0) {
            return (0, 0, 0);
        }

        uint256 index = userRankIndex[_user];
        if (index < rankingAddresses.length && rankingAddresses[index] == _user) {
            rank = index + 1;
        } else {
            for (uint256 i = 0; i < rankingAddresses.length;) {
                if (rankingAddresses[i] == _user) {
                    rank = i + 1;
                    break;
                }
                unchecked { i++; }
            }
        }

        totalBurned = userRankings[_user].totalBurned;
        burnCount = userRankings[_user].burnCount;

        return (rank, totalBurned, burnCount);
    }

    function getActiveCeremonies() external view returns (uint256[] memory) {
        uint256 activeCount = 0;

        for (uint256 i = 0; i < ceremonies.length;) {
            if (
                ceremonies[i].isActive &&
                !ceremonies[i].isCompleted &&
                block.timestamp >= ceremonies[i].startTime &&
                block.timestamp <= ceremonies[i].endTime
            ) {
                activeCount++;
            }
            unchecked { i++; }
        }

        uint256[] memory result = new uint256[](activeCount);
        uint256 index = 0;

        for (uint256 i = 0; i < ceremonies.length;) {
            if (
                ceremonies[i].isActive &&
                !ceremonies[i].isCompleted &&
                block.timestamp >= ceremonies[i].startTime &&
                block.timestamp <= ceremonies[i].endTime
            ) {
                result[index++] = i;
            }
            unchecked { i++; }
        }

        return result;
    }

    function hasParticipated(uint256 _ceremonyId, address _user) external view returns (bool) {
        require(_ceremonyId < ceremonies.length, "BurnCeremony: Invalid ceremony ID");

        return ceremoniesParticipants[_ceremonyId][_user].amount > 0;
    }

    function getCeremoniesPaged(uint256 offset, uint256 limit) external view returns (Ceremony[] memory) {
        uint256 total = ceremonies.length;
        if (offset >= total) return new Ceremony[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        Ceremony[] memory result = new Ceremony[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = ceremonies[i];
            unchecked { i++; }
        }
        return result;
    }

    function getParticipantsPaged(uint256 ceremonyId, uint256 offset, uint256 limit) external view returns (Participant[] memory) {
        require(ceremonyId < ceremonies.length, "BurnCeremony: Invalid ceremony ID");
        Ceremony storage ceremony = ceremonies[ceremonyId];
        uint256 total = ceremony.participants.length;
        if (offset >= total) return new Participant[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        Participant[] memory result = new Participant[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = ceremoniesParticipants[ceremonyId][ceremony.participants[i]];
            unchecked { i++; }
        }
        return result;
    }

    function getBurnRecordsPaged(uint256 ceremonyId, uint256 offset, uint256 limit) external view returns (BurnRecord[] memory) {
        require(ceremonyId < ceremonies.length, "BurnCeremony: Invalid ceremony ID");
        uint256 total = ceremonyBurnRecords[ceremonyId].length;
        if (offset >= total) return new BurnRecord[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        BurnRecord[] memory result = new BurnRecord[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = ceremonyBurnRecords[ceremonyId][i];
            unchecked { i++; }
        }
        return result;
    }
}