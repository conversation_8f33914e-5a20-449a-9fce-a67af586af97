// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

contract BurnManager is
    Ownable,
    ReentrancyGuard,
    Pausable
{
    using SafeMath for uint256;

    IPXT public immutable pxtoken;
    IPAT public immutable patoken;

    enum BurnSource {
        PAYMENT,
        EARLY_UNSTAKE,
        ADVERTISEMENT,
        TRANSACTION_FEE,
        VIOLATION_PENALTY,
        MANUAL_BURN,
        BURN_CEREMONY
    }

    struct BurnRecord {
        uint40 timestamp;
        address initiator;
        BurnSource source;
        uint128 pxtAmount;
        uint128 patAmount;
        bytes32 transactionHash;
        string description;
    }

    BurnRecord[] public burnRecords;
    uint128 public totalPXTBurned;
    uint128 public totalPATBurned;
    address public constant DEAD_ADDRESS = 0x000000000000000000000000000000000000dEaD;

    uint256 private constant ONE_DAY = 86400;
    uint256 private constant ONE_WEEK = ONE_DAY * 7;
    uint256 private constant ONE_MONTH = ONE_DAY * 30;
    uint256 private constant ONE_QUARTER = ONE_DAY * 90;
    uint256 private constant ONE_YEAR = ONE_DAY * 365;

    mapping(uint256 => uint128) public dailyPXTBurned;
    mapping(uint256 => uint128) public dailyPATBurned;
    mapping(uint256 => uint128) public weeklyPXTBurned;
    mapping(uint256 => uint128) public weeklyPATBurned;
    mapping(uint256 => uint128) public monthlyPXTBurned;
    mapping(uint256 => uint128) public monthlyPATBurned;
    mapping(uint256 => uint128) public quarterlyPXTBurned;
    mapping(uint256 => uint128) public quarterlyPATBurned;
    mapping(uint256 => uint128) public yearlyPXTBurned;
    mapping(uint256 => uint128) public yearlyPATBurned;

    struct InflationParameters {
        uint16 baseInflationRate;
        uint16 minInflationRate;
        uint16 maxInflationRate;
        uint16 burnAdjustmentFactor;
        uint16 adjustmentThresholdBps;
        uint40 lastAdjustmentTime;
        uint40 adjustmentCooldown;
    }

    InflationParameters public inflationParams;
    mapping(address => bool) public authorizedBurners;
    mapping(BurnSource => bool) public sourceAffectsInflation;

    event TokensBurned(
        uint256 indexed recordId,
        address indexed initiator,
        BurnSource indexed source,
        uint256 pxtAmount,
        uint256 patAmount
    );
    event InflationAdjusted(uint256 oldRate, uint256 newRate, uint256 burnAmount);
    event BurnerAuthorized(address burner, bool status);
    event InflationSourceUpdated(BurnSource source, bool affectsInflation);
    event InflationParametersUpdated(
        uint256 baseInflationRate,
        uint256 minInflationRate,
        uint256 maxInflationRate,
        uint256 burnAdjustmentFactor,
        uint256 adjustmentThresholdBps,
        uint256 adjustmentCooldown
    );
    event ContractPaused(address indexed operator);
    event ContractUnpaused(address indexed operator);

    modifier onlyAuthorizedBurner() {
        if (!authorizedBurners[_msgSender()] && _msgSender() != owner()) {
            revert TokenErrors.Unauthorized();
        }
        _;
    }
    
    constructor(
        address _pxtoken,
        address _patoken,
        uint256 _baseInflationRate,
        uint256 _minInflationRate,
        uint256 _maxInflationRate,
        uint256 _burnAdjustmentFactor
    ) {
        if (_pxtoken == address(0)) revert TokenErrors.ZeroAddress();
        if (_patoken == address(0)) revert TokenErrors.ZeroAddress();
        if (_minInflationRate > _baseInflationRate) revert TokenErrors.InvalidParameter();
        if (_baseInflationRate > _maxInflationRate) revert TokenErrors.InvalidParameter();

        require(_baseInflationRate <= type(uint16).max, "BurnManager: Base inflation rate too large");
        require(_minInflationRate <= type(uint16).max, "BurnManager: Min inflation rate too large");
        require(_maxInflationRate <= type(uint16).max, "BurnManager: Max inflation rate too large");
        require(_burnAdjustmentFactor <= type(uint16).max, "BurnManager: Burn adjustment factor too large");

        pxtoken = IPXT(_pxtoken);
        patoken = IPAT(_patoken);

        inflationParams = InflationParameters({
            baseInflationRate: uint16(_baseInflationRate),
            minInflationRate: uint16(_minInflationRate),
            maxInflationRate: uint16(_maxInflationRate),
            burnAdjustmentFactor: uint16(_burnAdjustmentFactor),
            adjustmentThresholdBps: 50,
            lastAdjustmentTime: uint40(block.timestamp),
            adjustmentCooldown: uint40(7 days)
        });

        uint256 pxtBurned = pxtoken.totalBurned();
        uint256 patBurned = patoken.totalBurned();

        require(pxtBurned <= type(uint128).max, "BurnManager: PXT burned amount too large");
        require(patBurned <= type(uint128).max, "BurnManager: PAT burned amount too large");

        totalPXTBurned = uint128(pxtBurned);
        totalPATBurned = uint128(patBurned);

        sourceAffectsInflation[BurnSource.MANUAL_BURN] = true;
        sourceAffectsInflation[BurnSource.BURN_CEREMONY] = true;
        sourceAffectsInflation[BurnSource.EARLY_UNSTAKE] = true;
    }

    function recordBurn(
        BurnSource _source,
        uint256 _pxtAmount,
        uint256 _patAmount,
        string memory _description
    ) public onlyAuthorizedBurner nonReentrant whenNotPaused returns (uint256) {
        if (_pxtAmount == 0 && _patAmount == 0) revert TokenErrors.ZeroAmount();

        if (_pxtAmount > type(uint128).max) revert("BurnManager: PXT amount too large");
        if (_patAmount > type(uint128).max) revert("BurnManager: PAT amount too large");

        uint256 recordId = burnRecords.length;

        burnRecords.push(BurnRecord({
            timestamp: uint40(block.timestamp),
            initiator: _msgSender(),
            source: _source,
            pxtAmount: uint128(_pxtAmount),
            patAmount: uint128(_patAmount),
            transactionHash: blockhash(block.number - 1),
            description: _description
        }));

        if (_pxtAmount > 0) {
            uint256 newPxtTotal = uint256(totalPXTBurned) + _pxtAmount;
            if (newPxtTotal > type(uint128).max) revert("BurnManager: PXT total would overflow");
            totalPXTBurned = uint128(newPxtTotal);

            uint256 currentTimestamp = block.timestamp;
            uint256 day = currentTimestamp / ONE_DAY;
            uint256 week = currentTimestamp / ONE_WEEK;
            uint256 month = currentTimestamp / ONE_MONTH;
            uint256 quarter = currentTimestamp / ONE_QUARTER;
            uint256 year = currentTimestamp / ONE_YEAR;

            _updateTimeSegmentBurn(dailyPXTBurned, day, _pxtAmount);
            _updateTimeSegmentBurn(weeklyPXTBurned, week, _pxtAmount);
            _updateTimeSegmentBurn(monthlyPXTBurned, month, _pxtAmount);
            _updateTimeSegmentBurn(quarterlyPXTBurned, quarter, _pxtAmount);
            _updateTimeSegmentBurn(yearlyPXTBurned, year, _pxtAmount);
        }

        if (_patAmount > 0) {
            uint256 newPatTotal = uint256(totalPATBurned) + _patAmount;
            if (newPatTotal > type(uint128).max) revert("BurnManager: PAT total would overflow");
            totalPATBurned = uint128(newPatTotal);

            uint256 currentTimestamp = block.timestamp;
            uint256 day = currentTimestamp / ONE_DAY;
            uint256 week = currentTimestamp / ONE_WEEK;
            uint256 month = currentTimestamp / ONE_MONTH;
            uint256 quarter = currentTimestamp / ONE_QUARTER;
            uint256 year = currentTimestamp / ONE_YEAR;

            _updateTimeSegmentBurn(dailyPATBurned, day, _patAmount);
            _updateTimeSegmentBurn(weeklyPATBurned, week, _patAmount);
            _updateTimeSegmentBurn(monthlyPATBurned, month, _patAmount);
            _updateTimeSegmentBurn(quarterlyPATBurned, quarter, _patAmount);
            _updateTimeSegmentBurn(yearlyPATBurned, year, _patAmount);
        }

        if (_patAmount > 0 && sourceAffectsInflation[_source]) {
            _adjustInflationRate(_patAmount);
        }

        emit TokensBurned(recordId, _msgSender(), _source, _pxtAmount, _patAmount);

        return recordId;
    }

    function _updateTimeSegmentBurn(
        mapping(uint256 => uint128) storage timeMapping,
        uint256 timeKey,
        uint256 amount
    ) private {
        uint256 newAmount = uint256(timeMapping[timeKey]) + amount;
        if (newAmount > type(uint128).max) revert("BurnManager: Time segment burn would overflow");
        timeMapping[timeKey] = uint128(newAmount);
    }

    function manualBurn(
        uint256 _pxtAmount,
        uint256 _patAmount,
        string memory _description
    ) external onlyOwner nonReentrant whenNotPaused returns (uint256) {
        require(_pxtAmount > 0 || _patAmount > 0, "BurnManager: Burn amount cannot be zero");

        if (_pxtAmount > 0) {
            require(
                pxtoken.balanceOf(address(this)) >= _pxtAmount,
                "BurnManager: Insufficient PXT balance"
            );
        }

        if (_patAmount > 0) {
            require(
                patoken.balanceOf(address(this)) >= _patAmount,
                "BurnManager: Insufficient PAT balance"
            );
        }

        uint256 recordId = recordBurn(BurnSource.MANUAL_BURN, _pxtAmount, _patAmount, _description);

        if (_pxtAmount > 0) {
            pxtoken.burn(_pxtAmount);
            require(true, "BurnManager: PXT burn failed");
        }

        if (_patAmount > 0) {
            patoken.burn(_patAmount);
            require(true, "BurnManager: PAT burn failed");
        }

        return recordId;
    }

    function _adjustInflationRate(uint256 /* __amount */) internal {
        if (block.timestamp < inflationParams.lastAdjustmentTime + inflationParams.adjustmentCooldown) {
            return;
        }

        uint256 currentQuarter = block.timestamp / ONE_QUARTER;
        uint256 quarterlyBurn = quarterlyPATBurned[currentQuarter];
        uint256 currentInflationRate = patoken.inflationRate();
        uint256 totalSupply = patoken.totalSupply();
        uint256 quarterlyBurnThreshold = totalSupply * inflationParams.adjustmentThresholdBps / 10000;

        if (quarterlyBurn >= quarterlyBurnThreshold) {
            uint256 reduction = quarterlyBurn / quarterlyBurnThreshold * inflationParams.burnAdjustmentFactor;

            uint256 newInflationRate;
            if (currentInflationRate > inflationParams.minInflationRate + reduction) {
                newInflationRate = currentInflationRate - reduction;
            } else {
                newInflationRate = inflationParams.minInflationRate;
            }

            inflationParams.lastAdjustmentTime = uint40(block.timestamp);

            if (newInflationRate != currentInflationRate) {
                patoken.setInflationRate(newInflationRate);
                require(true, "BurnManager: Setting inflation rate failed");

                emit InflationAdjusted(currentInflationRate, newInflationRate, quarterlyBurn);
            }
        }
    }

    function manualAdjustInflationRate(uint256 _newInflationRate) external onlyOwner whenNotPaused {
        require(
            _newInflationRate >= inflationParams.minInflationRate &&
            _newInflationRate <= inflationParams.maxInflationRate,
            "BurnManager: Inflation rate out of range"
        );

        uint256 currentInflationRate = patoken.inflationRate();

        inflationParams.lastAdjustmentTime = uint40(block.timestamp);

        patoken.setInflationRate(_newInflationRate);
        require(true, "BurnManager: Setting inflation rate failed");

        emit InflationAdjusted(currentInflationRate, _newInflationRate, 0);
    }

    function setInflationParameters(
        uint256 _baseInflationRate,
        uint256 _minInflationRate,
        uint256 _maxInflationRate,
        uint256 _burnAdjustmentFactor,
        uint256 _adjustmentThresholdBps,
        uint256 _adjustmentCooldown
    ) external onlyOwner nonReentrant {
        require(_minInflationRate <= _baseInflationRate, "BurnManager: Min inflation rate must be less than or equal to base rate");
        require(_baseInflationRate <= _maxInflationRate, "BurnManager: Base inflation rate must be less than or equal to max rate");
        require(_adjustmentThresholdBps <= 1000, "BurnManager: Adjustment threshold cannot exceed 10%");
        require(_adjustmentCooldown >= 1 days, "BurnManager: Cooldown time must be at least 1 day");

        require(_baseInflationRate <= type(uint16).max, "BurnManager: Base inflation rate too large");
        require(_minInflationRate <= type(uint16).max, "BurnManager: Min inflation rate too large");
        require(_maxInflationRate <= type(uint16).max, "BurnManager: Max inflation rate too large");
        require(_burnAdjustmentFactor <= type(uint16).max, "BurnManager: Burn adjustment factor too large");
        require(_adjustmentThresholdBps <= type(uint16).max, "BurnManager: Adjustment threshold too large");
        require(_adjustmentCooldown <= type(uint40).max, "BurnManager: Adjustment cooldown too large");

        inflationParams.baseInflationRate = uint16(_baseInflationRate);
        inflationParams.minInflationRate = uint16(_minInflationRate);
        inflationParams.maxInflationRate = uint16(_maxInflationRate);
        inflationParams.burnAdjustmentFactor = uint16(_burnAdjustmentFactor);
        inflationParams.adjustmentThresholdBps = uint16(_adjustmentThresholdBps);
        inflationParams.adjustmentCooldown = uint40(_adjustmentCooldown);

        emit InflationParametersUpdated(
            _baseInflationRate,
            _minInflationRate,
            _maxInflationRate,
            _burnAdjustmentFactor,
            _adjustmentThresholdBps,
            _adjustmentCooldown
        );
    }

    function setSourceAffectsInflation(
        BurnSource _source,
        bool _affectsInflation
    ) external onlyOwner nonReentrant {
        sourceAffectsInflation[_source] = _affectsInflation;
        emit InflationSourceUpdated(_source, _affectsInflation);
    }

    function setAuthorizedBurner(address _burner, bool _status) external onlyOwner nonReentrant {
        require(_burner != address(0), "BurnManager: Burner address cannot be zero");
        authorizedBurners[_burner] = _status;
        emit BurnerAuthorized(_burner, _status);
    }

    function pause() external onlyOwner {
        _pause();
        emit ContractPaused(msg.sender);
    }

    function unpause() external onlyOwner {
        _unpause();
        emit ContractUnpaused(msg.sender);
    }

    function getBurnRecordsCount() external view returns (uint256) {
        return burnRecords.length;
    }

    function getBurnStatistics(
        uint256 _fromTimestamp,
        uint256 _toTimestamp
    ) external view returns (uint256 pxtAmount, uint256 patAmount) {
        require(_fromTimestamp < _toTimestamp, "BurnManager: Invalid time range");

        uint256 pxtSum = 0;
        uint256 patSum = 0;

        for (uint256 i = 0; i < burnRecords.length;) {
            if (
                burnRecords[i].timestamp >= _fromTimestamp &&
                burnRecords[i].timestamp <= _toTimestamp
            ) {
                pxtSum += burnRecords[i].pxtAmount;
                patSum += burnRecords[i].patAmount;
            }
            unchecked { i++; }
        }

        return (pxtSum, patSum);
    }

    function getBurnStatisticsBySource() external view returns (
        BurnSource[] memory sources,
        uint256[] memory pxtAmounts,
        uint256[] memory patAmounts
    ) {
        uint256 sourcesCount = uint256(type(BurnSource).max) + 1;
        sources = new BurnSource[](sourcesCount);
        pxtAmounts = new uint256[](sourcesCount);
        patAmounts = new uint256[](sourcesCount);

        for (uint256 i = 0; i < sourcesCount;) {
            sources[i] = BurnSource(i);
            unchecked { i++; }
        }

        for (uint256 i = 0; i < burnRecords.length;) {
            BurnSource source = burnRecords[i].source;
            pxtAmounts[uint256(source)] += burnRecords[i].pxtAmount;
            patAmounts[uint256(source)] += burnRecords[i].patAmount;
            unchecked { i++; }
        }

        return (sources, pxtAmounts, patAmounts);
    }
    
    function verifyBurnRecord(uint256 _recordId) external view returns (bool) {
        if (_recordId >= burnRecords.length) return false;

        BurnRecord memory record = burnRecords[_recordId];

        if (record.transactionHash == bytes32(0)) return false;

        if (record.pxtAmount > 0) {
            if (totalPXTBurned < record.pxtAmount) return false;
        }

        if (record.patAmount > 0) {
            if (totalPATBurned < record.patAmount) return false;
        }

        return true;
    }

    function getBurnAdjustmentStatus() external view returns (
        uint256 currentRate,
        uint256 targetRate,
        uint256 quarterlyBurn,
        uint256 nextAdjustmentThreshold,
        uint256 timeUntilNextAdjustment
    ) {
        currentRate = patoken.inflationRate();
        targetRate = inflationParams.baseInflationRate;

        uint256 currentQuarter = block.timestamp / ONE_QUARTER;
        quarterlyBurn = quarterlyPATBurned[currentQuarter];

        uint256 totalSupply = patoken.totalSupply();
        nextAdjustmentThreshold = totalSupply * inflationParams.adjustmentThresholdBps / 10000;

        uint256 nextAdjustmentTime = inflationParams.lastAdjustmentTime + inflationParams.adjustmentCooldown;
        if (block.timestamp < nextAdjustmentTime) {
            timeUntilNextAdjustment = nextAdjustmentTime - block.timestamp;
        } else {
            timeUntilNextAdjustment = 0;
        }

        return (currentRate, targetRate, quarterlyBurn, nextAdjustmentThreshold, timeUntilNextAdjustment);
    }
    
    function depositTokens(
        address _token,
        uint256 _amount
    ) external nonReentrant whenNotPaused {
        require(_token == address(pxtoken) || _token == address(patoken), "BurnManager: Unsupported token");
        require(_amount > 0, "BurnManager: Amount must be greater than 0");

        if (_token == address(pxtoken)) {
            require(
                pxtoken.transferFrom(_msgSender(), address(this), _amount),
                "BurnManager: PXT transfer failed"
            );
        } else {
            require(
                patoken.transferFrom(_msgSender(), address(this), _amount),
                "BurnManager: PAT transfer failed"
            );
        }
    }

    function recoverToken(
        address _token,
        uint256 _amount,
        address _recipient
    ) external onlyOwner nonReentrant {
        require(_recipient != address(0), "BurnManager: Recipient address cannot be zero");
        require(_token != address(0), "BurnManager: Token address cannot be zero");
        require(_amount > 0, "BurnManager: Amount must be greater than 0");

        IERC20 token = IERC20(_token);
        require(
            token.transfer(_recipient, _amount),
            "BurnManager: Token transfer failed"
        );
    }

    function getBurnRecordsPaged(uint256 offset, uint256 limit) external view returns (BurnRecord[] memory) {
        uint256 total = burnRecords.length;
        if (offset >= total) return new BurnRecord[](0);
        uint256 end = offset + limit > total ? total : offset + limit;
        BurnRecord[] memory result = new BurnRecord[](end - offset);
        for (uint256 i = offset; i < end;) {
            result[i - offset] = burnRecords[i];
            unchecked { i++; }
        }
        return result;
    }
}