// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

/**
 * @title EventDrivenAutoChain
 * @dev 事件驱动的PAT自动上链系统
 * 
 * 特点：
 * 1. 基于用户行为触发自动补充
 * 2. 智能预测和预充值
 * 3. 多级触发机制
 * 4. 成本优化的批量处理
 */
contract EventDrivenAutoChain is AccessControl, ReentrancyGuard {
    bytes32 public constant OPERATOR_ROLE = keccak256("OPERATOR_ROLE");
    bytes32 public constant TRIGGER_ROLE = keccak256("TRIGGER_ROLE");

    IPAT public immutable patToken;

    // 触发条件类型
    enum TriggerType {
        BALANCE_LOW,        // 余额过低
        USAGE_SPIKE,        // 使用量激增
        TIME_BASED,         // 基于时间
        PREDICTIVE,         // 预测性补充
        EMERGENCY           // 紧急补充
    }

    // 自动补充规则
    struct AutoRefillRule {
        uint256 id;
        address targetAccount;      // 目标账户
        TriggerType triggerType;    // 触发类型
        uint256 threshold;          // 触发阈值
        uint256 refillAmount;       // 补充数量
        uint256 maxDailyRefills;    // 每日最大补充次数
        uint256 cooldownPeriod;     // 冷却期
        bool isActive;              // 是否激活
        uint256 lastTriggered;      // 上次触发时间
        uint256 dailyRefillCount;   // 今日补充次数
        uint256 lastResetTime;      // 上次重置时间
    }

    // 使用模式分析
    struct UsagePattern {
        uint256 hourlyAverage;      // 小时平均使用量
        uint256 dailyAverage;       // 日平均使用量
        uint256 peakHourUsage;      // 峰值小时使用量
        uint256 lastUpdateTime;     // 上次更新时间
        uint256 totalTransactions;  // 总交易数
    }

    // 预测模型参数
    struct PredictionModel {
        uint256 trendFactor;        // 趋势因子
        uint256 seasonalFactor;     // 季节性因子
        uint256 volatilityFactor;   // 波动性因子
        uint256 confidenceLevel;    // 置信度
    }

    mapping(uint256 => AutoRefillRule) public refillRules;
    mapping(address => UsagePattern) public usagePatterns;
    mapping(address => uint256[]) public accountRules; // 账户对应的规则ID列表
    
    uint256 public ruleCount;
    address public treasuryAccount;
    PredictionModel public predictionModel;
    
    // 统计数据
    uint256 public totalAutoRefills;
    uint256 public totalAmountRefilled;
    uint256 public successfulPredictions;
    uint256 public totalPredictions;

    event AutoRefillTriggered(
        uint256 indexed ruleId,
        address indexed account,
        TriggerType triggerType,
        uint256 amount,
        uint256 timestamp
    );
    
    event UsagePatternUpdated(
        address indexed account,
        uint256 hourlyAverage,
        uint256 dailyAverage,
        uint256 timestamp
    );
    
    event PredictiveRefillExecuted(
        address indexed account,
        uint256 predictedUsage,
        uint256 refillAmount,
        uint256 confidence
    );

    constructor(
        address _patToken,
        address _treasuryAccount,
        address _admin
    ) {
        if (_patToken == address(0)) revert TokenErrors.ZeroAddress();
        if (_treasuryAccount == address(0)) revert TokenErrors.ZeroAddress();
        
        patToken = IPAT(_patToken);
        treasuryAccount = _treasuryAccount;
        
        // 初始化预测模型
        predictionModel = PredictionModel({
            trendFactor: 1200,      // 120% (基数10000)
            seasonalFactor: 1100,   // 110%
            volatilityFactor: 800,  // 80%
            confidenceLevel: 8500   // 85%
        });
        
        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(OPERATOR_ROLE, _admin);
    }

    /**
     * @dev 创建自动补充规则
     */
    function createRefillRule(
        address targetAccount,
        TriggerType triggerType,
        uint256 threshold,
        uint256 refillAmount,
        uint256 maxDailyRefills,
        uint256 cooldownPeriod
    ) external onlyRole(OPERATOR_ROLE) returns (uint256 ruleId) {
        if (targetAccount == address(0)) revert TokenErrors.ZeroAddress();
        if (refillAmount == 0) revert TokenErrors.ZeroAmount();
        
        ruleId = ruleCount;
        unchecked {
            ruleCount++;
        }

        refillRules[ruleId] = AutoRefillRule({
            id: ruleId,
            targetAccount: targetAccount,
            triggerType: triggerType,
            threshold: threshold,
            refillAmount: refillAmount,
            maxDailyRefills: maxDailyRefills,
            cooldownPeriod: cooldownPeriod,
            isActive: true,
            lastTriggered: 0,
            dailyRefillCount: 0,
            lastResetTime: block.timestamp
        });
        
        accountRules[targetAccount].push(ruleId);
        
        return ruleId;
    }

    /**
     * @dev 触发自动补充检查
     */
    function triggerAutoRefill(address account, TriggerType triggerType) 
        external 
        onlyRole(TRIGGER_ROLE) 
        nonReentrant 
    {
        uint256[] memory rules = accountRules[account];
        
        for (uint256 i = 0; i < rules.length; i++) {
            uint256 ruleId = rules[i];
            AutoRefillRule storage rule = refillRules[ruleId];
            
            if (!rule.isActive || rule.triggerType != triggerType) continue;
            
            if (_shouldTriggerRule(rule)) {
                _executeRefill(ruleId);
            }
        }
    }

    /**
     * @dev 更新使用模式
     */
    function updateUsagePattern(
        address account,
        uint256 transactionAmount
    ) external onlyRole(TRIGGER_ROLE) {
        UsagePattern storage pattern = usagePatterns[account];
        
        uint256 currentHour = block.timestamp / 1 hours;
        uint256 lastHour = pattern.lastUpdateTime / 1 hours;
        
        // 🔒 安全优化：使用unchecked进行安全的数学运算
        if (currentHour > lastHour) {
            unchecked {
                pattern.hourlyAverage = (pattern.hourlyAverage * 9 + transactionAmount) / 10;
            }
        } else {
            unchecked {
                pattern.hourlyAverage += transactionAmount;
            }
        }
        
        // 更新日平均值
        uint256 currentDay = block.timestamp / 1 days;
        uint256 lastDay = pattern.lastUpdateTime / 1 days;
        
        if (currentDay > lastDay) {
            pattern.dailyAverage = (pattern.dailyAverage * 6 + pattern.hourlyAverage * 24) / 7;
        }
        
        // 更新峰值
        if (pattern.hourlyAverage > pattern.peakHourUsage) {
            pattern.peakHourUsage = pattern.hourlyAverage;
        }
        
        pattern.lastUpdateTime = block.timestamp;
        unchecked {
            pattern.totalTransactions++;
        }
        
        emit UsagePatternUpdated(
            account,
            pattern.hourlyAverage,
            pattern.dailyAverage,
            block.timestamp
        );
        
        // 检查是否需要预测性补充
        _checkPredictiveRefill(account);
    }

    /**
     * @dev 预测性补充检查
     */
    function _checkPredictiveRefill(address account) internal {
        UsagePattern storage pattern = usagePatterns[account];
        
        if (pattern.totalTransactions < 10) return; // 数据不足
        
        // 预测未来24小时使用量
        uint256 predictedUsage = _predictFutureUsage(account);
        uint256 currentBalance = patToken.balanceOf(account);
        
        // 如果预测使用量超过当前余额的80%，触发补充
        if (predictedUsage > currentBalance * 8 / 10) {
            uint256 refillAmount = predictedUsage - currentBalance + (predictedUsage / 5); // 额外20%缓冲
            
            unchecked {
                totalPredictions++;
            }

            if (_executePredictiveRefill(account, refillAmount, predictedUsage)) {
                unchecked {
                    successfulPredictions++;
                }
            }
        }
    }

    /**
     * @dev 预测未来使用量
     */
    function _predictFutureUsage(address account) internal view returns (uint256) {
        UsagePattern storage pattern = usagePatterns[account];
        
        uint256 baseUsage = pattern.dailyAverage;
        
        // 🔒 安全优化：防止溢出的预测计算
        unchecked {
            // 应用趋势因子
            baseUsage = (baseUsage * predictionModel.trendFactor) / 10000;

            // 应用季节性因子
            baseUsage = (baseUsage * predictionModel.seasonalFactor) / 10000;
        }

        // 应用波动性因子（需要检查下溢）
        uint256 volatilityAdjustment = 0;
        if (pattern.peakHourUsage > pattern.hourlyAverage) {
            unchecked {
                volatilityAdjustment = ((pattern.peakHourUsage - pattern.hourlyAverage) *
                                      predictionModel.volatilityFactor) / 10000;
            }
        }
        
        return baseUsage + volatilityAdjustment;
    }

    /**
     * @dev 执行预测性补充
     */
    function _executePredictiveRefill(
        address account,
        uint256 refillAmount,
        uint256 predictedUsage
    ) internal returns (bool success) {
        // 检查国库余额
        uint256 treasuryBalance = patToken.balanceOf(treasuryAccount);
        if (treasuryBalance < refillAmount) return false;
        
        try patToken.transferFrom(treasuryAccount, account, refillAmount) {
            emit PredictiveRefillExecuted(
                account,
                predictedUsage,
                refillAmount,
                predictionModel.confidenceLevel
            );
            
            unchecked {
                totalAutoRefills++;
                totalAmountRefilled += refillAmount;
            }
            
            return true;
        } catch {
            return false;
        }
    }

    /**
     * @dev 检查规则是否应该触发
     */
    function _shouldTriggerRule(AutoRefillRule storage rule) internal view returns (bool) {
        // 检查冷却期
        if (block.timestamp < rule.lastTriggered + rule.cooldownPeriod) {
            return false;
        }
        
        // 检查每日限制
        uint256 dailyRefillCount = rule.dailyRefillCount;
        if (block.timestamp >= rule.lastResetTime + 1 days) {
            dailyRefillCount = 0; // 新的一天
        }
        
        if (dailyRefillCount >= rule.maxDailyRefills) {
            return false;
        }
        
        // 检查触发条件
        if (rule.triggerType == TriggerType.BALANCE_LOW) {
            uint256 currentBalance = patToken.balanceOf(rule.targetAccount);
            return currentBalance < rule.threshold;
        }
        
        return true;
    }

    /**
     * @dev 执行补充
     */
    function _executeRefill(uint256 ruleId) internal {
        AutoRefillRule storage rule = refillRules[ruleId];
        
        // 检查国库余额
        uint256 treasuryBalance = patToken.balanceOf(treasuryAccount);
        if (treasuryBalance < rule.refillAmount) return;
        
        try patToken.transferFrom(treasuryAccount, rule.targetAccount, rule.refillAmount) {
            // 更新规则状态
            rule.lastTriggered = block.timestamp;
            
            if (block.timestamp >= rule.lastResetTime + 1 days) {
                rule.dailyRefillCount = 1;
                rule.lastResetTime = block.timestamp;
            } else {
                rule.dailyRefillCount++;
            }
            
            totalAutoRefills++;
            totalAmountRefilled += rule.refillAmount;
            
            emit AutoRefillTriggered(
                ruleId,
                rule.targetAccount,
                rule.triggerType,
                rule.refillAmount,
                block.timestamp
            );
        } catch {
            // 转账失败，记录但不抛出异常
        }
    }

    /**
     * @dev 更新预测模型参数
     */
    function updatePredictionModel(
        uint256 trendFactor,
        uint256 seasonalFactor,
        uint256 volatilityFactor,
        uint256 confidenceLevel
    ) external onlyRole(OPERATOR_ROLE) {
        predictionModel.trendFactor = trendFactor;
        predictionModel.seasonalFactor = seasonalFactor;
        predictionModel.volatilityFactor = volatilityFactor;
        predictionModel.confidenceLevel = confidenceLevel;
    }

    /**
     * @dev 获取账户的使用模式
     */
    function getUsagePattern(address account) external view returns (
        uint256 hourlyAverage,
        uint256 dailyAverage,
        uint256 peakHourUsage,
        uint256 totalTransactions,
        uint256 predictedUsage
    ) {
        UsagePattern storage pattern = usagePatterns[account];
        
        return (
            pattern.hourlyAverage,
            pattern.dailyAverage,
            pattern.peakHourUsage,
            pattern.totalTransactions,
            _predictFutureUsage(account)
        );
    }

    /**
     * @dev 获取统计信息
     */
    function getStats() external view returns (
        uint256 _totalAutoRefills,
        uint256 _totalAmountRefilled,
        uint256 _successfulPredictions,
        uint256 _totalPredictions,
        uint256 predictionAccuracy
    ) {
        predictionAccuracy = totalPredictions > 0 ? 
            (successfulPredictions * 10000 / totalPredictions) : 0;
            
        return (
            totalAutoRefills,
            totalAmountRefilled,
            successfulPredictions,
            totalPredictions,
            predictionAccuracy
        );
    }
}
