// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

/**
 * @title StakingAnalytics
 * @dev 质押数据分析和统计合约
 * @notice 这个合约提供详细的质押数据分析、统计和报告功能
 * <AUTHOR> Team
 *
 * 功能特性:
 * - 实时数据统计：质押量、用户数、奖励分发等实时统计
 * - 历史数据分析：质押趋势、用户行为分析
 * - 性能指标：APY计算、收益率分析
 * - 用户画像：基于质押行为的用户分类
 * - 风险评估：质押池风险指标监控
 * - 数据导出：支持数据导出和外部分析
 */

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/math/Math.sol";
import "../interfaces/TokenErrors.sol";

interface IStakingPool {
    enum StakingLevel { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> }
    
    function getTotalStaked() external view returns (uint256);
    function getUserStakingInfo(address _user) external view returns (
        uint256 amount,
        uint256 startTime,
        StakingLevel level,
        bool isUnlocking,
        uint256 unlockTime,
        uint256 requestedDuration,
        uint256 pendingReward,
        uint256 randomSeed,
        uint256 currentRandomMultiplier
    );
}

contract StakingAnalytics is AccessControl, Pausable {
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant ANALYST_ROLE = keccak256("ANALYST_ROLE");
    bytes32 public constant DATA_PROVIDER_ROLE = keccak256("DATA_PROVIDER_ROLE");

    enum UserType { Casual, Regular, Whale, Institution }
    enum TrendDirection { Increasing, Decreasing, Stable }

    struct DailyStats {
        uint256 date;
        uint256 totalStaked;
        uint256 totalUsers;
        uint256 newUsers;
        uint256 totalRewards;
        uint256 averageStakeAmount;
        uint256 averageStakeDuration;
    }

    struct LevelStats {
        IStakingPool.StakingLevel level;
        uint256 userCount;
        uint256 totalStaked;
        uint256 averageStakeAmount;
        uint256 totalRewards;
        uint256 averageAPY;
    }

    struct UserProfile {
        address user;
        UserType userType;
        uint256 totalStaked;
        uint256 stakingDuration;
        uint256 totalRewards;
        uint256 lastActivityTime;
        IStakingPool.StakingLevel currentLevel;
        uint256 riskScore;
    }

    struct PerformanceMetrics {
        uint256 totalValueLocked;
        uint256 averageAPY;
        uint256 totalRewardsDistributed;
        uint256 userRetentionRate;
        uint256 stakingUtilizationRate;
        TrendDirection trend;
        uint256 riskLevel;
    }

    struct RiskIndicators {
        uint256 concentrationRisk;     // 大户集中度风险
        uint256 liquidityRisk;         // 流动性风险
        uint256 durationRisk;          // 期限风险
        uint256 rewardSustainability;  // 奖励可持续性
        uint256 overallRiskScore;      // 综合风险评分
    }

    // 状态变量
    IStakingPool public stakingPool;
    
    mapping(uint256 => DailyStats) public dailyStats;
    mapping(IStakingPool.StakingLevel => LevelStats) public levelStats;
    mapping(address => UserProfile) public userProfiles;
    
    uint256[] public statsDates;
    address[] public trackedUsers;
    
    PerformanceMetrics public currentMetrics;
    RiskIndicators public riskIndicators;
    
    // 配置参数
    uint256 public whaleThreshold = 100000 * 10**18;  // 100K PXT
    uint256 public institutionThreshold = 1000000 * 10**18;  // 1M PXT
    uint256 public riskThreshold = 7000;  // 70%
    
    // 统计计数器
    uint256 public totalHistoricalUsers;
    uint256 public totalHistoricalRewards;
    uint256 public maxDailyStaked;
    uint256 public maxDailyUsers;

    // 事件
    event DailyStatsUpdated(uint256 indexed date, uint256 totalStaked, uint256 totalUsers);
    event UserProfileUpdated(address indexed user, UserType userType, uint256 riskScore);
    event PerformanceMetricsUpdated(uint256 tvl, uint256 apy, TrendDirection trend);
    event RiskIndicatorsUpdated(uint256 overallRiskScore, uint256 concentrationRisk);
    event AlertTriggered(string alertType, uint256 value, uint256 threshold);

    constructor(address _stakingPool) {
        if (_stakingPool == address(0)) revert TokenErrors.ZeroAddress();
        
        stakingPool = IStakingPool(_stakingPool);
        
        _setupRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _setupRole(ADMIN_ROLE, msg.sender);
        _setupRole(ANALYST_ROLE, msg.sender);
        _setupRole(DATA_PROVIDER_ROLE, msg.sender);
    }

    function updateDailyStats() external onlyRole(DATA_PROVIDER_ROLE) {
        uint256 today = block.timestamp / 1 days;
        
        if (dailyStats[today].date == today) {
            return; // 今日数据已更新
        }

        uint256 totalStaked = stakingPool.getTotalStaked();
        uint256 totalUsers = trackedUsers.length;
        
        // 计算新用户数
        uint256 newUsers = 0;
        uint256 yesterday = today - 1;
        if (dailyStats[yesterday].date == yesterday) {
            newUsers = totalUsers > dailyStats[yesterday].totalUsers ? 
                      totalUsers - dailyStats[yesterday].totalUsers : 0;
        }

        // 计算平均质押金额
        uint256 averageStakeAmount = totalUsers > 0 ? totalStaked / totalUsers : 0;

        dailyStats[today] = DailyStats({
            date: today,
            totalStaked: totalStaked,
            totalUsers: totalUsers,
            newUsers: newUsers,
            totalRewards: totalHistoricalRewards,
            averageStakeAmount: averageStakeAmount,
            averageStakeDuration: _calculateAverageStakeDuration()
        });

        statsDates.push(today);
        
        // 更新最大值记录
        if (totalStaked > maxDailyStaked) {
            maxDailyStaked = totalStaked;
        }
        if (totalUsers > maxDailyUsers) {
            maxDailyUsers = totalUsers;
        }

        emit DailyStatsUpdated(today, totalStaked, totalUsers);
    }

    function updateUserProfile(address _user) external onlyRole(DATA_PROVIDER_ROLE) {
        (uint256 amount, uint256 startTime, IStakingPool.StakingLevel level, , , , , , ) = 
            stakingPool.getUserStakingInfo(_user);

        if (amount == 0) return;

        UserType userType = _determineUserType(amount);
        uint256 stakingDuration = block.timestamp - startTime;
        uint256 riskScore = _calculateUserRiskScore(_user, amount, stakingDuration);

        userProfiles[_user] = UserProfile({
            user: _user,
            userType: userType,
            totalStaked: amount,
            stakingDuration: stakingDuration,
            totalRewards: 0, // 需要从奖励合约获取
            lastActivityTime: block.timestamp,
            currentLevel: level,
            riskScore: riskScore
        });

        // 添加到跟踪列表
        bool isTracked = false;
        for (uint256 i = 0; i < trackedUsers.length; i++) {
            if (trackedUsers[i] == _user) {
                isTracked = true;
                break;
            }
        }
        if (!isTracked) {
            trackedUsers.push(_user);
            totalHistoricalUsers++;
        }

        emit UserProfileUpdated(_user, userType, riskScore);
    }

    function updateLevelStats() external onlyRole(DATA_PROVIDER_ROLE) {
        // 重置所有等级统计
        for (uint256 i = 0; i < 7; i++) {
            IStakingPool.StakingLevel level = IStakingPool.StakingLevel(i);
            levelStats[level] = LevelStats({
                level: level,
                userCount: 0,
                totalStaked: 0,
                averageStakeAmount: 0,
                totalRewards: 0,
                averageAPY: 0
            });
        }

        // 遍历所有用户更新等级统计
        for (uint256 i = 0; i < trackedUsers.length; i++) {
            address user = trackedUsers[i];
            (uint256 amount, , IStakingPool.StakingLevel level, , , , , , ) = 
                stakingPool.getUserStakingInfo(user);

            if (amount > 0) {
                LevelStats storage stats = levelStats[level];
                stats.userCount++;
                stats.totalStaked += amount;
            }
        }

        // 计算平均值
        for (uint256 i = 0; i < 7; i++) {
            IStakingPool.StakingLevel level = IStakingPool.StakingLevel(i);
            LevelStats storage stats = levelStats[level];
            
            if (stats.userCount > 0) {
                stats.averageStakeAmount = stats.totalStaked / stats.userCount;
                stats.averageAPY = _calculateLevelAPY(level);
            }
        }
    }

    function updatePerformanceMetrics() external onlyRole(ANALYST_ROLE) {
        uint256 tvl = stakingPool.getTotalStaked();
        uint256 avgAPY = _calculateOverallAPY();
        TrendDirection trend = _analyzeTrend();
        uint256 retentionRate = _calculateRetentionRate();
        uint256 utilizationRate = _calculateUtilizationRate();
        uint256 riskLevel = _calculateOverallRisk();

        currentMetrics = PerformanceMetrics({
            totalValueLocked: tvl,
            averageAPY: avgAPY,
            totalRewardsDistributed: totalHistoricalRewards,
            userRetentionRate: retentionRate,
            stakingUtilizationRate: utilizationRate,
            trend: trend,
            riskLevel: riskLevel
        });

        emit PerformanceMetricsUpdated(tvl, avgAPY, trend);
    }

    function updateRiskIndicators() external onlyRole(ANALYST_ROLE) {
        uint256 concentrationRisk = _calculateConcentrationRisk();
        uint256 liquidityRisk = _calculateLiquidityRisk();
        uint256 durationRisk = _calculateDurationRisk();
        uint256 sustainability = _calculateRewardSustainability();
        
        uint256 overallRisk = (concentrationRisk + liquidityRisk + durationRisk + (10000 - sustainability)) / 4;

        riskIndicators = RiskIndicators({
            concentrationRisk: concentrationRisk,
            liquidityRisk: liquidityRisk,
            durationRisk: durationRisk,
            rewardSustainability: sustainability,
            overallRiskScore: overallRisk
        });

        // 触发风险警报
        if (overallRisk > riskThreshold) {
            emit AlertTriggered("HIGH_RISK", overallRisk, riskThreshold);
        }

        emit RiskIndicatorsUpdated(overallRisk, concentrationRisk);
    }

    function _determineUserType(uint256 _amount) internal view returns (UserType) {
        if (_amount >= institutionThreshold) {
            return UserType.Institution;
        } else if (_amount >= whaleThreshold) {
            return UserType.Whale;
        } else if (_amount >= 10000 * 10**18) {
            return UserType.Regular;
        } else {
            return UserType.Casual;
        }
    }

    function _calculateUserRiskScore(address /* _user */, uint256 _amount, uint256 _duration) internal view returns (uint256) {
        uint256 riskScore = 5000; // 基础风险分数 50%

        // 基于质押金额调整风险
        if (_amount >= institutionThreshold) {
            riskScore += 2000; // 机构用户风险较高
        } else if (_amount >= whaleThreshold) {
            riskScore += 1000; // 大户风险中等
        }

        // 基于质押时长调整风险
        if (_duration < 30 days) {
            riskScore += 1500; // 短期质押风险高
        } else if (_duration > 365 days) {
            riskScore -= 1000; // 长期质押风险低
        }

        return Math.min(riskScore, 10000);
    }

    function _calculateAverageStakeDuration() internal view returns (uint256) {
        if (trackedUsers.length == 0) return 0;

        uint256 totalDuration = 0;
        uint256 validUsers = 0;

        for (uint256 i = 0; i < trackedUsers.length; i++) {
            (uint256 amount, uint256 startTime, , , , , , , ) =
                stakingPool.getUserStakingInfo(trackedUsers[i]);

            if (amount > 0 && startTime > 0) {
                totalDuration += (block.timestamp - startTime);
                validUsers++;
            }
        }

        return validUsers > 0 ? totalDuration / validUsers : 0;
    }

    function _calculateLevelAPY(IStakingPool.StakingLevel _level) internal pure returns (uint256) {
        // 简化的APY计算，实际应该基于历史奖励数据
        if (_level == IStakingPool.StakingLevel.DingJi) return 500;      // 5%
        if (_level == IStakingPool.StakingLevel.ChengJi) return 600;     // 6%
        if (_level == IStakingPool.StakingLevel.YiJi) return 750;        // 7.5%
        if (_level == IStakingPool.StakingLevel.JiaJi) return 1000;      // 10%
        if (_level == IStakingPool.StakingLevel.ShiJue) return 1250;     // 12.5%
        if (_level == IStakingPool.StakingLevel.ShuangShiJue) return 1500; // 15%
        if (_level == IStakingPool.StakingLevel.ZhiZun) return 2000;     // 20%
        return 500;
    }

    function _calculateOverallAPY() internal view returns (uint256) {
        uint256 totalWeightedAPY = 0;
        uint256 totalStaked = 0;

        for (uint256 i = 0; i < 7; i++) {
            IStakingPool.StakingLevel level = IStakingPool.StakingLevel(i);
            LevelStats storage stats = levelStats[level];

            if (stats.totalStaked > 0) {
                totalWeightedAPY += stats.averageAPY * stats.totalStaked;
                totalStaked += stats.totalStaked;
            }
        }

        return totalStaked > 0 ? totalWeightedAPY / totalStaked : 0;
    }

    function _analyzeTrend() internal view returns (TrendDirection) {
        if (statsDates.length < 7) return TrendDirection.Stable;

        uint256 recentIndex = statsDates.length - 1;
        uint256 weekAgoIndex = statsDates.length - 7;

        uint256 recentStaked = dailyStats[statsDates[recentIndex]].totalStaked;
        uint256 weekAgoStaked = dailyStats[statsDates[weekAgoIndex]].totalStaked;

        if (recentStaked > weekAgoStaked * 105 / 100) { // 5%增长
            return TrendDirection.Increasing;
        } else if (recentStaked < weekAgoStaked * 95 / 100) { // 5%下降
            return TrendDirection.Decreasing;
        } else {
            return TrendDirection.Stable;
        }
    }

    function _calculateRetentionRate() internal view returns (uint256) {
        if (trackedUsers.length == 0) return 0;

        uint256 activeUsers = 0;
        uint256 cutoffTime = block.timestamp - 30 days;

        for (uint256 i = 0; i < trackedUsers.length; i++) {
            UserProfile storage profile = userProfiles[trackedUsers[i]];
            if (profile.lastActivityTime > cutoffTime) {
                activeUsers++;
            }
        }

        return (activeUsers * 10000) / trackedUsers.length;
    }

    function _calculateUtilizationRate() internal view returns (uint256) {
        // 简化计算：假设最大可质押量为当前的150%
        uint256 currentStaked = stakingPool.getTotalStaked();
        uint256 maxCapacity = currentStaked * 150 / 100;

        return maxCapacity > 0 ? (currentStaked * 10000) / maxCapacity : 0;
    }

    function _calculateOverallRisk() internal view returns (uint256) {
        return riskIndicators.overallRiskScore;
    }

    function _calculateConcentrationRisk() internal view returns (uint256) {
        if (trackedUsers.length == 0) return 0;

        uint256 totalStaked = stakingPool.getTotalStaked();
        if (totalStaked == 0) return 0;

        // 计算前10%用户的质押占比
        uint256 topUsersCount = Math.max(trackedUsers.length / 10, 1);
        uint256 topUsersStaked = 0;

        // 简化实现：假设按质押量排序
        for (uint256 i = 0; i < Math.min(topUsersCount, trackedUsers.length); i++) {
            (uint256 amount, , , , , , , , ) =
                stakingPool.getUserStakingInfo(trackedUsers[i]);
            topUsersStaked += amount;
        }

        uint256 concentration = (topUsersStaked * 10000) / totalStaked;

        // 集中度超过50%认为是高风险
        return concentration > 5000 ? concentration : 0;
    }

    function _calculateLiquidityRisk() internal view returns (uint256) {
        uint256 totalStaked = stakingPool.getTotalStaked();
        if (totalStaked == 0) return 0;

        uint256 unlockingAmount = 0;

        // 计算正在解锁的金额占比
        for (uint256 i = 0; i < trackedUsers.length; i++) {
            (uint256 amount, , , bool isUnlocking, , , , , ) =
                stakingPool.getUserStakingInfo(trackedUsers[i]);

            if (isUnlocking) {
                unlockingAmount += amount;
            }
        }

        uint256 unlockingRatio = (unlockingAmount * 10000) / totalStaked;

        // 解锁比例超过30%认为是高风险
        return unlockingRatio > 3000 ? unlockingRatio : 0;
    }

    function _calculateDurationRisk() internal view returns (uint256) {
        uint256 averageDuration = _calculateAverageStakeDuration();

        // 平均质押时长少于30天认为是高风险
        if (averageDuration < 30 days) {
            return 8000; // 80%风险
        } else if (averageDuration < 90 days) {
            return 5000; // 50%风险
        } else {
            return 2000; // 20%风险
        }
    }

    function _calculateRewardSustainability() internal pure returns (uint256) {
        // 简化实现：基于奖励池余额和分发速度
        // 实际应该计算奖励池可持续时间
        return 8000; // 80%可持续性
    }

    // 查询函数
    function getDailyStats(uint256 _date) external view returns (DailyStats memory) {
        return dailyStats[_date];
    }

    function getLevelStats(IStakingPool.StakingLevel _level) external view returns (LevelStats memory) {
        return levelStats[_level];
    }

    function getUserProfile(address _user) external view returns (UserProfile memory) {
        return userProfiles[_user];
    }

    function getPerformanceMetrics() external view returns (PerformanceMetrics memory) {
        return currentMetrics;
    }

    function getRiskIndicators() external view returns (RiskIndicators memory) {
        return riskIndicators;
    }

    function getTrackedUsersCount() external view returns (uint256) {
        return trackedUsers.length;
    }

    function getStatsDatesCount() external view returns (uint256) {
        return statsDates.length;
    }

    function getRecentStats(uint256 _days) external view returns (DailyStats[] memory) {
        require(_days > 0 && _days <= statsDates.length, "Invalid days");

        DailyStats[] memory recentStats = new DailyStats[](_days);
        uint256 startIndex = statsDates.length - _days;

        for (uint256 i = 0; i < _days; i++) {
            recentStats[i] = dailyStats[statsDates[startIndex + i]];
        }

        return recentStats;
    }

    // 管理函数
    function setThresholds(
        uint256 _whaleThreshold,
        uint256 _institutionThreshold,
        uint256 _riskThreshold
    ) external onlyRole(ADMIN_ROLE) {
        whaleThreshold = _whaleThreshold;
        institutionThreshold = _institutionThreshold;
        riskThreshold = _riskThreshold;
    }

    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
    }
}
