// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

/**
 * @title StakingRewards
 * @dev 专门处理质押奖励计算和分发的合约
 * @notice 这个合约实现了复杂的奖励计算逻辑和多种奖励分发机制
 * <AUTHOR> Team
 *
 * 功能特性:
 * - 多层级奖励计算：基础奖励、等级奖励、时间奖励、活动奖励
 * - 动态奖励调整：根据市场条件和池子状态动态调整奖励
 * - 奖励预测：为用户提供奖励预测功能
 * - 奖励历史：完整的奖励发放历史记录
 * - 奖励优化：Gas优化的奖励计算和批量分发
 */

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/math/Math.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

contract StakingRewards is AccessControl, ReentrancyGuard, Pausable {
    using SafeERC20 for IERC20;

    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant OPERATOR_ROLE = keccak256("OPERATOR_ROLE");
    bytes32 public constant REWARD_MANAGER_ROLE = keccak256("REWARD_MANAGER_ROLE");

    enum RewardType { Base, Level, Duration, Activity, Bonus, Special }
    enum StakingLevel { DingJi, ChengJi, YiJi, JiaJi, ShiJue, ShuangShiJue, ZhiZun }

    struct RewardConfig {
        uint256 baseRate;           // 基础奖励率 (基点)
        uint256 levelMultiplier;    // 等级倍数
        uint256 durationBonus;      // 时长奖励
        uint256 activityBonus;      // 活跃奖励
        bool isActive;
    }

    struct UserRewardInfo {
        uint256 totalEarned;        // 总获得奖励
        uint256 totalClaimed;       // 总已领取奖励
        uint256 pendingRewards;     // 待领取奖励
        uint256 lastClaimTime;      // 最后领取时间
        uint256 rewardDebt;         // 奖励债务
        uint256 bonusMultiplier;    // 奖励倍数
    }

    struct RewardPool {
        IERC20 rewardToken;         // 奖励代币
        uint256 totalRewards;       // 总奖励池
        uint256 distributedRewards; // 已分发奖励
        uint256 rewardPerSecond;    // 每秒奖励
        uint256 lastUpdateTime;     // 最后更新时间
        uint256 accRewardPerShare;  // 累积每股奖励
        bool isActive;
    }

    struct RewardHistory {
        address user;
        RewardType rewardType;
        uint256 amount;
        uint256 timestamp;
        uint256 stakingLevel;
    }

    // 状态变量
    address public stakingPool;
    mapping(address => RewardPool) public rewardPools;
    mapping(address => UserRewardInfo) public userRewards;
    mapping(StakingLevel => RewardConfig) public levelConfigs;
    
    RewardHistory[] public rewardHistory;
    mapping(address => uint256[]) public userRewardHistory;
    
    uint256 public totalStaked;
    uint256 public globalRewardMultiplier = 10000; // 100% = 10000
    uint256 public maxRewardMultiplier = 50000;    // 500% = 50000
    
    // 奖励预测相关
    mapping(address => uint256) public predictedDailyRewards;
    uint256 public rewardPredictionAccuracy = 9500; // 95%

    // 事件
    event RewardPoolAdded(address indexed token, uint256 rewardPerSecond);
    event RewardPoolUpdated(address indexed token, uint256 newRewardPerSecond);
    event RewardClaimed(address indexed user, address indexed token, uint256 amount);
    event RewardDistributed(address indexed user, RewardType rewardType, uint256 amount);
    event LevelConfigUpdated(StakingLevel indexed level, uint256 baseRate, uint256 multiplier);
    event GlobalMultiplierUpdated(uint256 oldMultiplier, uint256 newMultiplier);
    event RewardPredictionUpdated(address indexed user, uint256 dailyReward);

    constructor(address _stakingPool) {
        if (_stakingPool == address(0)) revert TokenErrors.ZeroAddress();
        
        stakingPool = _stakingPool;
        
        _setupRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _setupRole(ADMIN_ROLE, msg.sender);
        _setupRole(OPERATOR_ROLE, msg.sender);
        _setupRole(REWARD_MANAGER_ROLE, msg.sender);
        
        _initializeLevelConfigs();
    }

    function _initializeLevelConfigs() internal {
        // 丁级配置
        levelConfigs[StakingLevel.DingJi] = RewardConfig({
            baseRate: 500,      // 5%
            levelMultiplier: 12000,  // 1.2x
            durationBonus: 1000,     // 10%
            activityBonus: 500,      // 5%
            isActive: true
        });

        // 丙级配置
        levelConfigs[StakingLevel.ChengJi] = RewardConfig({
            baseRate: 600,      // 6%
            levelMultiplier: 13000,  // 1.3x
            durationBonus: 1200,     // 12%
            activityBonus: 600,      // 6%
            isActive: true
        });

        // 乙级配置
        levelConfigs[StakingLevel.YiJi] = RewardConfig({
            baseRate: 750,      // 7.5%
            levelMultiplier: 15000,  // 1.5x
            durationBonus: 1500,     // 15%
            activityBonus: 750,      // 7.5%
            isActive: true
        });

        // 甲级配置
        levelConfigs[StakingLevel.JiaJi] = RewardConfig({
            baseRate: 1000,     // 10%
            levelMultiplier: 19000,  // 1.9x
            durationBonus: 2000,     // 20%
            activityBonus: 1000,     // 10%
            isActive: true
        });

        // 十绝配置
        levelConfigs[StakingLevel.ShiJue] = RewardConfig({
            baseRate: 1250,     // 12.5%
            levelMultiplier: 22500,  // 2.25x
            durationBonus: 2500,     // 25%
            activityBonus: 1250,     // 12.5%
            isActive: true
        });

        // 双十绝配置
        levelConfigs[StakingLevel.ShuangShiJue] = RewardConfig({
            baseRate: 1500,     // 15%
            levelMultiplier: 30000,  // 3.0x
            durationBonus: 3000,     // 30%
            activityBonus: 1500,     // 15%
            isActive: true
        });

        // 至尊配置
        levelConfigs[StakingLevel.ZhiZun] = RewardConfig({
            baseRate: 2000,     // 20%
            levelMultiplier: 45000,  // 4.5x
            durationBonus: 4000,     // 40%
            activityBonus: 2000,     // 20%
            isActive: true
        });
    }

    function addRewardPool(
        address _rewardToken,
        uint256 _rewardPerSecond
    ) external onlyRole(REWARD_MANAGER_ROLE) {
        if (_rewardToken == address(0)) revert TokenErrors.ZeroAddress();
        if (_rewardPerSecond == 0) revert TokenErrors.ZeroAmount();

        RewardPool storage pool = rewardPools[_rewardToken];
        if (address(pool.rewardToken) != address(0)) revert TokenErrors.PoolAlreadyExists();

        pool.rewardToken = IERC20(_rewardToken);
        pool.rewardPerSecond = _rewardPerSecond;
        pool.lastUpdateTime = block.timestamp;
        pool.isActive = true;

        emit RewardPoolAdded(_rewardToken, _rewardPerSecond);
    }

    function updateRewardPool(
        address _rewardToken,
        uint256 _newRewardPerSecond
    ) external onlyRole(REWARD_MANAGER_ROLE) {
        RewardPool storage pool = rewardPools[_rewardToken];
        if (address(pool.rewardToken) == address(0)) revert TokenErrors.PoolNotFound();

        _updatePool(_rewardToken);
        pool.rewardPerSecond = _newRewardPerSecond;

        emit RewardPoolUpdated(_rewardToken, _newRewardPerSecond);
    }

    function _updatePool(address _rewardToken) internal {
        RewardPool storage pool = rewardPools[_rewardToken];
        
        if (block.timestamp <= pool.lastUpdateTime || totalStaked == 0) {
            pool.lastUpdateTime = block.timestamp;
            return;
        }

        uint256 timeElapsed = block.timestamp - pool.lastUpdateTime;
        uint256 reward = timeElapsed * pool.rewardPerSecond;
        
        pool.accRewardPerShare += (reward * 1e18) / totalStaked;
        pool.lastUpdateTime = block.timestamp;
        pool.totalRewards += reward;
    }

    function calculateUserReward(
        address /* _user */,
        address _rewardToken,
        uint256 _stakedAmount,
        StakingLevel _level,
        uint256 _stakingDuration
    ) external view returns (uint256) {
        RewardPool storage pool = rewardPools[_rewardToken];
        if (address(pool.rewardToken) == address(0) || !pool.isActive) {
            return 0;
        }

        RewardConfig storage config = levelConfigs[_level];
        if (!config.isActive) {
            return 0;
        }

        // 计算基础奖励
        uint256 baseReward = _stakedAmount * config.baseRate / 10000;
        
        // 应用等级倍数
        uint256 levelReward = baseReward * config.levelMultiplier / 10000;
        
        // 应用时长奖励
        uint256 durationMultiplier = _calculateDurationMultiplier(_stakingDuration, config.durationBonus);
        uint256 finalReward = levelReward * durationMultiplier / 10000;
        
        // 应用全局倍数
        finalReward = finalReward * globalRewardMultiplier / 10000;
        
        return finalReward;
    }

    function _calculateDurationMultiplier(uint256 _duration, uint256 _baseBonus) internal pure returns (uint256) {
        if (_duration < 30 days) return 10000; // 1.0x
        if (_duration < 90 days) return 10000 + _baseBonus / 4; // 基础奖励的25%
        if (_duration < 180 days) return 10000 + _baseBonus / 2; // 基础奖励的50%
        if (_duration < 365 days) return 10000 + (_baseBonus * 3) / 4; // 基础奖励的75%
        return 10000 + _baseBonus; // 完整基础奖励
    }

    function claimReward(address _rewardToken) external nonReentrant whenNotPaused {
        _updatePool(_rewardToken);

        UserRewardInfo storage userInfo = userRewards[msg.sender];
        RewardPool storage pool = rewardPools[_rewardToken];

        if (address(pool.rewardToken) == address(0)) revert TokenErrors.PoolNotFound();

        uint256 pending = userInfo.pendingRewards;
        if (pending == 0) revert TokenErrors.NoRewardToClaim();

        userInfo.pendingRewards = 0;
        userInfo.totalClaimed += pending;
        userInfo.lastClaimTime = block.timestamp;

        pool.distributedRewards += pending;

        // 记录奖励历史
        _recordRewardHistory(msg.sender, RewardType.Base, pending, StakingLevel.DingJi);

        pool.rewardToken.safeTransfer(msg.sender, pending);

        emit RewardClaimed(msg.sender, _rewardToken, pending);
    }

    function batchClaimRewards(address[] calldata _rewardTokens) external nonReentrant whenNotPaused {
        for (uint256 i = 0; i < _rewardTokens.length; i++) {
            address token = _rewardTokens[i];
            _updatePool(token);

            UserRewardInfo storage userInfo = userRewards[msg.sender];
            RewardPool storage pool = rewardPools[token];

            if (address(pool.rewardToken) == address(0)) continue;

            uint256 pending = userInfo.pendingRewards;
            if (pending == 0) continue;

            userInfo.pendingRewards = 0;
            userInfo.totalClaimed += pending;
            userInfo.lastClaimTime = block.timestamp;

            pool.distributedRewards += pending;

            _recordRewardHistory(msg.sender, RewardType.Base, pending, StakingLevel.DingJi);

            pool.rewardToken.safeTransfer(msg.sender, pending);

            emit RewardClaimed(msg.sender, token, pending);
        }
    }

    function distributeReward(
        address _user,
        address _rewardToken,
        uint256 _amount,
        RewardType _rewardType,
        StakingLevel _level
    ) external onlyRole(OPERATOR_ROLE) {
        if (_user == address(0)) revert TokenErrors.ZeroAddress();
        if (_amount == 0) revert TokenErrors.ZeroAmount();

        _updatePool(_rewardToken);

        UserRewardInfo storage userInfo = userRewards[_user];
        userInfo.pendingRewards += _amount;
        userInfo.totalEarned += _amount;

        _recordRewardHistory(_user, _rewardType, _amount, _level);

        emit RewardDistributed(_user, _rewardType, _amount);
    }

    function _recordRewardHistory(
        address _user,
        RewardType _rewardType,
        uint256 _amount,
        StakingLevel _level
    ) internal {
        RewardHistory memory record = RewardHistory({
            user: _user,
            rewardType: _rewardType,
            amount: _amount,
            timestamp: block.timestamp,
            stakingLevel: uint256(_level)
        });

        rewardHistory.push(record);
        userRewardHistory[_user].push(rewardHistory.length - 1);
    }

    function predictDailyReward(
        address /* _user */,
        uint256 _stakedAmount,
        StakingLevel _level
    ) external view returns (uint256) {
        RewardConfig storage config = levelConfigs[_level];
        if (!config.isActive) return 0;

        // 基础日奖励计算
        uint256 dailyBase = (_stakedAmount * config.baseRate) / 10000 / 365;

        // 应用等级倍数
        uint256 dailyReward = (dailyBase * config.levelMultiplier) / 10000;

        // 应用全局倍数
        dailyReward = (dailyReward * globalRewardMultiplier) / 10000;

        // 应用预测准确度
        return (dailyReward * rewardPredictionAccuracy) / 10000;
    }

    function updatePredictedReward(address _user, uint256 _dailyReward)
        external
        onlyRole(OPERATOR_ROLE)
    {
        predictedDailyRewards[_user] = _dailyReward;
        emit RewardPredictionUpdated(_user, _dailyReward);
    }

    function setGlobalRewardMultiplier(uint256 _multiplier)
        external
        onlyRole(ADMIN_ROLE)
    {
        if (_multiplier > maxRewardMultiplier) revert TokenErrors.InvalidOperation();

        uint256 oldMultiplier = globalRewardMultiplier;
        globalRewardMultiplier = _multiplier;

        emit GlobalMultiplierUpdated(oldMultiplier, _multiplier);
    }

    function updateLevelConfig(
        StakingLevel _level,
        uint256 _baseRate,
        uint256 _levelMultiplier,
        uint256 _durationBonus,
        uint256 _activityBonus
    ) external onlyRole(ADMIN_ROLE) {
        RewardConfig storage config = levelConfigs[_level];

        config.baseRate = _baseRate;
        config.levelMultiplier = _levelMultiplier;
        config.durationBonus = _durationBonus;
        config.activityBonus = _activityBonus;

        emit LevelConfigUpdated(_level, _baseRate, _levelMultiplier);
    }

    function getUserRewardInfo(address _user) external view returns (
        uint256 totalEarned,
        uint256 totalClaimed,
        uint256 pendingRewards,
        uint256 lastClaimTime,
        uint256 bonusMultiplier
    ) {
        UserRewardInfo storage info = userRewards[_user];
        return (
            info.totalEarned,
            info.totalClaimed,
            info.pendingRewards,
            info.lastClaimTime,
            info.bonusMultiplier
        );
    }

    function getUserRewardHistory(address _user) external view returns (uint256[] memory) {
        return userRewardHistory[_user];
    }

    function getRewardHistoryDetails(uint256 _index) external view returns (
        address user,
        RewardType rewardType,
        uint256 amount,
        uint256 timestamp,
        uint256 stakingLevel
    ) {
        if (_index >= rewardHistory.length) revert TokenErrors.IndexOutOfRange();

        RewardHistory storage record = rewardHistory[_index];
        return (
            record.user,
            record.rewardType,
            record.amount,
            record.timestamp,
            record.stakingLevel
        );
    }

    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
    }

    function emergencyWithdraw(address _token, uint256 _amount)
        external
        onlyRole(DEFAULT_ADMIN_ROLE)
    {
        if (_token == address(0)) revert TokenErrors.ZeroAddress();
        if (_amount == 0) revert TokenErrors.ZeroAmount();

        IERC20(_token).safeTransfer(msg.sender, _amount);
    }
}
