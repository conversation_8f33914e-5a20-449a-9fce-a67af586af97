# PXT Staking Contracts

这个目录包含了 PXT 代币质押系统的完整合约实现，提供了一个功能丰富、安全可靠的质押生态系统。

## 合约概览

### 核心合约

#### 1. StakingPool.sol
**主要质押池合约** - 系统的核心合约
- ✅ 7级质押等级系统（丁级到至尊）
- ✅ 灵活解锁机制（27天-3年）
- ✅ 随机奖励倍数系统
- ✅ 紧急提取功能
- ✅ 挖矿算力计算
- ✅ 完整的权限控制
- ✅ Gas优化实现

#### 2. StakingFactory.sol
**质押池工厂合约** - 管理多个质押池
- ✅ 创建不同类型的质押池（标准、特殊、促销）
- ✅ 池子生命周期管理
- ✅ 奖励分发器集成
- ✅ 池子状态监控

#### 3. RewardDistributor.sol
**奖励分发合约** - 管理奖励分发逻辑
- ✅ 多池奖励分配
- ✅ 奖励周期管理
- ✅ 奖励倍数系统
- ✅ 特殊活动支持
- ✅ 批量奖励分发

### 新增增强合约

#### 4. StakingRewards.sol ⭐ **新增**
**专业奖励计算合约** - 复杂奖励逻辑处理
- 🆕 多层级奖励计算（基础+等级+时间+活动）
- 🆕 动态奖励调整机制
- 🆕 奖励预测功能
- 🆕 完整奖励历史记录
- 🆕 批量奖励领取
- 🆕 Gas优化的奖励分发

**主要功能：**
```solidity
// 奖励计算
function calculateUserReward(address user, address token, uint256 amount, StakingLevel level, uint256 duration) external view returns (uint256);

// 奖励预测
function predictDailyReward(address user, uint256 amount, StakingLevel level) external view returns (uint256);

// 批量领取
function batchClaimRewards(address[] calldata tokens) external;
```

#### 5. StakingGovernance.sol ⭐ **新增**
**质押治理合约** - 去中心化治理机制
- 🆕 基于质押的投票权重系统
- 🆕 提案创建和投票机制
- 🆕 时间锁保护重要变更
- 🆕 紧急治理机制
- 🆕 多重签名保护

**治理流程：**
1. 提案创建 → 2. 投票期 → 3. 排队执行 → 4. 执行变更

**投票权重计算：**
- 基础权重：每个PXT = 1票
- 等级倍数：丁级1x → 至尊10x
- 最大权重限制：防止过度集中

#### 6. StakingAnalytics.sol ⭐ **新增**
**数据分析合约** - 全面的数据统计和分析
- 🆕 实时数据统计（TVL、用户数、奖励等）
- 🆕 历史趋势分析
- 🆕 用户行为画像
- 🆕 风险指标监控
- 🆕 性能指标计算

**分析维度：**
- 📊 日度统计：质押量、用户数、新增用户
- 📈 等级分析：各等级用户分布和收益
- 👤 用户画像：休闲、常规、大户、机构
- ⚠️ 风险评估：集中度、流动性、持续性

#### 7. StakingVault.sol ⭐ **新增**
**安全保管库合约** - 资金安全保护
- 🆕 多重签名保护
- 🆕 资金分类管理（质押、奖励、保险、紧急）
- 🆕 时间锁机制
- 🆕 紧急恢复功能
- 🆕 完整审计追踪
- 🆕 保险机制

**安全特性：**
- 🔐 多重签名：重要操作需要3/5签名
- ⏰ 时间锁：大额操作24小时延迟
- 🚨 紧急模式：紧急情况快速响应
- 📋 审计追踪：所有操作完整记录

## 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  StakingPool    │◄──►│ StakingFactory   │◄──►│ RewardDistributor│
│   (核心质押)     │    │   (池子管理)      │    │   (奖励分发)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         ▲                        ▲                        ▲
         │                        │                        │
         ▼                        ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ StakingRewards  │    │ StakingGovernance│    │ StakingAnalytics│
│  (奖励计算)      │    │   (治理投票)      │    │   (数据分析)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         ▲                        ▲                        ▲
         │                        │                        │
         └────────────────────────┼────────────────────────┘
                                  ▼
                        ┌─────────────────┐
                        │  StakingVault   │
                        │   (安全保管)     │
                        └─────────────────┘
```

## 质押等级系统

| 等级 | 中文名 | 门槛 | 固定倍数 | 随机倍数范围 | 投票权重 |
|------|--------|------|----------|--------------|----------|
| 0 | 丁级 | 100 PXT | 1.3x | 1.2x - 1.3x | 1x |
| 1 | 丙级 | 1,000 PXT | 1.4x | 1.3x - 1.4x | 2x |
| 2 | 乙级 | 5,000 PXT | 1.6x | 1.5x - 1.6x | 3x |
| 3 | 甲级 | 20,000 PXT | 2.0x | 1.9x - 2.0x | 5x |
| 4 | 十绝 | 100,000 PXT | 2.5x | 2.0x - 2.5x | 7x |
| 5 | 双十绝 | 250,000 PXT | 3.0x | 3.0x - 4.0x | 9x |
| 6 | 至尊 | 500,000 PXT | 5.0x | 4.0x - 6.0x | 10x |

## 时长倍数系统

| 质押时长 | 倍数 |
|----------|------|
| 30天 | 1.05x |
| 90天 | 1.10x |
| 180天 | 1.20x |
| 365天 | 1.35x |
| 730天 | 1.70x |
| 1095天 | 2.00x |

## 部署顺序

1. **部署基础合约**
   ```bash
   # 1. 部署 StakingPool (实现合约)
   # 2. 部署 RewardDistributor
   # 3. 部署 StakingFactory
   ```

2. **部署增强合约**
   ```bash
   # 4. 部署 StakingVault
   # 5. 部署 StakingRewards
   # 6. 部署 StakingAnalytics
   # 7. 部署 StakingGovernance
   ```

3. **配置和初始化**
   ```bash
   # 8. 设置合约间的关联关系
   # 9. 配置权限和参数
   # 10. 初始化奖励池和治理参数
   ```

## 安全特性

### 🔒 多层安全保护
- **重入攻击防护**：所有外部调用使用 ReentrancyGuard
- **权限控制**：基于 AccessControl 的细粒度权限管理
- **暂停机制**：紧急情况下可暂停合约操作
- **时间锁保护**：重要参数变更需要时间延迟

### 🛡️ 资金安全
- **多重签名**：大额操作需要多方确认
- **资金隔离**：不同用途资金分别管理
- **紧急恢复**：极端情况下的资金恢复机制
- **审计追踪**：所有操作完整记录

### ⚡ Gas优化
- **批量操作**：支持批量质押、领取、投票
- **存储优化**：合理的数据结构设计
- **循环优化**：使用 unchecked 进行安全的数学运算
- **事件优化**：合理的事件设计减少Gas消耗

## 使用示例

### 质押操作
```solidity
// 1. 质押代币
stakingPool.stake(1000 * 10**18);

// 2. 请求解锁
stakingPool.requestUnlock(500 * 10**18, 90 days);

// 3. 提取代币
stakingPool.withdraw(requestId);

// 4. 领取奖励
stakingRewards.claimReward(rewardToken);
```

### 治理操作
```solidity
// 1. 创建提案
uint256 proposalId = governance.propose(
    ProposalType.APRUpdate,
    "Increase Base APR",
    "Proposal to increase base APR from 5% to 6%",
    stakingPool,
    0,
    abi.encodeWithSignature("setBaseAPR(uint256)", 600)
);

// 2. 投票
governance.castVote(proposalId, VoteType.For);

// 3. 执行提案
governance.queue(proposalId);
governance.execute(proposalId);
```

### 数据查询
```solidity
// 1. 获取用户质押信息
(uint256 amount, uint256 startTime, StakingLevel level, , , , , , ) = 
    stakingPool.getUserStakingInfo(user);

// 2. 获取性能指标
PerformanceMetrics memory metrics = analytics.getPerformanceMetrics();

// 3. 获取风险指标
RiskIndicators memory risks = analytics.getRiskIndicators();
```

## 总结

这套质押合约系统提供了：

✅ **完整功能**：从基础质押到高级治理的全套功能
✅ **安全可靠**：多层安全保护和风险控制机制  
✅ **高度可扩展**：模块化设计支持功能扩展
✅ **用户友好**：直观的接口和丰富的查询功能
✅ **治理驱动**：去中心化治理确保系统演进
✅ **数据驱动**：全面的数据分析支持决策

这是一个生产级别的质押系统，可以支持大规模的代币质押和治理需求。
