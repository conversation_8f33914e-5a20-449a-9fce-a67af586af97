// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

/**
 * @title StakingVault
 * @dev 安全的质押资金保管库合约
 * @notice 这个合约提供安全的资金保管、多重签名保护和紧急恢复机制
 * <AUTHOR> Team
 *
 * 功能特性:
 * - 多重签名保护：重要操作需要多重签名确认
 * - 资金隔离：不同类型资金分别管理
 * - 紧急恢复：紧急情况下的资金恢复机制
 * - 审计追踪：完整的资金流动记录
 * - 时间锁保护：大额操作需要时间锁延迟
 * - 保险机制：资金保险和风险缓解
 */

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/TokenErrors.sol";

contract StakingVault is AccessControl, ReentrancyGuard, Pausable {
    using SafeERC20 for IERC20;
    using ECDSA for bytes32;

    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant GUARDIAN_ROLE = keccak256("GUARDIAN_ROLE");
    bytes32 public constant OPERATOR_ROLE = keccak256("OPERATOR_ROLE");
    bytes32 public constant EMERGENCY_ROLE = keccak256("EMERGENCY_ROLE");
    bytes32 public constant AUDITOR_ROLE = keccak256("AUDITOR_ROLE");

    enum VaultType { Staking, Rewards, Insurance, Emergency }
    enum TransactionType { Deposit, Withdraw, Transfer, Emergency }
    enum TransactionStatus { Pending, Approved, Executed, Cancelled, Expired }

    struct VaultInfo {
        VaultType vaultType;
        IERC20 token;
        uint256 balance;
        uint256 totalDeposited;
        uint256 totalWithdrawn;
        uint256 reserveRatio;
        bool isActive;
        bool isLocked;
    }

    struct Transaction {
        uint256 id;
        address initiator;
        VaultType fromVault;
        VaultType toVault;
        TransactionType txType;
        uint256 amount;
        address recipient;
        uint256 timestamp;
        uint256 executionTime;
        TransactionStatus status;
        uint256 requiredSignatures;
        uint256 currentSignatures;
        bytes32 dataHash;
        string description;
    }

    struct Signature {
        address signer;
        bytes signature;
        uint256 timestamp;
    }

    struct InsurancePolicy {
        uint256 id;
        uint256 coverage;
        uint256 premium;
        uint256 startTime;
        uint256 endTime;
        bool isActive;
        address provider;
    }

    struct AuditRecord {
        uint256 timestamp;
        address auditor;
        string action;
        bytes32 dataHash;
        bool isValid;
    }

    // 状态变量
    mapping(VaultType => VaultInfo) public vaults;
    mapping(uint256 => Transaction) public transactions;
    mapping(uint256 => mapping(address => bool)) public hasSignedTransaction;
    mapping(uint256 => Signature[]) public transactionSignatures;
    mapping(uint256 => InsurancePolicy) public insurancePolicies;
    
    AuditRecord[] public auditTrail;
    uint256 public transactionCount;
    uint256 public insurancePolicyCount;
    
    // 配置参数
    uint256 public requiredSignatures = 3;
    uint256 public maxSignatures = 5;
    uint256 public timelock = 24 hours;
    uint256 public emergencyTimelock = 1 hours;
    uint256 public maxSingleTransaction = 1000000 * 10**18; // 1M PXT
    
    // 紧急状态
    bool public emergencyMode;
    uint256 public emergencyActivatedTime;
    address public emergencyRecoveryAddress;

    // 事件
    event VaultCreated(VaultType indexed vaultType, address indexed token);
    event TransactionCreated(uint256 indexed txId, address indexed initiator, TransactionType txType);
    event TransactionSigned(uint256 indexed txId, address indexed signer);
    event TransactionExecuted(uint256 indexed txId, uint256 amount);
    event TransactionCancelled(uint256 indexed txId, address indexed canceller);
    event EmergencyModeActivated(address indexed activator);
    event EmergencyModeDeactivated(address indexed deactivator);
    event InsurancePolicyCreated(uint256 indexed policyId, uint256 coverage);
    event AuditRecordAdded(address indexed auditor, string action);
    event FundsRecovered(VaultType indexed vaultType, uint256 amount, address indexed recipient);

    constructor() {
        _setupRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _setupRole(ADMIN_ROLE, msg.sender);
        _setupRole(GUARDIAN_ROLE, msg.sender);
        _setupRole(OPERATOR_ROLE, msg.sender);
        _setupRole(EMERGENCY_ROLE, msg.sender);
        _setupRole(AUDITOR_ROLE, msg.sender);
    }

    function createVault(
        VaultType _vaultType,
        address _token,
        uint256 _reserveRatio
    ) external onlyRole(ADMIN_ROLE) {
        if (_token == address(0)) revert TokenErrors.ZeroAddress();
        if (address(vaults[_vaultType].token) != address(0)) revert TokenErrors.PoolAlreadyExists();
        if (_reserveRatio > 10000) revert TokenErrors.InvalidPercentage();

        vaults[_vaultType] = VaultInfo({
            vaultType: _vaultType,
            token: IERC20(_token),
            balance: 0,
            totalDeposited: 0,
            totalWithdrawn: 0,
            reserveRatio: _reserveRatio,
            isActive: true,
            isLocked: false
        });

        _addAuditRecord("VAULT_CREATED", keccak256(abi.encodePacked(_vaultType, _token)));
        emit VaultCreated(_vaultType, _token);
    }

    function deposit(
        VaultType _vaultType,
        uint256 _amount
    ) external nonReentrant whenNotPaused {
        VaultInfo storage vault = vaults[_vaultType];
        if (address(vault.token) == address(0)) revert TokenErrors.PoolNotFound();
        if (!vault.isActive || vault.isLocked) revert TokenErrors.PoolInactive();
        if (_amount == 0) revert TokenErrors.ZeroAmount();

        vault.token.safeTransferFrom(msg.sender, address(this), _amount);
        
        vault.balance += _amount;
        vault.totalDeposited += _amount;

        _addAuditRecord("DEPOSIT", keccak256(abi.encodePacked(_vaultType, _amount, msg.sender)));
    }

    function initiateWithdrawal(
        VaultType _vaultType,
        uint256 _amount,
        address _recipient,
        string memory _description
    ) external onlyRole(OPERATOR_ROLE) returns (uint256) {
        VaultInfo storage vault = vaults[_vaultType];
        if (address(vault.token) == address(0)) revert TokenErrors.PoolNotFound();
        if (_amount == 0) revert TokenErrors.ZeroAmount();
        if (_amount > vault.balance) revert TokenErrors.InsufficientBalance();
        if (_recipient == address(0)) revert TokenErrors.ZeroAddress();

        uint256 txId = ++transactionCount;
        uint256 executionTime = block.timestamp + timelock;
        
        // 大额交易需要更多签名
        uint256 requiredSigs = _amount > maxSingleTransaction ? maxSignatures : requiredSignatures;

        transactions[txId] = Transaction({
            id: txId,
            initiator: msg.sender,
            fromVault: _vaultType,
            toVault: VaultType.Staking, // 默认值
            txType: TransactionType.Withdraw,
            amount: _amount,
            recipient: _recipient,
            timestamp: block.timestamp,
            executionTime: executionTime,
            status: TransactionStatus.Pending,
            requiredSignatures: requiredSigs,
            currentSignatures: 0,
            dataHash: keccak256(abi.encodePacked(_vaultType, _amount, _recipient)),
            description: _description
        });

        _addAuditRecord("WITHDRAWAL_INITIATED", transactions[txId].dataHash);
        emit TransactionCreated(txId, msg.sender, TransactionType.Withdraw);
        
        return txId;
    }

    function signTransaction(uint256 _txId) external onlyRole(GUARDIAN_ROLE) {
        Transaction storage transaction = transactions[_txId];
        if (transaction.id == 0) revert TokenErrors.InvalidRequestId(_txId);
        if (transaction.status != TransactionStatus.Pending) revert TokenErrors.RequestAlreadyProcessed(_txId);
        if (hasSignedTransaction[_txId][msg.sender]) revert TokenErrors.AlreadyVoted();

        hasSignedTransaction[_txId][msg.sender] = true;
        transaction.currentSignatures++;

        // 创建签名记录
        bytes32 messageHash = keccak256(abi.encodePacked(_txId, transaction.dataHash));
        bytes memory signature = abi.encodePacked(messageHash); // 简化签名

        transactionSignatures[_txId].push(Signature({
            signer: msg.sender,
            signature: signature,
            timestamp: block.timestamp
        }));

        if (transaction.currentSignatures >= transaction.requiredSignatures) {
            transaction.status = TransactionStatus.Approved;
        }

        _addAuditRecord("TRANSACTION_SIGNED", transaction.dataHash);
        emit TransactionSigned(_txId, msg.sender);
    }

    function executeTransaction(uint256 _txId) external nonReentrant onlyRole(OPERATOR_ROLE) {
        Transaction storage transaction = transactions[_txId];
        if (transaction.id == 0) revert TokenErrors.InvalidRequestId(_txId);
        if (transaction.status != TransactionStatus.Approved) revert TokenErrors.InvalidProposalState();
        if (block.timestamp < transaction.executionTime) revert TokenErrors.ExecutionDelayNotMet();

        VaultInfo storage vault = vaults[transaction.fromVault];
        if (transaction.amount > vault.balance) revert TokenErrors.InsufficientBalance();

        transaction.status = TransactionStatus.Executed;
        vault.balance -= transaction.amount;
        vault.totalWithdrawn += transaction.amount;

        if (transaction.txType == TransactionType.Withdraw) {
            vault.token.safeTransfer(transaction.recipient, transaction.amount);
        } else if (transaction.txType == TransactionType.Transfer) {
            VaultInfo storage toVault = vaults[transaction.toVault];
            toVault.balance += transaction.amount;
        }

        _addAuditRecord("TRANSACTION_EXECUTED", transaction.dataHash);
        emit TransactionExecuted(_txId, transaction.amount);
    }

    function cancelTransaction(uint256 _txId) external {
        Transaction storage transaction = transactions[_txId];
        if (transaction.id == 0) revert TokenErrors.InvalidRequestId(_txId);
        if (transaction.status == TransactionStatus.Executed) revert TokenErrors.RequestAlreadyProcessed(_txId);

        // 只有发起者或管理员可以取消
        if (msg.sender != transaction.initiator && !hasRole(ADMIN_ROLE, msg.sender)) {
            revert TokenErrors.Unauthorized();
        }

        transaction.status = TransactionStatus.Cancelled;

        _addAuditRecord("TRANSACTION_CANCELLED", transaction.dataHash);
        emit TransactionCancelled(_txId, msg.sender);
    }

    function activateEmergencyMode() external onlyRole(EMERGENCY_ROLE) {
        if (emergencyMode) revert TokenErrors.EmergencyModeActive();
        
        emergencyMode = true;
        emergencyActivatedTime = block.timestamp;
        
        // 锁定所有保险库
        for (uint256 i = 0; i < 4; i++) {
            VaultType vaultType = VaultType(i);
            if (address(vaults[vaultType].token) != address(0)) {
                vaults[vaultType].isLocked = true;
            }
        }

        _addAuditRecord("EMERGENCY_ACTIVATED", keccak256(abi.encodePacked(block.timestamp)));
        emit EmergencyModeActivated(msg.sender);
    }

    function deactivateEmergencyMode() external onlyRole(ADMIN_ROLE) {
        if (!emergencyMode) revert TokenErrors.EmergencyModeInactive();
        
        emergencyMode = false;
        
        // 解锁所有保险库
        for (uint256 i = 0; i < 4; i++) {
            VaultType vaultType = VaultType(i);
            if (address(vaults[vaultType].token) != address(0)) {
                vaults[vaultType].isLocked = false;
            }
        }

        _addAuditRecord("EMERGENCY_DEACTIVATED", keccak256(abi.encodePacked(block.timestamp)));
        emit EmergencyModeDeactivated(msg.sender);
    }

    function emergencyRecovery(
        VaultType _vaultType,
        uint256 _amount,
        address _recipient
    ) external onlyRole(EMERGENCY_ROLE) {
        if (!emergencyMode) revert TokenErrors.NotInEmergencyMode();
        if (block.timestamp < emergencyActivatedTime + emergencyTimelock) {
            revert TokenErrors.ExecutionDelayNotMet();
        }

        VaultInfo storage vault = vaults[_vaultType];
        if (address(vault.token) == address(0)) revert TokenErrors.PoolNotFound();
        if (_amount > vault.balance) revert TokenErrors.InsufficientBalance();
        if (_recipient == address(0)) revert TokenErrors.ZeroAddress();

        vault.balance -= _amount;
        vault.token.safeTransfer(_recipient, _amount);

        _addAuditRecord("EMERGENCY_RECOVERY", keccak256(abi.encodePacked(_vaultType, _amount, _recipient)));
        emit FundsRecovered(_vaultType, _amount, _recipient);
    }

    function createInsurancePolicy(
        uint256 _coverage,
        uint256 _premium,
        uint256 _duration,
        address _provider
    ) external onlyRole(ADMIN_ROLE) returns (uint256) {
        if (_coverage == 0) revert TokenErrors.ZeroAmount();
        if (_provider == address(0)) revert TokenErrors.ZeroAddress();

        uint256 policyId = ++insurancePolicyCount;

        insurancePolicies[policyId] = InsurancePolicy({
            id: policyId,
            coverage: _coverage,
            premium: _premium,
            startTime: block.timestamp,
            endTime: block.timestamp + _duration,
            isActive: true,
            provider: _provider
        });

        _addAuditRecord("INSURANCE_CREATED", keccak256(abi.encodePacked(policyId, _coverage)));
        emit InsurancePolicyCreated(policyId, _coverage);

        return policyId;
    }

    function _addAuditRecord(string memory _action, bytes32 _dataHash) internal {
        auditTrail.push(AuditRecord({
            timestamp: block.timestamp,
            auditor: msg.sender,
            action: _action,
            dataHash: _dataHash,
            isValid: true
        }));

        emit AuditRecordAdded(msg.sender, _action);
    }

    // 查询函数
    function getVaultInfo(VaultType _vaultType) external view returns (VaultInfo memory) {
        return vaults[_vaultType];
    }

    function getTransaction(uint256 _txId) external view returns (Transaction memory) {
        return transactions[_txId];
    }

    function getTransactionSignatures(uint256 _txId) external view returns (Signature[] memory) {
        return transactionSignatures[_txId];
    }

    function getInsurancePolicy(uint256 _policyId) external view returns (InsurancePolicy memory) {
        return insurancePolicies[_policyId];
    }

    function getAuditTrail() external view returns (AuditRecord[] memory) {
        return auditTrail;
    }

    function getAuditRecord(uint256 _index) external view returns (AuditRecord memory) {
        if (_index >= auditTrail.length) revert TokenErrors.IndexOutOfRange();
        return auditTrail[_index];
    }

    function getTotalBalance() external view returns (uint256) {
        uint256 total = 0;
        for (uint256 i = 0; i < 4; i++) {
            VaultType vaultType = VaultType(i);
            total += vaults[vaultType].balance;
        }
        return total;
    }

    function getVaultBalance(VaultType _vaultType) external view returns (uint256) {
        return vaults[_vaultType].balance;
    }

    function isTransactionReady(uint256 _txId) external view returns (bool) {
        Transaction storage transaction = transactions[_txId];
        return transaction.status == TransactionStatus.Approved &&
               block.timestamp >= transaction.executionTime;
    }

    function getPendingTransactions() external view returns (uint256[] memory) {
        uint256 count = 0;

        // 计算待处理交易数量
        for (uint256 i = 1; i <= transactionCount; i++) {
            if (transactions[i].status == TransactionStatus.Pending ||
                transactions[i].status == TransactionStatus.Approved) {
                count++;
            }
        }

        uint256[] memory pending = new uint256[](count);
        uint256 index = 0;

        for (uint256 i = 1; i <= transactionCount; i++) {
            if (transactions[i].status == TransactionStatus.Pending ||
                transactions[i].status == TransactionStatus.Approved) {
                pending[index] = i;
                index++;
            }
        }

        return pending;
    }

    // 管理函数
    function setRequiredSignatures(uint256 _required) external onlyRole(ADMIN_ROLE) {
        if (_required == 0 || _required > maxSignatures) revert TokenErrors.InvalidOperation();
        requiredSignatures = _required;
    }

    function setTimelock(uint256 _timelock) external onlyRole(ADMIN_ROLE) {
        if (_timelock > 7 days) revert TokenErrors.InvalidTimeRange();
        timelock = _timelock;
    }

    function setMaxSingleTransaction(uint256 _maxAmount) external onlyRole(ADMIN_ROLE) {
        maxSingleTransaction = _maxAmount;
    }

    function setEmergencyRecoveryAddress(address _recoveryAddress) external onlyRole(ADMIN_ROLE) {
        if (_recoveryAddress == address(0)) revert TokenErrors.ZeroAddress();
        emergencyRecoveryAddress = _recoveryAddress;
    }

    function lockVault(VaultType _vaultType) external onlyRole(ADMIN_ROLE) {
        VaultInfo storage vault = vaults[_vaultType];
        if (address(vault.token) == address(0)) revert TokenErrors.PoolNotFound();
        vault.isLocked = true;
    }

    function unlockVault(VaultType _vaultType) external onlyRole(ADMIN_ROLE) {
        VaultInfo storage vault = vaults[_vaultType];
        if (address(vault.token) == address(0)) revert TokenErrors.PoolNotFound();
        vault.isLocked = false;
    }

    function setVaultReserveRatio(VaultType _vaultType, uint256 _ratio) external onlyRole(ADMIN_ROLE) {
        if (_ratio > 10000) revert TokenErrors.InvalidPercentage();
        VaultInfo storage vault = vaults[_vaultType];
        if (address(vault.token) == address(0)) revert TokenErrors.PoolNotFound();
        vault.reserveRatio = _ratio;
    }

    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
    }

    // 批量操作
    function batchSignTransactions(uint256[] calldata _txIds) external onlyRole(GUARDIAN_ROLE) {
        for (uint256 i = 0; i < _txIds.length; i++) {
            uint256 txId = _txIds[i];
            Transaction storage transaction = transactions[txId];

            // 检查是否可以签名
            if (transaction.id == 0) continue;
            if (transaction.status != TransactionStatus.Pending) continue;
            if (hasSignedTransaction[txId][msg.sender]) continue;

            // 执行签名逻辑
            hasSignedTransaction[txId][msg.sender] = true;
            transaction.currentSignatures++;

            // 创建签名记录
            bytes32 messageHash = keccak256(abi.encodePacked(txId, transaction.dataHash));
            bytes memory signature = abi.encodePacked(messageHash);

            transactionSignatures[txId].push(Signature({
                signer: msg.sender,
                signature: signature,
                timestamp: block.timestamp
            }));

            if (transaction.currentSignatures >= transaction.requiredSignatures) {
                transaction.status = TransactionStatus.Approved;
            }

            _addAuditRecord("TRANSACTION_SIGNED", transaction.dataHash);
            emit TransactionSigned(txId, msg.sender);
        }
    }

    function batchExecuteTransactions(uint256[] calldata _txIds) external nonReentrant onlyRole(OPERATOR_ROLE) {
        for (uint256 i = 0; i < _txIds.length; i++) {
            uint256 txId = _txIds[i];
            Transaction storage transaction = transactions[txId];

            // 检查是否可以执行
            if (transaction.id == 0) continue;
            if (transaction.status != TransactionStatus.Approved) continue;
            if (block.timestamp < transaction.executionTime) continue;

            VaultInfo storage vault = vaults[transaction.fromVault];
            if (transaction.amount > vault.balance) continue;

            // 执行交易逻辑
            transaction.status = TransactionStatus.Executed;
            vault.balance -= transaction.amount;
            vault.totalWithdrawn += transaction.amount;

            if (transaction.txType == TransactionType.Withdraw) {
                vault.token.safeTransfer(transaction.recipient, transaction.amount);
            } else if (transaction.txType == TransactionType.Transfer) {
                VaultInfo storage toVault = vaults[transaction.toVault];
                toVault.balance += transaction.amount;
            }

            _addAuditRecord("TRANSACTION_EXECUTED", transaction.dataHash);
            emit TransactionExecuted(txId, transaction.amount);
        }
    }

    // 统计函数
    function getVaultStatistics(VaultType _vaultType) external view returns (
        uint256 currentBalance,
        uint256 totalDeposited,
        uint256 totalWithdrawn,
        uint256 netFlow,
        uint256 utilizationRate
    ) {
        VaultInfo storage vault = vaults[_vaultType];

        currentBalance = vault.balance;
        totalDeposited = vault.totalDeposited;
        totalWithdrawn = vault.totalWithdrawn;
        netFlow = totalDeposited > totalWithdrawn ?
                  totalDeposited - totalWithdrawn : 0;
        utilizationRate = totalDeposited > 0 ?
                         (totalWithdrawn * 10000) / totalDeposited : 0;
    }

    function getTransactionStatistics() external view returns (
        uint256 totalTransactions,
        uint256 pendingTransactions,
        uint256 executedTransactions,
        uint256 cancelledTransactions
    ) {
        totalTransactions = transactionCount;

        for (uint256 i = 1; i <= transactionCount; i++) {
            TransactionStatus status = transactions[i].status;
            if (status == TransactionStatus.Pending || status == TransactionStatus.Approved) {
                pendingTransactions++;
            } else if (status == TransactionStatus.Executed) {
                executedTransactions++;
            } else if (status == TransactionStatus.Cancelled) {
                cancelledTransactions++;
            }
        }
    }

    // 接收ETH
    receive() external payable {
        // 允许接收ETH用于gas费用
    }

    // 紧急提取ETH
    function emergencyWithdrawETH(uint256 _amount) external onlyRole(EMERGENCY_ROLE) {
        if (_amount > address(this).balance) revert TokenErrors.InsufficientBalance();
        payable(msg.sender).transfer(_amount);
    }
}
