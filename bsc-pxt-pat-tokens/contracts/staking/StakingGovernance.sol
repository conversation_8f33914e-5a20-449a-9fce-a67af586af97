// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

/**
 * @title StakingGovernance
 * @dev 质押治理合约 - 管理质押相关的治理决策
 * @notice 这个合约实现了基于质押的治理机制和参数调整
 * <AUTHOR> Team
 *
 * 功能特性:
 * - 质押参数治理：通过投票调整质押参数
 * - 提案系统：质押用户可以提交和投票治理提案
 * - 权重投票：基于质押数量和等级的投票权重
 * - 时间锁：重要参数变更需要时间锁保护
 * - 紧急治理：紧急情况下的快速决策机制
 */

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/structs/EnumerableSet.sol";
import "../interfaces/TokenErrors.sol";

interface IStakingPool {
    enum StakingLevel { Ding<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> }
    
    function getUserStakingInfo(address _user) external view returns (
        uint256 amount,
        uint256 startTime,
        StakingLevel level,
        bool isUnlocking,
        uint256 unlockTime,
        uint256 requestedDuration,
        uint256 pendingReward,
        uint256 randomSeed,
        uint256 currentRandomMultiplier
    );
    
    function setBaseAPR(uint256 _baseAPR) external;
    function setMinStakeAmount(uint256 _minStakeAmount) external;
    function setMaxUnlockRequests(uint256 _maxRequests) external;
}

contract StakingGovernance is AccessControl, ReentrancyGuard, Pausable {
    using EnumerableSet for EnumerableSet.UintSet;

    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant PROPOSER_ROLE = keccak256("PROPOSER_ROLE");
    bytes32 public constant EXECUTOR_ROLE = keccak256("EXECUTOR_ROLE");
    bytes32 public constant EMERGENCY_ROLE = keccak256("EMERGENCY_ROLE");

    enum ProposalType { 
        ParameterChange,    // 参数变更
        APRUpdate,         // APR更新
        ThresholdUpdate,   // 阈值更新
        Emergency,         // 紧急提案
        FeatureToggle      // 功能开关
    }

    enum ProposalStatus { 
        Pending,           // 待投票
        Active,            // 投票中
        Succeeded,         // 投票通过
        Defeated,          // 投票失败
        Queued,            // 排队执行
        Executed,          // 已执行
        Cancelled,         // 已取消
        Expired            // 已过期
    }

    enum VoteType { Against, For, Abstain }

    struct Proposal {
        uint256 id;
        address proposer;
        ProposalType proposalType;
        string title;
        string description;
        bytes callData;
        address target;
        uint256 value;
        uint256 startTime;
        uint256 endTime;
        uint256 executionTime;
        ProposalStatus status;
        uint256 forVotes;
        uint256 againstVotes;
        uint256 abstainVotes;
        uint256 totalVotingPower;
        bool executed;
    }

    struct Vote {
        bool hasVoted;
        VoteType vote;
        uint256 weight;
        uint256 timestamp;
    }

    struct GovernanceConfig {
        uint256 votingDelay;           // 投票延迟时间
        uint256 votingPeriod;          // 投票持续时间
        uint256 proposalThreshold;     // 提案门槛
        uint256 quorumThreshold;       // 法定人数门槛
        uint256 executionDelay;        // 执行延迟时间
        uint256 gracePeriod;           // 宽限期
        bool emergencyMode;            // 紧急模式
    }

    // 状态变量
    IStakingPool public stakingPool;
    GovernanceConfig public config;
    
    uint256 public proposalCount;
    mapping(uint256 => Proposal) public proposals;
    mapping(uint256 => mapping(address => Vote)) public votes;
    mapping(address => uint256) public latestProposalIds;
    
    EnumerableSet.UintSet private activeProposals;
    EnumerableSet.UintSet private queuedProposals;
    
    // 投票权重配置
    mapping(IStakingPool.StakingLevel => uint256) public levelVotingWeights;
    uint256 public baseVotingWeight = 1;
    uint256 public maxVotingWeight = 10;

    // 事件
    event ProposalCreated(
        uint256 indexed proposalId,
        address indexed proposer,
        ProposalType indexed proposalType,
        string title
    );
    event VoteCast(
        address indexed voter,
        uint256 indexed proposalId,
        VoteType vote,
        uint256 weight
    );
    event ProposalQueued(uint256 indexed proposalId, uint256 executionTime);
    event ProposalExecuted(uint256 indexed proposalId);
    event ProposalCancelled(uint256 indexed proposalId);
    event GovernanceConfigUpdated(string parameter, uint256 oldValue, uint256 newValue);
    event EmergencyModeToggled(bool enabled);

    constructor(address _stakingPool) {
        if (_stakingPool == address(0)) revert TokenErrors.ZeroAddress();
        
        stakingPool = IStakingPool(_stakingPool);
        
        _setupRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _setupRole(ADMIN_ROLE, msg.sender);
        _setupRole(PROPOSER_ROLE, msg.sender);
        _setupRole(EXECUTOR_ROLE, msg.sender);
        _setupRole(EMERGENCY_ROLE, msg.sender);
        
        // 初始化治理配置
        config = GovernanceConfig({
            votingDelay: 1 days,
            votingPeriod: 7 days,
            proposalThreshold: 1000 * 10**18, // 1000 PXT
            quorumThreshold: 4000, // 40%
            executionDelay: 2 days,
            gracePeriod: 14 days,
            emergencyMode: false
        });
        
        _initializeVotingWeights();
    }

    function _initializeVotingWeights() internal {
        levelVotingWeights[IStakingPool.StakingLevel.DingJi] = 1;
        levelVotingWeights[IStakingPool.StakingLevel.ChengJi] = 2;
        levelVotingWeights[IStakingPool.StakingLevel.YiJi] = 3;
        levelVotingWeights[IStakingPool.StakingLevel.JiaJi] = 5;
        levelVotingWeights[IStakingPool.StakingLevel.ShiJue] = 7;
        levelVotingWeights[IStakingPool.StakingLevel.ShuangShiJue] = 9;
        levelVotingWeights[IStakingPool.StakingLevel.ZhiZun] = 10;
    }

    function propose(
        ProposalType _proposalType,
        string memory _title,
        string memory _description,
        address _target,
        uint256 _value,
        bytes memory _callData
    ) external whenNotPaused returns (uint256) {
        // 检查提案权限
        if (!hasRole(PROPOSER_ROLE, msg.sender)) {
            uint256 votingPower = getVotingPower(msg.sender);
            if (votingPower < config.proposalThreshold) {
                revert TokenErrors.InsufficientVotingPower();
            }
        }

        // 检查是否有活跃提案
        if (latestProposalIds[msg.sender] != 0) {
            ProposalStatus status = proposals[latestProposalIds[msg.sender]].status;
            if (status == ProposalStatus.Active || status == ProposalStatus.Pending) {
                revert TokenErrors.VotingActive();
            }
        }

        uint256 proposalId = ++proposalCount;
        uint256 startTime = block.timestamp + config.votingDelay;
        uint256 endTime = startTime + config.votingPeriod;

        proposals[proposalId] = Proposal({
            id: proposalId,
            proposer: msg.sender,
            proposalType: _proposalType,
            title: _title,
            description: _description,
            callData: _callData,
            target: _target,
            value: _value,
            startTime: startTime,
            endTime: endTime,
            executionTime: 0,
            status: ProposalStatus.Pending,
            forVotes: 0,
            againstVotes: 0,
            abstainVotes: 0,
            totalVotingPower: 0,
            executed: false
        });

        latestProposalIds[msg.sender] = proposalId;
        activeProposals.add(proposalId);

        emit ProposalCreated(proposalId, msg.sender, _proposalType, _title);
        
        return proposalId;
    }

    function castVote(uint256 _proposalId, VoteType _vote) external nonReentrant whenNotPaused {
        Proposal storage proposal = proposals[_proposalId];
        
        if (proposal.id == 0) revert TokenErrors.ProposalNotFound();
        if (block.timestamp < proposal.startTime) revert TokenErrors.VotingNotStarted();
        if (block.timestamp > proposal.endTime) revert TokenErrors.VotingEnded();
        if (votes[_proposalId][msg.sender].hasVoted) revert TokenErrors.AlreadyVoted();

        uint256 weight = getVotingPower(msg.sender);
        if (weight == 0) revert TokenErrors.InsufficientVotingPower();

        votes[_proposalId][msg.sender] = Vote({
            hasVoted: true,
            vote: _vote,
            weight: weight,
            timestamp: block.timestamp
        });

        if (_vote == VoteType.For) {
            proposal.forVotes += weight;
        } else if (_vote == VoteType.Against) {
            proposal.againstVotes += weight;
        } else {
            proposal.abstainVotes += weight;
        }

        proposal.totalVotingPower += weight;
        proposal.status = ProposalStatus.Active;

        emit VoteCast(msg.sender, _proposalId, _vote, weight);
    }

    function getVotingPower(address _user) public view returns (uint256) {
        (uint256 amount, , IStakingPool.StakingLevel level, , , , , , ) =
            stakingPool.getUserStakingInfo(_user);

        if (amount == 0) return 0;

        uint256 baseWeight = amount / 10**18; // 每个PXT = 1个基础权重
        uint256 levelMultiplier = levelVotingWeights[level];

        uint256 totalWeight = baseWeight * levelMultiplier;

        // 限制最大投票权重
        if (totalWeight > maxVotingWeight * amount / 10**18) {
            totalWeight = maxVotingWeight * amount / 10**18;
        }

        return totalWeight;
    }

    function queue(uint256 _proposalId) external onlyRole(EXECUTOR_ROLE) {
        Proposal storage proposal = proposals[_proposalId];

        if (proposal.id == 0) revert TokenErrors.ProposalNotFound();
        if (proposal.status != ProposalStatus.Succeeded) revert TokenErrors.InvalidProposalState();

        uint256 executionTime = block.timestamp + config.executionDelay;
        proposal.executionTime = executionTime;
        proposal.status = ProposalStatus.Queued;

        queuedProposals.add(_proposalId);
        activeProposals.remove(_proposalId);

        emit ProposalQueued(_proposalId, executionTime);
    }

    function execute(uint256 _proposalId) external payable onlyRole(EXECUTOR_ROLE) {
        Proposal storage proposal = proposals[_proposalId];

        if (proposal.id == 0) revert TokenErrors.ProposalNotFound();
        if (proposal.status != ProposalStatus.Queued) revert TokenErrors.InvalidProposalState();
        if (block.timestamp < proposal.executionTime) revert TokenErrors.ExecutionDelayNotMet();
        if (block.timestamp > proposal.executionTime + config.gracePeriod) {
            proposal.status = ProposalStatus.Expired;
            revert TokenErrors.ProposalEnded();
        }

        proposal.status = ProposalStatus.Executed;
        proposal.executed = true;

        queuedProposals.remove(_proposalId);

        // 执行提案
        if (proposal.target != address(0)) {
            (bool success, ) = proposal.target.call{value: proposal.value}(proposal.callData);
            if (!success) revert TokenErrors.ExecutionFailed();
        }

        emit ProposalExecuted(_proposalId);
    }

    function cancel(uint256 _proposalId) external {
        Proposal storage proposal = proposals[_proposalId];

        if (proposal.id == 0) revert TokenErrors.ProposalNotFound();
        if (proposal.executed) revert TokenErrors.ProposalExecuted();

        // 只有提案者或管理员可以取消
        if (msg.sender != proposal.proposer && !hasRole(ADMIN_ROLE, msg.sender)) {
            revert TokenErrors.Unauthorized();
        }

        proposal.status = ProposalStatus.Cancelled;

        activeProposals.remove(_proposalId);
        queuedProposals.remove(_proposalId);

        emit ProposalCancelled(_proposalId);
    }

    function updateProposalStatus(uint256 _proposalId) external {
        Proposal storage proposal = proposals[_proposalId];

        if (proposal.id == 0) revert TokenErrors.ProposalNotFound();
        if (proposal.status != ProposalStatus.Active) return;
        if (block.timestamp <= proposal.endTime) return;

        // 计算投票结果
        uint256 totalVotes = proposal.forVotes + proposal.againstVotes + proposal.abstainVotes;
        uint256 quorum = (totalVotes * 10000) / getTotalVotingPower();

        if (quorum >= config.quorumThreshold && proposal.forVotes > proposal.againstVotes) {
            proposal.status = ProposalStatus.Succeeded;
        } else {
            proposal.status = ProposalStatus.Defeated;
        }

        activeProposals.remove(_proposalId);
    }

    function getTotalVotingPower() public pure returns (uint256) {
        // 这里应该从质押池获取总质押量并计算总投票权重
        // 简化实现，实际应该遍历所有质押用户
        return 1000000 * 10**18; // 临时值
    }

    function emergencyExecute(uint256 _proposalId) external onlyRole(EMERGENCY_ROLE) {
        if (!config.emergencyMode) revert TokenErrors.NotInEmergencyMode();

        Proposal storage proposal = proposals[_proposalId];

        if (proposal.id == 0) revert TokenErrors.ProposalNotFound();
        if (proposal.proposalType != ProposalType.Emergency) revert TokenErrors.InvalidProposalState();

        proposal.status = ProposalStatus.Executed;
        proposal.executed = true;

        if (proposal.target != address(0)) {
            (bool success, ) = proposal.target.call{value: proposal.value}(proposal.callData);
            if (!success) revert TokenErrors.ExecutionFailed();
        }

        emit ProposalExecuted(_proposalId);
    }

    function setEmergencyMode(bool _enabled) external onlyRole(EMERGENCY_ROLE) {
        config.emergencyMode = _enabled;
        emit EmergencyModeToggled(_enabled);
    }

    function updateGovernanceConfig(
        uint256 _votingDelay,
        uint256 _votingPeriod,
        uint256 _proposalThreshold,
        uint256 _quorumThreshold,
        uint256 _executionDelay,
        uint256 _gracePeriod
    ) external onlyRole(ADMIN_ROLE) {
        if (_votingDelay > 30 days) revert TokenErrors.InvalidTimeRange();
        if (_votingPeriod < 1 days || _votingPeriod > 30 days) revert TokenErrors.InvalidTimeRange();
        if (_quorumThreshold > 10000) revert TokenErrors.InvalidPercentage();
        if (_executionDelay > 30 days) revert TokenErrors.InvalidTimeRange();
        if (_gracePeriod > 30 days) revert TokenErrors.InvalidTimeRange();

        config.votingDelay = _votingDelay;
        config.votingPeriod = _votingPeriod;
        config.proposalThreshold = _proposalThreshold;
        config.quorumThreshold = _quorumThreshold;
        config.executionDelay = _executionDelay;
        config.gracePeriod = _gracePeriod;
    }

    function setVotingWeight(IStakingPool.StakingLevel _level, uint256 _weight)
        external
        onlyRole(ADMIN_ROLE)
    {
        if (_weight > maxVotingWeight) revert TokenErrors.InvalidWeight();
        levelVotingWeights[_level] = _weight;
    }

    function getProposal(uint256 _proposalId) external view returns (
        address proposer,
        ProposalType proposalType,
        string memory title,
        string memory description,
        uint256 startTime,
        uint256 endTime,
        ProposalStatus status,
        uint256 forVotes,
        uint256 againstVotes,
        uint256 abstainVotes
    ) {
        Proposal storage proposal = proposals[_proposalId];
        return (
            proposal.proposer,
            proposal.proposalType,
            proposal.title,
            proposal.description,
            proposal.startTime,
            proposal.endTime,
            proposal.status,
            proposal.forVotes,
            proposal.againstVotes,
            proposal.abstainVotes
        );
    }

    function getActiveProposals() external view returns (uint256[] memory) {
        uint256 length = activeProposals.length();
        uint256[] memory result = new uint256[](length);

        for (uint256 i = 0; i < length; i++) {
            result[i] = activeProposals.at(i);
        }

        return result;
    }

    function getQueuedProposals() external view returns (uint256[] memory) {
        uint256 length = queuedProposals.length();
        uint256[] memory result = new uint256[](length);

        for (uint256 i = 0; i < length; i++) {
            result[i] = queuedProposals.at(i);
        }

        return result;
    }

    function hasVoted(uint256 _proposalId, address _voter) external view returns (bool) {
        return votes[_proposalId][_voter].hasVoted;
    }

    function getVote(uint256 _proposalId, address _voter) external view returns (
        bool voted,
        VoteType vote,
        uint256 weight,
        uint256 timestamp
    ) {
        Vote storage userVote = votes[_proposalId][_voter];
        return (
            userVote.hasVoted,
            userVote.vote,
            userVote.weight,
            userVote.timestamp
        );
    }

    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
    }
}
