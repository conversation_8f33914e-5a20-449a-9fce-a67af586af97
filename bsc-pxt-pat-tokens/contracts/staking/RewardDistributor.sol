// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/structs/EnumerableSet.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

contract RewardDistributor is AccessControl, ReentrancyGuard, Pausable {
    using SafeERC20 for IERC20;
    using EnumerableSet for EnumerableSet.AddressSet;

    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant OPERATOR_ROLE = keccak256("OPERATOR_ROLE");
    bytes32 public constant REWARD_PROVIDER_ROLE = keccak256("REWARD_PROVIDER_ROLE");
    bytes32 public constant EMERGENCY_ROLE = keccak256("EMERGENCY_ROLE");

    IPAT public rewardToken;
    EnumerableSet.AddressSet private stakingPools;

    struct RewardPool {
        uint256 totalAllocated;
        uint256 totalDistributed;
        uint256 allocPoint;
        uint256 lastRewardTime;
    }

    struct RewardCycle {
        uint256 startTime;
        uint256 endTime;
        uint256 rewardPerSecond;
        bool isActive;
    }

    struct RewardMultiplier {
        address stakingPool;
        uint256 multiplier;
        uint256 startTime;
        uint256 endTime;
        bool isActive;
    }

    struct SpecialRewardCampaign {
        string name;
        uint256 startTime;
        uint256 endTime;
        uint256 totalReward;
        uint256 distributed;
        mapping(address => bool) eligiblePools;
        bool isActive;
    }

    struct RewardRecord {
        address stakingPool;
        uint256 amount;
        uint256 timestamp;
    }

    mapping(address => RewardPool) public rewardPools;
    mapping(address => RewardRecord[]) public rewardHistory;
    mapping(address => uint256) public totalPoolRewards;
    mapping(uint256 => RewardCycle) public rewardCycles;
    mapping(uint256 => RewardMultiplier) public rewardMultipliers;
    mapping(uint256 => SpecialRewardCampaign) public specialCampaigns;

    uint256 public rewardCycleCount;
    uint256 public multiplierCount;
    uint256 public campaignCount;
    uint256 public totalAllocPoint;

    event RewardPoolAdded(address indexed stakingPool, uint256 indexed allocPoint);
    event RewardPoolUpdated(address indexed stakingPool, uint256 indexed allocPoint);
    event RewardPoolRemoved(address indexed stakingPool);
    event RewardDistributed(address indexed stakingPool, uint256 amount);
    event RewardCycleCreated(uint256 indexed cycleId, uint256 startTime, uint256 endTime, uint256 indexed rewardPerSecond);
    event RewardCycleUpdated(uint256 indexed cycleId, uint256 indexed rewardPerSecond);
    event MultiplierAdded(uint256 indexed multiplierId, address indexed stakingPool, uint256 multiplier);
    event MultiplierRemoved(uint256 indexed multiplierId);
    event SpecialCampaignCreated(uint256 indexed campaignId, string name, uint256 indexed totalReward);
    event EmergencyWithdrawal(uint256 amount);
    event TokenRecovered(address indexed token, uint256 amount);

    constructor(address _rewardToken) {
        if (_rewardToken == address(0)) revert TokenErrors.ZeroAddress();

        rewardToken = IPAT(_rewardToken);

        _setupRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _setupRole(ADMIN_ROLE, msg.sender);
        _setupRole(OPERATOR_ROLE, msg.sender);
        _setupRole(REWARD_PROVIDER_ROLE, msg.sender);
        _setupRole(EMERGENCY_ROLE, msg.sender);
    }

    modifier whenPoolActive(address _stakingPool) {
        if (!EnumerableSet.contains(stakingPools, _stakingPool)) revert TokenErrors.PoolInactive();
        _;
    }

    function addRewardPool(address _stakingPool, uint256 _allocPoint)
        external
        onlyRole(ADMIN_ROLE)
        whenNotPaused
    {
        if (_stakingPool == address(0)) revert TokenErrors.ZeroAddress();
        if (EnumerableSet.contains(stakingPools, _stakingPool)) revert TokenErrors.PoolAlreadyExists();

        EnumerableSet.add(stakingPools, _stakingPool);

        rewardPools[_stakingPool] = RewardPool({
            totalAllocated: 0,
            totalDistributed: 0,
            allocPoint: _allocPoint,
            lastRewardTime: block.timestamp
        });

        // 🔒 Gas优化：使用unchecked进行分配点数更新
        unchecked {
            totalAllocPoint += _allocPoint;
        }

        emit RewardPoolAdded(_stakingPool, _allocPoint);
    }

    function updateRewardPool(address _stakingPool, uint256 _allocPoint)
        external
        onlyRole(ADMIN_ROLE)
        whenNotPaused
        whenPoolActive(_stakingPool)
    {
        distributeReward(_stakingPool);

        // 🔒 Gas优化：使用unchecked进行分配点数更新
        unchecked {
            totalAllocPoint = totalAllocPoint - rewardPools[_stakingPool].allocPoint + _allocPoint;
        }

        rewardPools[_stakingPool].allocPoint = _allocPoint;

        emit RewardPoolUpdated(_stakingPool, _allocPoint);
    }

    function removeRewardPool(address _stakingPool)
        external
        onlyRole(ADMIN_ROLE)
        whenPoolActive(_stakingPool)
    {
        distributeReward(_stakingPool);

        // 🔒 Gas优化：使用unchecked进行分配点数更新
        unchecked {
            totalAllocPoint -= rewardPools[_stakingPool].allocPoint;
        }

        EnumerableSet.remove(stakingPools, _stakingPool);

        emit RewardPoolRemoved(_stakingPool);
    }

    function createRewardCycle(
        uint256 _startTime,
        uint256 _endTime,
        uint256 _rewardPerSecond
    )
        external
        onlyRole(ADMIN_ROLE)
        whenNotPaused
    {
        if (_startTime < block.timestamp) revert TokenErrors.InvalidDates();
        if (_endTime <= _startTime) revert TokenErrors.InvalidDates();

        uint256 cycleId = rewardCycleCount;
        // 🔒 Gas优化：使用unchecked进行计数器更新
        unchecked {
            rewardCycleCount++;
        }

        rewardCycles[cycleId] = RewardCycle({
            startTime: _startTime,
            endTime: _endTime,
            rewardPerSecond: _rewardPerSecond,
            isActive: true
        });

        uint256 totalReward = _rewardPerSecond * (_endTime - _startTime);

        if (rewardToken.balanceOf(address(this)) < totalReward) revert TokenErrors.InsufficientBalance();

        emit RewardCycleCreated(cycleId, _startTime, _endTime, _rewardPerSecond);
    }

    function updateRewardCycle(uint256 _cycleId, uint256 _rewardPerSecond)
        external
        onlyRole(ADMIN_ROLE)
        whenNotPaused
    {
        if (_cycleId >= rewardCycleCount) revert TokenErrors.InvalidCycleId();
        RewardCycle storage cycle = rewardCycles[_cycleId];

        if (!cycle.isActive) revert TokenErrors.CycleNotFinalized();
        if (block.timestamp >= cycle.endTime) revert TokenErrors.CycleNotEnded();

        cycle.rewardPerSecond = _rewardPerSecond;

        emit RewardCycleUpdated(_cycleId, _rewardPerSecond);
    }

    function addRewardMultiplier(
        address _stakingPool,
        uint256 _multiplier,
        uint256 _duration
    )
        external
        onlyRole(ADMIN_ROLE)
        whenNotPaused
        whenPoolActive(_stakingPool)
    {
        if (_multiplier == 0) revert TokenErrors.InvalidWeight();

        uint256 multiplierId = multiplierCount;
        // 🔒 Gas优化：使用unchecked进行计数器更新
        unchecked {
            multiplierCount++;
        }

        rewardMultipliers[multiplierId] = RewardMultiplier({
            stakingPool: _stakingPool,
            multiplier: _multiplier,
            startTime: block.timestamp,
            endTime: block.timestamp + _duration,
            isActive: true
        });

        emit MultiplierAdded(multiplierId, _stakingPool, _multiplier);
    }

    function removeRewardMultiplier(uint256 _multiplierId)
        external
        onlyRole(ADMIN_ROLE)
    {
        if (_multiplierId >= multiplierCount) revert TokenErrors.InvalidOperation();

        RewardMultiplier storage multiplier = rewardMultipliers[_multiplierId];
        multiplier.isActive = false;

        emit MultiplierRemoved(_multiplierId);
    }

    function createSpecialCampaign(
        string memory _name,
        uint256 _duration,
        uint256 _totalReward,
        address[] memory _eligiblePools
    )
        external
        onlyRole(ADMIN_ROLE)
        whenNotPaused
    {
        if (_duration == 0) revert TokenErrors.InvalidTimeRange();
        if (_totalReward == 0) revert TokenErrors.ZeroAmount();

        if (rewardToken.balanceOf(address(this)) < _totalReward) revert TokenErrors.InsufficientBalance();

        uint256 campaignId = campaignCount;
        // 🔒 Gas优化：使用unchecked进行计数器更新
        unchecked {
            campaignCount++;
        }

        SpecialRewardCampaign storage campaign = specialCampaigns[campaignId];
        campaign.name = _name;
        campaign.startTime = block.timestamp;
        campaign.endTime = block.timestamp + _duration;
        campaign.totalReward = _totalReward;
        campaign.distributed = 0;
        campaign.isActive = true;

        // 🔒 Gas优化：合格池设置循环优化
        for (uint256 i = 0; i < _eligiblePools.length;) {
            if(!EnumerableSet.contains(stakingPools, _eligiblePools[i])) revert TokenErrors.PoolInactive();
            campaign.eligiblePools[_eligiblePools[i]] = true;
            unchecked { i++; }
        }

        emit SpecialCampaignCreated(campaignId, _name, _totalReward);
    }

    function distributeSpecialReward(
        uint256 _campaignId,
        address _stakingPool,
        uint256 _amount
    )
        external
        onlyRole(OPERATOR_ROLE)
        whenNotPaused
        whenPoolActive(_stakingPool)
    {
        if (_campaignId >= campaignCount) revert TokenErrors.InvalidOperation();
        SpecialRewardCampaign storage campaign = specialCampaigns[_campaignId];

        if (!campaign.isActive) revert TokenErrors.InvalidOperation();
        if (block.timestamp > campaign.endTime) revert TokenErrors.InvalidOperation();
        if (!campaign.eligiblePools[_stakingPool]) revert TokenErrors.InvalidOperation();
        if (campaign.distributed + _amount > campaign.totalReward) revert TokenErrors.InsufficientBalance();

        // 🔒 Gas优化：使用unchecked进行分发统计更新
        unchecked {
            campaign.distributed += _amount;
        }

        _distributeReward(_stakingPool, _amount);
    }

    function distributeReward(address _stakingPool) public whenNotPaused whenPoolActive(_stakingPool) {
        RewardPool storage pool = rewardPools[_stakingPool];

        if (pool.lastRewardTime == block.timestamp || pool.allocPoint == 0) {
            return;
        }

        uint256 reward = calculatePoolReward(_stakingPool);

        if (reward > 0) {
            _distributeReward(_stakingPool, reward);
        }
    }

    function distributeAllRewards() external whenNotPaused {
        uint256 poolCount = EnumerableSet.length(stakingPools);

        // 🔒 Gas优化：分发所有奖励循环优化
        for (uint256 i = 0; i < poolCount;) {
            address stakingPool = EnumerableSet.at(stakingPools, i);
            distributeReward(stakingPool);
            unchecked { i++; }
        }
    }

    function calculatePoolReward(address _stakingPool) public view returns (uint256) {
        if (!EnumerableSet.contains(stakingPools, _stakingPool)) {
            return 0;
        }

        RewardPool storage pool = rewardPools[_stakingPool];

        if (pool.allocPoint == 0) {
            return 0;
        }

        uint256 lastRewardTime = pool.lastRewardTime;
        uint256 currentTime = block.timestamp;

        if (lastRewardTime >= currentTime) {
            return 0;
        }

        uint256 totalReward = _calculateBasicReward(_stakingPool, lastRewardTime, currentTime);

        uint256 finalReward = _applyRewardMultipliers(_stakingPool, totalReward, lastRewardTime, currentTime);

        return finalReward;
    }

    function _calculateBasicReward(address _stakingPool, uint256 lastRewardTime, uint256 currentTime) internal view returns (uint256) {
        uint256 totalReward = 0;
        RewardPool storage pool = rewardPools[_stakingPool];

        // 🔒 Gas优化：奖励周期计算循环优化
        for (uint256 i = 0; i < rewardCycleCount;) {
            RewardCycle storage cycle = rewardCycles[i];

            if (!cycle.isActive) {
                unchecked { i++; }
                continue;
            }

            if (currentTime < cycle.startTime) {
                unchecked { i++; }
                continue;
            }

            if (lastRewardTime >= cycle.endTime) {
                unchecked { i++; }
                continue;
            }

            uint256 from = lastRewardTime > cycle.startTime ? lastRewardTime : cycle.startTime;
            uint256 to = currentTime < cycle.endTime ? currentTime : cycle.endTime;

            if (from < to) {
                uint256 duration = to - from;
                uint256 cycleReward = cycle.rewardPerSecond * duration;

                uint256 poolReward = cycleReward * pool.allocPoint / totalAllocPoint;
                unchecked {
                    totalReward += poolReward;
                }
            }
            unchecked { i++; }
        }

        return totalReward;
    }

    function _applyRewardMultipliers(address _stakingPool, uint256 totalReward, uint256 lastRewardTime, uint256 currentTime) internal view returns (uint256) {
        uint256 finalReward = totalReward;

        // 🔒 Gas优化：奖励倍数应用循环优化
        for (uint256 i = 0; i < multiplierCount;) {
            RewardMultiplier storage multiplier = rewardMultipliers[i];

            if (!multiplier.isActive) {
                unchecked { i++; }
                continue;
            }
            if (multiplier.stakingPool != _stakingPool) {
                unchecked { i++; }
                continue;
            }

            if (lastRewardTime < multiplier.endTime && currentTime > multiplier.startTime) {
                uint256 from = lastRewardTime > multiplier.startTime ? lastRewardTime : multiplier.startTime;
                uint256 to = currentTime < multiplier.endTime ? currentTime : multiplier.endTime;

                if (from < to) {
                    uint256 totalDuration = currentTime - lastRewardTime;
                    uint256 multiplierDuration = to - from;

                    uint256 ratio = multiplierDuration * 10000 / totalDuration;

                    uint256 baseReward = totalReward * ratio / 10000;
                    uint256 extraReward = baseReward * (multiplier.multiplier - 10000) / 10000;

                    unchecked {
                        finalReward += extraReward;
                    }
                }
            }
            unchecked { i++; }
        }

        return finalReward;
    }
    
    function _distributeReward(address _stakingPool, uint256 _amount) internal {
        if (_amount == 0) return;

        RewardPool storage pool = rewardPools[_stakingPool];

        // 🔒 Gas优化：使用unchecked进行奖励统计更新
        unchecked {
            pool.totalAllocated += _amount;
            pool.totalDistributed += _amount;
            totalPoolRewards[_stakingPool] += _amount;
        }
        pool.lastRewardTime = block.timestamp;

        rewardHistory[_stakingPool].push(RewardRecord({
            stakingPool: _stakingPool,
            amount: _amount,
            timestamp: block.timestamp
        }));

        rewardToken.transfer(_stakingPool, _amount);

        emit RewardDistributed(_stakingPool, _amount);
    }

    function addRewardTokens(uint256 _amount) external onlyRole(REWARD_PROVIDER_ROLE) whenNotPaused {
        if (_amount == 0) revert TokenErrors.ZeroAmount();

        rewardToken.transferFrom(msg.sender, address(this), _amount);
    }

    function emergencyWithdraw(uint256 _amount) external onlyRole(EMERGENCY_ROLE) {
        if (_amount == 0) revert TokenErrors.ZeroAmount();
        if (_amount > rewardToken.balanceOf(address(this))) revert TokenErrors.InsufficientBalance();

        rewardToken.transfer(msg.sender, _amount);

        emit EmergencyWithdrawal(_amount);
    }
    
    function recoverToken(address _token, uint256 _amount) external onlyRole(ADMIN_ROLE) {
        if (_token == address(0)) revert TokenErrors.ZeroAddress();
        if (_amount == 0) revert TokenErrors.ZeroAmount();

        if (_token == address(rewardToken)) revert TokenErrors.InvalidOperation();

        IERC20(_token).transfer(msg.sender, _amount);

        emit TokenRecovered(_token, _amount);
    }

    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
    }

    function getAllStakingPools() external view returns (address[] memory) {
        uint256 poolCount = EnumerableSet.length(stakingPools);
        address[] memory pools = new address[](poolCount);

        for (uint256 i = 0; i < poolCount; i++) {
            pools[i] = EnumerableSet.at(stakingPools, i);
        }

        return pools;
    }

    function getPoolRewardHistory(address _stakingPool) external view returns (RewardRecord[] memory) {
        return rewardHistory[_stakingPool];
    }
    

    function isEligibleForCampaign(uint256 _campaignId, address _stakingPool) external view returns (bool) {
        if (_campaignId >= campaignCount) revert TokenErrors.InvalidOperation();
        return specialCampaigns[_campaignId].eligiblePools[_stakingPool];
    }
    

    function getEligiblePoolsForCampaign(uint256 _campaignId, address[] memory _allPools) external view returns (address[] memory) {
        if (_campaignId >= campaignCount) revert TokenErrors.InvalidOperation();
        SpecialRewardCampaign storage campaign = specialCampaigns[_campaignId];
        uint256 count = 0;
        for (uint256 i = 0; i < _allPools.length; i++) {
            if (campaign.eligiblePools[_allPools[i]]) {
                count++;
            }
        }
        address[] memory eligible = new address[](count);
        uint256 idx = 0;
        for (uint256 i = 0; i < _allPools.length; i++) {
            if (campaign.eligiblePools[_allPools[i]]) {
                eligible[idx] = _allPools[i];
                idx++;
            }
        }
        return eligible;
    }


    function getPoolRewardHistoryPaged(address _stakingPool, uint256 offset, uint256 limit) external view returns (RewardRecord[] memory) {
        RewardRecord[] storage history = rewardHistory[_stakingPool];
        uint256 total = history.length;
        if (offset >= total) {
            return new RewardRecord[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        RewardRecord[] memory result = new RewardRecord[](end - offset);
        for (uint256 i = offset; i < end; i++) {
            result[i - offset] = history[i];
        }
        return result;
        }
}
