// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

/**
 * @title ArtistIdentity
 * @dev 艺术家身份管理合约 - 管理创作者的身份验证和声誉系统
 * @notice 这个合约负责艺术家注册、验证、专业技能管理和成就系统
 * <AUTHOR> Team
 *
 * 功能特性:
 * - 艺术家注册：支持多种艺术家类型注册
 * - 身份验证：多级验证系统（基础/高级/专业）
 * - 声誉系统：基于活动和贡献的动态声誉评分
 * - 专业技能：技能分类和背书机制
 * - 成就系统：官方成就认证和奖励
 * - 权限管理：基于声誉和代币持有的权限控制
 * - 分页查询：高效的数据检索和展示
 */

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/utils/Counters.sol";
import "../interfaces/TokenErrors.sol";
import "../staking/StakingPool.sol";

contract ArtistIdentity is AccessControl, Pausable, ReentrancyGuard {
    using SafeERC20 for IERC20;
    using Counters for Counters.Counter;

    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant VERIFIER_ROLE = keccak256("VERIFIER_ROLE");
    bytes32 public constant PLATFORM_ROLE = keccak256("PLATFORM_ROLE");

    IERC20 public patoken;
    IERC20 public pxtoken;

    Counters.Counter private _artistIdCounter;

    enum VerificationLevel { NONE, BASIC, ADVANCED, PREMIUM }

    enum ArtistType { CREATOR, CURATOR, VALIDATOR, COLLABORATOR }

    enum ArtistStatus { ACTIVE, SUSPENDED, BANNED }

    uint256 private constant MAX_TAGS = 10;
    uint256 private constant MAX_PAGE_SIZE = 100;

    struct ArtistInfo {
        uint256 artistId;
        address walletAddress;
        string displayName;
        string profileURI;
        uint256 registrationTime;
        uint256 reputation;
        uint256 contributionScore;
        uint8 verLevel;
        uint8 artistType;
        uint8 status;
        bool hasSpecialRights;
        string[] tags;
    }

    struct VerificationRecord {
        address verifier;
        uint256 timestamp;
        uint8 level;
        string details;
        bytes signature;
    }

    struct Expertise {
        string category;
        uint8 level;
        uint256 endorsements;
    }

    struct Achievement {
        string title;
        string description;
        uint256 timestamp;
        string proofURI;
    }

    event ArtistRegistered(uint256 indexed artistId, address indexed walletAddress, string displayName);
    event ArtistUpdated(uint256 indexed artistId, address indexed walletAddress);
    event ArtistVerified(uint256 indexed artistId, address indexed verifier, VerificationLevel level);
    event ArtistStatusChanged(uint256 indexed artistId, ArtistStatus status);
    event ExpertiseAdded(uint256 indexed artistId, string category, uint8 level);
    event AchievementAwarded(uint256 indexed artistId, string title);
    event ReputationChanged(uint256 indexed artistId, uint256 oldScore, uint256 newScore);
    event ContractPaused(address indexed operator);
    event ContractUnpaused(address indexed operator);
    event TokensWithdrawn(address indexed token, address indexed to, uint256 amount);

    StakingPool public stakingPool;

    mapping(uint256 => ArtistInfo) public artists;
    mapping(address => uint256) public artistsByAddress;
    mapping(string => uint256) public artistsByName;
    mapping(uint256 => VerificationRecord[]) public verifications;
    mapping(uint256 => Expertise[]) public expertise;
    mapping(uint256 => Achievement[]) public achievements;
    mapping(uint256 => mapping(address => bool)) public endorsements;

    uint256 public registrationFee;
    uint256 public verificationFee;
    uint256 public premiumStatusThreshold;
    uint256 public reputationDecayPeriod;
    uint256 public minReputationToCreateProposal;

    constructor() {
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(ADMIN_ROLE, msg.sender);
        _grantRole(PLATFORM_ROLE, msg.sender);

        registrationFee = 100 * 10**18;
        verificationFee = 50 * 10**18;
        premiumStatusThreshold = 1000 * 10**18;
        reputationDecayPeriod = 30 days;
        minReputationToCreateProposal = 5000;
    }

    function initialize(
        address _patoken,
        address _pxtoken,
        address _stakingPool
    ) external onlyRole(ADMIN_ROLE) {
        require(address(patoken) == address(0), "Already initialized");

        patoken = IERC20(_patoken);
        pxtoken = IERC20(_pxtoken);
        stakingPool = StakingPool(_stakingPool);
    }

    function registerArtist(
        string memory _displayName,
        string memory _profileURI,
        ArtistType _artistType,
        string[] memory _tags
    ) external whenNotPaused nonReentrant {
        if (artistsByAddress[msg.sender] != 0) revert TokenErrors.AlreadyRegistered();
        if (artistsByName[_displayName] != 0) revert TokenErrors.NameAlreadyTaken();
        if (_tags.length > MAX_TAGS) revert TokenErrors.TooManyTags();

        _artistIdCounter.increment();
        uint256 newArtistId = _artistIdCounter.current();

        ArtistInfo memory newArtist = ArtistInfo({
            artistId: newArtistId,
            walletAddress: msg.sender,
            displayName: _displayName,
            profileURI: _profileURI,
            verLevel: uint8(VerificationLevel.NONE),
            artistType: uint8(_artistType),
            status: uint8(ArtistStatus.ACTIVE),
            registrationTime: block.timestamp,
            reputation: 1000,
            hasSpecialRights: false,
            contributionScore: 0,
            tags: _tags
        });

        artists[newArtistId] = newArtist;
        artistsByAddress[msg.sender] = newArtistId;
        artistsByName[_displayName] = newArtistId;

        if (registrationFee > 0) {
            patoken.safeTransferFrom(msg.sender, address(this), registrationFee);
        }

        emit ArtistRegistered(newArtistId, msg.sender, _displayName);
    }

    function updateArtistProfile(
        string memory _profileURI,
        string[] memory _tags
    ) external whenNotPaused nonReentrant {
        uint256 artistId = artistsByAddress[msg.sender];
        if (artistId == 0) revert TokenErrors.NotRegistered();
        if (artists[artistId].status == uint8(ArtistStatus.BANNED)) revert TokenErrors.InvalidArtistStatus();
        if (_tags.length > MAX_TAGS) revert TokenErrors.TooManyTags();

        ArtistInfo storage artist = artists[artistId];
        artist.profileURI = _profileURI;
        artist.tags = _tags;

        emit ArtistUpdated(artistId, msg.sender);
    }

    function requestVerification(
        string memory _details
    ) external whenNotPaused nonReentrant {
        uint256 artistId = artistsByAddress[msg.sender];
        if (artistId == 0) revert TokenErrors.NotRegistered();
        if (artists[artistId].status != uint8(ArtistStatus.ACTIVE)) revert TokenErrors.InvalidArtistStatus();

        VerificationRecord memory record = VerificationRecord({
            verifier: address(0),
            timestamp: block.timestamp,
            level: uint8(VerificationLevel.NONE),
            details: _details,
            signature: bytes("")
        });

        verifications[artistId].push(record);

        if (verificationFee > 0) {
            patoken.safeTransferFrom(msg.sender, address(this), verificationFee);
        }
    }

    function verifyArtist(
        uint256 _artistId,
        VerificationLevel _level,
        bytes memory _signature
    ) external onlyRole(VERIFIER_ROLE) whenNotPaused nonReentrant {
        if (_artistId == 0 || _artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();
        if (artists[_artistId].status != uint8(ArtistStatus.ACTIVE)) revert TokenErrors.InvalidArtistStatus();
        if (_level == VerificationLevel.NONE) revert TokenErrors.InvalidVerificationLevel();

        artists[_artistId].verLevel = uint8(_level);

        VerificationRecord memory record = VerificationRecord({
            verifier: msg.sender,
            timestamp: block.timestamp,
            level: uint8(_level),
            details: "Official verification",
            signature: _signature
        });

        verifications[_artistId].push(record);

        uint256 reputationIncrease;
        if (_level == VerificationLevel.BASIC) {
            reputationIncrease = 500;
        } else if (_level == VerificationLevel.ADVANCED) {
            reputationIncrease = 1000;
        } else if (_level == VerificationLevel.PREMIUM) {
            reputationIncrease = 2000;
        }

        if (reputationIncrease > 0) {
            // 🔒 安全优化：防止溢出的声誉增加
            uint256 newReputation;
            unchecked {
                newReputation = artists[_artistId].reputation + reputationIncrease;
            }
            _updateReputation(_artistId, newReputation);
        }

        emit ArtistVerified(_artistId, msg.sender, _level);
    }

    function addExpertise(
        string memory _category,
        uint8 _level
    ) external whenNotPaused nonReentrant {
        uint256 artistId = artistsByAddress[msg.sender];
        if (artistId == 0) revert TokenErrors.NotRegistered();
        if (artists[artistId].status != uint8(ArtistStatus.ACTIVE)) revert TokenErrors.InvalidArtistStatus();
        if (_level < 1 || _level > 5) revert TokenErrors.InvalidExpertiseLevel();

        bool found = false;
        uint256 length = expertise[artistId].length;
        for (uint256 i = 0; i < length;) {
            if (keccak256(bytes(expertise[artistId][i].category)) == keccak256(bytes(_category))) {
                expertise[artistId][i].level = _level;
                found = true;
                break;
            }
            unchecked { i++; }
        }

        if (!found) {
            expertise[artistId].push(Expertise({
                category: _category,
                level: _level,
                endorsements: 0
            }));
        }

        emit ExpertiseAdded(artistId, _category, _level);
    }

    function endorseExpertise(
        uint256 _artistId,
        uint256 _expertiseIndex
    ) external whenNotPaused nonReentrant {
        uint256 endorserArtistId = artistsByAddress[msg.sender];
        if (endorserArtistId == 0) revert TokenErrors.NotRegistered();
        if (endorserArtistId == _artistId) revert TokenErrors.CannotEndorseYourself();
        if (_artistId == 0 || _artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();
        if (artists[_artistId].status != uint8(ArtistStatus.ACTIVE)) revert TokenErrors.InvalidArtistStatus();
        if (_expertiseIndex >= expertise[_artistId].length) revert TokenErrors.InvalidExpertiseIndex();
        if (endorsements[_artistId][msg.sender]) revert TokenErrors.AlreadyEndorsed();

        endorsements[_artistId][msg.sender] = true;

        // 🔒 安全优化：使用unchecked进行背书计数和声誉更新
        unchecked {
            expertise[_artistId][_expertiseIndex].endorsements++;
        }

        uint256 newReputation;
        unchecked {
            newReputation = artists[_artistId].reputation + 10;
        }
        _updateReputation(_artistId, newReputation);
    }

    function awardAchievement(
        uint256 _artistId,
        string memory _title,
        string memory _description,
        string memory _proofURI
    ) external onlyRole(ADMIN_ROLE) whenNotPaused nonReentrant {
        if (_artistId == 0 || _artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();
        if (artists[_artistId].status != uint8(ArtistStatus.ACTIVE)) revert TokenErrors.InvalidArtistStatus();

        achievements[_artistId].push(Achievement({
            title: _title,
            description: _description,
            timestamp: block.timestamp,
            proofURI: _proofURI
        }));

        // 🔒 安全优化：防止溢出的成就奖励声誉增加
        uint256 newReputation;
        unchecked {
            newReputation = artists[_artistId].reputation + 200;
        }
        _updateReputation(_artistId, newReputation);

        emit AchievementAwarded(_artistId, _title);
    }

    function updateArtistStatus(
        uint256 _artistId,
        ArtistStatus _status
    ) external onlyRole(ADMIN_ROLE) nonReentrant {
        if (_artistId == 0 || _artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();

        artists[_artistId].status = uint8(_status);

        if (_status == ArtistStatus.BANNED) {
            // 🔒 安全优化：防止溢出的声誉惩罚计算
            uint256 newReputation;
            unchecked {
                newReputation = artists[_artistId].reputation / 2;
            }
            _updateReputation(_artistId, newReputation);
        }

        emit ArtistStatusChanged(_artistId, _status);
    }

    function adjustReputation(
        uint256 _artistId,
        uint256 _delta,
        bool _isIncrease
    ) external onlyRole(ADMIN_ROLE) nonReentrant {
        if (_artistId == 0 || _artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();

        uint256 currentReputation = artists[_artistId].reputation;
        uint256 newReputation;

        // 🔒 安全优化：防止溢出的声誉调整计算
        if (_isIncrease) {
            unchecked {
                newReputation = currentReputation + _delta;
            }
            if (newReputation > 10000) newReputation = 10000;
        } else {
            if (_delta > currentReputation) {
                newReputation = 0;
            } else {
                unchecked {
                    newReputation = currentReputation - _delta;
                }
            }
        }

        _updateReputation(_artistId, newReputation);
    }

    function addContributionScore(
        uint256 _artistId,
        uint256 _points
    ) external onlyRole(PLATFORM_ROLE) whenNotPaused nonReentrant {
        if (_artistId == 0 || _artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();
        if (artists[_artistId].status != uint8(ArtistStatus.ACTIVE)) revert TokenErrors.InvalidArtistStatus();

        // 🔒 安全优化：使用unchecked进行贡献分数和声誉计算
        unchecked {
            artists[_artistId].contributionScore += _points;
        }

        uint256 reputationIncrease;
        unchecked {
            reputationIncrease = _points / 10;
        }
        if (reputationIncrease > 0) {
            uint256 newReputation;
            unchecked {
                newReputation = artists[_artistId].reputation + reputationIncrease;
            }
            _updateReputation(_artistId, newReputation);
        }
    }

    function checkSpecialRights(uint256 _artistId) external view returns (bool) {
        if (_artistId == 0 || _artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();

        if (artists[_artistId].hasSpecialRights) {
            return true;
        }

        address artistAddress = artists[_artistId].walletAddress;
        uint256 pxtBalance = pxtoken.balanceOf(artistAddress);

        return pxtBalance >= premiumStatusThreshold;
    }

    function canCreateProposal(uint256 _artistId) external view returns (bool) {
        if (_artistId == 0 || _artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();

        return artists[_artistId].reputation >= minReputationToCreateProposal;
    }

    function getArtistInfo(uint256 _artistId) external view returns (ArtistInfo memory) {
        if (_artistId == 0 || _artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();

        return artists[_artistId];
    }

    function getVerificationRecords(uint256 _artistId) external view returns (VerificationRecord[] memory) {
        if (_artistId == 0 || _artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();

        return verifications[_artistId];
    }

    function getExpertises(uint256 _artistId) external view returns (Expertise[] memory) {
        if (_artistId == 0 || _artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();

        return expertise[_artistId];
    }

    function getAchievements(uint256 _artistId) external view returns (Achievement[] memory) {
        if (_artistId == 0 || _artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();

        return achievements[_artistId];
    }

    function _updateReputation(uint256 _artistId, uint256 _newReputation) internal {
        uint256 oldReputation = artists[_artistId].reputation;

        if (_newReputation > 10000) {
            _newReputation = 10000;
        }

        artists[_artistId].reputation = _newReputation;

        emit ReputationChanged(_artistId, oldReputation, _newReputation);
    }

    function withdrawTokens(
        address _token,
        address _to,
        uint256 _amount
    ) external onlyRole(ADMIN_ROLE) nonReentrant {
        if (_to == address(0)) revert TokenErrors.ZeroAddress();
        IERC20(_token).safeTransfer(_to, _amount);
        emit TokensWithdrawn(_token, _to, _amount);
    }

    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
        emit ContractPaused(msg.sender);
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
        emit ContractUnpaused(msg.sender);
    }

    function getArtistsPaged(uint256 offset, uint256 limit) external view returns (uint256[] memory) {
        if (limit > MAX_PAGE_SIZE) {
            limit = MAX_PAGE_SIZE;
        }

        uint256 total = _artistIdCounter.current();
        if (offset >= total) {
            return new uint256[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        uint256[] memory ids = new uint256[](end - offset);

        for (uint256 i = offset; i < end;) {
            ids[i - offset] = i + 1;
            unchecked { i++; }
        }
        return ids;
    }

    function getVerificationRecordsPaged(uint256 artistId, uint256 offset, uint256 limit) external view returns (VerificationRecord[] memory) {
        if (artistId == 0 || artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();

        if (limit > MAX_PAGE_SIZE) {
            limit = MAX_PAGE_SIZE;
        }

        uint256 total = verifications[artistId].length;
        if (offset >= total) {
            return new VerificationRecord[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        VerificationRecord[] memory result = new VerificationRecord[](end - offset);

        for (uint256 i = offset; i < end;) {
            result[i - offset] = verifications[artistId][i];
            unchecked { i++; }
        }
        return result;
    }

    function getExpertisesPaged(uint256 artistId, uint256 offset, uint256 limit) external view returns (Expertise[] memory) {
        if (artistId == 0 || artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();

        if (limit > MAX_PAGE_SIZE) {
            limit = MAX_PAGE_SIZE;
        }

        uint256 total = expertise[artistId].length;
        if (offset >= total) {
            return new Expertise[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        Expertise[] memory result = new Expertise[](end - offset);

        for (uint256 i = offset; i < end;) {
            result[i - offset] = expertise[artistId][i];
            unchecked { i++; }
        }
        return result;
    }

    function getAchievementsPaged(uint256 artistId, uint256 offset, uint256 limit) external view returns (Achievement[] memory) {
        if (artistId == 0 || artistId > _artistIdCounter.current()) revert TokenErrors.InvalidArtistId();

        if (limit > MAX_PAGE_SIZE) {
            limit = MAX_PAGE_SIZE;
        }

        uint256 total = achievements[artistId].length;
        if (offset >= total) {
            return new Achievement[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        Achievement[] memory result = new Achievement[](end - offset);

        for (uint256 i = offset; i < end;) {
            result[i - offset] = achievements[artistId][i];
            unchecked { i++; }
        }
        return result;
    }

    // 🔒 安全优化：添加实用的查询函数

    /**
     * @dev 获取艺术家总数
     */
    function getTotalArtists() external view returns (uint256) {
        return _artistIdCounter.current();
    }

    /**
     * @dev 检查艺术家是否存在
     */
    function artistExists(uint256 _artistId) external view returns (bool) {
        return _artistId > 0 && _artistId <= _artistIdCounter.current();
    }

    /**
     * @dev 通过地址获取艺术家ID
     */
    function getArtistIdByAddress(address _address) external view returns (uint256) {
        return artistsByAddress[_address];
    }

    /**
     * @dev 通过名称获取艺术家ID
     */
    function getArtistIdByName(string memory _name) external view returns (uint256) {
        return artistsByName[_name];
    }

    /**
     * @dev 批量检查艺术家状态
     */
    function batchGetArtistStatus(uint256[] memory _artistIds) external view returns (ArtistStatus[] memory) {
        ArtistStatus[] memory statuses = new ArtistStatus[](_artistIds.length);

        for (uint256 i = 0; i < _artistIds.length;) {
            if (_artistIds[i] > 0 && _artistIds[i] <= _artistIdCounter.current()) {
                statuses[i] = ArtistStatus(artists[_artistIds[i]].status);
            } else {
                statuses[i] = ArtistStatus.BANNED; // 无效ID视为禁用状态
            }
            unchecked { i++; }
        }

        return statuses;
    }
}