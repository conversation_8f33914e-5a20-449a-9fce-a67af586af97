// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "../interfaces/TokenErrors.sol";

/**
 * @title EmergencyManager
 * @dev 紧急响应管理合约 - 提供完善的紧急暂停和恢复机制
 * 
 * 功能：
 * 1. 分级紧急响应：不同级别的紧急情况
 * 2. 自动恢复机制：基于时间和条件的自动恢复
 * 3. 紧急操作权限：紧急情况下的特殊权限
 * 4. 事件记录和分析：完整的紧急事件日志
 */
contract EmergencyManager is AccessControl, ReentrancyGuard, Pausable {
    bytes32 public constant EMERGENCY_OPERATOR_ROLE = keccak256("EMERGENCY_OPERATOR_ROLE");
    bytes32 public constant EMERGENCY_ADMIN_ROLE = keccak256("EMERGENCY_ADMIN_ROLE");
    bytes32 public constant SYSTEM_MONITOR_ROLE = keccak256("SYSTEM_MONITOR_ROLE");

    // 紧急级别
    enum EmergencyLevel {
        NONE,           // 无紧急情况
        LOW,            // 低级：监控告警
        MEDIUM,         // 中级：部分功能暂停
        HIGH,           // 高级：主要功能暂停
        CRITICAL        // 严重：全系统暂停
    }

    // 紧急事件类型
    enum EmergencyType {
        SECURITY_BREACH,     // 安全漏洞
        ECONOMIC_ATTACK,     // 经济攻击
        TECHNICAL_FAILURE,   // 技术故障
        GOVERNANCE_DISPUTE,  // 治理争议
        EXTERNAL_THREAT,     // 外部威胁
        ORACLE_FAILURE,      // 预言机故障
        LIQUIDITY_CRISIS     // 流动性危机
    }

    struct EmergencyEvent {
        uint256 id;
        EmergencyType eventType;
        EmergencyLevel level;
        uint256 triggeredAt;
        uint256 resolvedAt;
        address triggeredBy;
        address resolvedBy;
        string description;
        string resolution;
        bool isActive;
        bool autoResolve;
        uint256 autoResolveTime;
        bytes32[] affectedContracts;
        mapping(bytes32 => bool) pausedFunctions;
    }

    struct EmergencyConfig {
        EmergencyLevel maxAutoLevel;     // 最大自动触发级别
        uint256 autoResolveDelay;        // 自动恢复延迟
        bool allowAutoResolve;           // 是否允许自动恢复
        uint256 cooldownPeriod;          // 冷却期
        mapping(EmergencyType => bool) autoTriggerEnabled;
    }

    // 当前紧急状态
    EmergencyLevel public currentEmergencyLevel;
    uint256 public lastEmergencyTime;
    uint256 public emergencyEventCount;
    
    // 紧急事件映射
    mapping(uint256 => EmergencyEvent) public emergencyEvents;
    
    // 配置
    EmergencyConfig public config;
    
    // 受影响的合约列表
    mapping(bytes32 => address) public monitoredContracts;
    bytes32[] public contractNames;
    
    // 暂停的功能列表
    mapping(bytes32 => mapping(bytes4 => bool)) public pausedFunctions;
    
    // 紧急操作权限
    mapping(address => mapping(EmergencyLevel => bool)) public emergencyPermissions;

    event EmergencyTriggered(
        uint256 indexed eventId,
        EmergencyType indexed eventType,
        EmergencyLevel indexed level,
        address triggeredBy,
        string description
    );

    event EmergencyResolved(
        uint256 indexed eventId,
        address resolvedBy,
        string resolution
    );

    event EmergencyLevelChanged(
        EmergencyLevel indexed oldLevel,
        EmergencyLevel indexed newLevel,
        uint256 timestamp
    );

    event FunctionPaused(bytes32 indexed contractName, bytes4 indexed functionSelector);
    event FunctionUnpaused(bytes32 indexed contractName, bytes4 indexed functionSelector);
    event ContractAdded(bytes32 indexed contractName, address contractAddress);
    event AutoResolveExecuted(uint256 indexed eventId, uint256 timestamp);

    constructor(address admin) {
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(EMERGENCY_ADMIN_ROLE, admin);
        _grantRole(EMERGENCY_OPERATOR_ROLE, admin);
        
        // 初始化配置
        config.maxAutoLevel = EmergencyLevel.MEDIUM;
        config.autoResolveDelay = 24 hours;
        config.allowAutoResolve = true;
        config.cooldownPeriod = 1 hours;
        
        currentEmergencyLevel = EmergencyLevel.NONE;
    }

    /**
     * @dev 触发紧急事件
     */
    function triggerEmergency(
        EmergencyType eventType,
        EmergencyLevel level,
        string calldata description,
        bytes32[] calldata affectedContracts,
        bool autoResolve
    ) external onlyRole(EMERGENCY_OPERATOR_ROLE) returns (uint256) {
        require(level > currentEmergencyLevel, "EmergencyManager: level not higher than current");
        require(
            block.timestamp >= lastEmergencyTime + config.cooldownPeriod,
            "EmergencyManager: cooldown period active"
        );

        uint256 eventId = emergencyEventCount++;
        EmergencyEvent storage newEvent = emergencyEvents[eventId];
        
        newEvent.id = eventId;
        newEvent.eventType = eventType;
        newEvent.level = level;
        newEvent.triggeredAt = block.timestamp;
        newEvent.triggeredBy = msg.sender;
        newEvent.description = description;
        newEvent.isActive = true;
        newEvent.autoResolve = autoResolve;
        newEvent.affectedContracts = affectedContracts;
        
        if (autoResolve && config.allowAutoResolve) {
            newEvent.autoResolveTime = block.timestamp + config.autoResolveDelay;
        }

        // 更新紧急级别
        EmergencyLevel oldLevel = currentEmergencyLevel;
        currentEmergencyLevel = level;
        lastEmergencyTime = block.timestamp;

        // 根据级别执行相应的暂停操作
        _executeEmergencyActions(level, affectedContracts);

        emit EmergencyTriggered(eventId, eventType, level, msg.sender, description);
        emit EmergencyLevelChanged(oldLevel, level, block.timestamp);

        return eventId;
    }

    /**
     * @dev 解决紧急事件
     */
    function resolveEmergency(
        uint256 eventId,
        string calldata resolution
    ) external onlyRole(EMERGENCY_ADMIN_ROLE) {
        EmergencyEvent storage emergencyEvent = emergencyEvents[eventId];
        
        require(emergencyEvent.isActive, "EmergencyManager: event not active");
        require(emergencyEvent.triggeredAt > 0, "EmergencyManager: event does not exist");

        emergencyEvent.isActive = false;
        emergencyEvent.resolvedAt = block.timestamp;
        emergencyEvent.resolvedBy = msg.sender;
        emergencyEvent.resolution = resolution;

        // 恢复暂停的功能
        _restoreFromEmergency(emergencyEvent.level, emergencyEvent.affectedContracts);

        // 更新紧急级别
        EmergencyLevel oldLevel = currentEmergencyLevel;
        currentEmergencyLevel = _calculateCurrentEmergencyLevel();

        emit EmergencyResolved(eventId, msg.sender, resolution);
        emit EmergencyLevelChanged(oldLevel, currentEmergencyLevel, block.timestamp);
    }

    /**
     * @dev 自动解决紧急事件
     */
    function autoResolveEmergency(uint256 eventId) external {
        EmergencyEvent storage emergencyEvent = emergencyEvents[eventId];
        
        if (!emergencyEvent.isActive) revert TokenErrors.InvalidOperation();
        if (!emergencyEvent.autoResolve) revert TokenErrors.InvalidOperation();
        if (block.timestamp < emergencyEvent.autoResolveTime) revert TokenErrors.InvalidOperation();

        emergencyEvent.isActive = false;
        emergencyEvent.resolvedAt = block.timestamp;
        emergencyEvent.resolvedBy = address(0); // 自动解决
        emergencyEvent.resolution = "Auto-resolved after timeout";

        // 恢复暂停的功能
        _restoreFromEmergency(emergencyEvent.level, emergencyEvent.affectedContracts);

        // 更新紧急级别
        EmergencyLevel oldLevel = currentEmergencyLevel;
        currentEmergencyLevel = _calculateCurrentEmergencyLevel();

        emit AutoResolveExecuted(eventId, block.timestamp);
        emit EmergencyLevelChanged(oldLevel, currentEmergencyLevel, block.timestamp);
    }

    /**
     * @dev 暂停特定合约的特定功能
     */
    function pauseFunction(
        bytes32 contractName,
        bytes4 functionSelector
    ) external onlyRole(EMERGENCY_OPERATOR_ROLE) {
        if (monitoredContracts[contractName] == address(0)) revert TokenErrors.ZeroAddress();
        
        pausedFunctions[contractName][functionSelector] = true;
        
        emit FunctionPaused(contractName, functionSelector);
    }

    /**
     * @dev 恢复特定合约的特定功能
     */
    function unpauseFunction(
        bytes32 contractName,
        bytes4 functionSelector
    ) external onlyRole(EMERGENCY_ADMIN_ROLE) {
        pausedFunctions[contractName][functionSelector] = false;
        
        emit FunctionUnpaused(contractName, functionSelector);
    }

    /**
     * @dev 添加监控合约
     */
    function addMonitoredContract(
        bytes32 contractName,
        address contractAddress
    ) external onlyRole(EMERGENCY_ADMIN_ROLE) {
        require(contractAddress != address(0), "EmergencyManager: invalid contract address");
        require(monitoredContracts[contractName] == address(0), "EmergencyManager: contract already monitored");
        
        monitoredContracts[contractName] = contractAddress;
        contractNames.push(contractName);
        
        emit ContractAdded(contractName, contractAddress);
    }

    /**
     * @dev 检查功能是否被暂停
     */
    function isFunctionPaused(
        bytes32 contractName,
        bytes4 functionSelector
    ) external view returns (bool) {
        return pausedFunctions[contractName][functionSelector];
    }

    /**
     * @dev 检查是否处于紧急状态
     */
    function isEmergencyActive() external view returns (bool) {
        return currentEmergencyLevel > EmergencyLevel.NONE;
    }

    /**
     * @dev 获取活跃的紧急事件
     */
    function getActiveEmergencies() external view returns (uint256[] memory) {
        uint256 activeCount = 0;
        
        // 计算活跃事件数量
        for (uint256 i = 0; i < emergencyEventCount; i++) {
            if (emergencyEvents[i].isActive) {
                activeCount++;
            }
        }
        
        // 构建活跃事件数组
        uint256[] memory activeEvents = new uint256[](activeCount);
        uint256 index = 0;
        
        for (uint256 i = 0; i < emergencyEventCount; i++) {
            if (emergencyEvents[i].isActive) {
                activeEvents[index] = i;
                index++;
            }
        }
        
        return activeEvents;
    }

    /**
     * @dev 获取可自动解决的事件
     */
    function getAutoResolvableEvents() external view returns (uint256[] memory) {
        uint256 resolvableCount = 0;
        
        // 计算可解决事件数量
        for (uint256 i = 0; i < emergencyEventCount; i++) {
            EmergencyEvent storage emergencyEvent = emergencyEvents[i];
            if (emergencyEvent.isActive &&
                emergencyEvent.autoResolve &&
                block.timestamp >= emergencyEvent.autoResolveTime) {
                resolvableCount++;
            }
        }

        // 构建可解决事件数组
        uint256[] memory resolvableEvents = new uint256[](resolvableCount);
        uint256 index = 0;

        for (uint256 i = 0; i < emergencyEventCount; i++) {
            EmergencyEvent storage emergencyEvent = emergencyEvents[i];
            if (emergencyEvent.isActive &&
                emergencyEvent.autoResolve &&
                block.timestamp >= emergencyEvent.autoResolveTime) {
                resolvableEvents[index] = i;
                index++;
            }
        }
        
        return resolvableEvents;
    }

    /**
     * @dev 更新紧急配置
     */
    function updateConfig(
        EmergencyLevel maxAutoLevel,
        uint256 autoResolveDelay,
        bool allowAutoResolve,
        uint256 cooldownPeriod
    ) external onlyRole(EMERGENCY_ADMIN_ROLE) {
        config.maxAutoLevel = maxAutoLevel;
        config.autoResolveDelay = autoResolveDelay;
        config.allowAutoResolve = allowAutoResolve;
        config.cooldownPeriod = cooldownPeriod;
    }

    /**
     * @dev 执行紧急操作
     */
    function _executeEmergencyActions(
        EmergencyLevel level,
        bytes32[] memory affectedContracts
    ) internal {
        if (level >= EmergencyLevel.CRITICAL) {
            // 严重级别：暂停所有功能
            _pause();
        } else if (level >= EmergencyLevel.HIGH) {
            // 高级：暂停主要功能
            for (uint256 i = 0; i < affectedContracts.length; i++) {
                _pauseContractMainFunctions(affectedContracts[i]);
            }
        } else if (level >= EmergencyLevel.MEDIUM) {
            // 中级：暂停特定功能
            for (uint256 i = 0; i < affectedContracts.length; i++) {
                _pauseContractSpecificFunctions(affectedContracts[i]);
            }
        }
        // 低级：仅记录，不暂停功能
    }

    /**
     * @dev 从紧急状态恢复
     */
    function _restoreFromEmergency(
        EmergencyLevel level,
        bytes32[] memory affectedContracts
    ) internal {
        if (level >= EmergencyLevel.CRITICAL) {
            _unpause();
        } else {
            for (uint256 i = 0; i < affectedContracts.length; i++) {
                _unpauseContractFunctions(affectedContracts[i]);
            }
        }
    }

    /**
     * @dev 暂停合约主要功能
     */
    function _pauseContractMainFunctions(bytes32 contractName) internal {
        // 这里可以根据具体合约定义主要功能
        // 例如：transfer, mint, burn等
        bytes4[] memory mainFunctions = new bytes4[](3);
        mainFunctions[0] = bytes4(keccak256("transfer(address,uint256)"));
        mainFunctions[1] = bytes4(keccak256("mint(address,uint256)"));
        mainFunctions[2] = bytes4(keccak256("burn(uint256)"));
        
        for (uint256 i = 0; i < mainFunctions.length; i++) {
            pausedFunctions[contractName][mainFunctions[i]] = true;
            emit FunctionPaused(contractName, mainFunctions[i]);
        }
    }

    /**
     * @dev 暂停合约特定功能
     */
    function _pauseContractSpecificFunctions(bytes32 contractName) internal {
        // 根据具体需求暂停特定功能
        bytes4 transferFunction = bytes4(keccak256("transfer(address,uint256)"));
        pausedFunctions[contractName][transferFunction] = true;
        emit FunctionPaused(contractName, transferFunction);
    }

    /**
     * @dev 恢复合约功能
     */
    function _unpauseContractFunctions(bytes32 contractName) internal {
        // 这里需要根据实际情况恢复功能
        // 为简化，这里只是示例
        bytes4[] memory functions = new bytes4[](3);
        functions[0] = bytes4(keccak256("transfer(address,uint256)"));
        functions[1] = bytes4(keccak256("mint(address,uint256)"));
        functions[2] = bytes4(keccak256("burn(uint256)"));
        
        for (uint256 i = 0; i < functions.length; i++) {
            pausedFunctions[contractName][functions[i]] = false;
            emit FunctionUnpaused(contractName, functions[i]);
        }
    }

    /**
     * @dev 计算当前紧急级别
     */
    function _calculateCurrentEmergencyLevel() internal view returns (EmergencyLevel) {
        EmergencyLevel maxLevel = EmergencyLevel.NONE;
        
        for (uint256 i = 0; i < emergencyEventCount; i++) {
            if (emergencyEvents[i].isActive && emergencyEvents[i].level > maxLevel) {
                maxLevel = emergencyEvents[i].level;
            }
        }
        
        return maxLevel;
    }

    /**
     * @dev 获取统计信息
     */
    function getStats() external view returns (
        uint256 totalEvents,
        uint256 activeEvents,
        EmergencyLevel currentLevel,
        uint256 lastEmergency,
        uint256 monitoredContractCount
    ) {
        uint256 activeCount = 0;
        for (uint256 i = 0; i < emergencyEventCount; i++) {
            if (emergencyEvents[i].isActive) {
                activeCount++;
            }
        }
        
        return (
            emergencyEventCount,
            activeCount,
            currentEmergencyLevel,
            lastEmergencyTime,
            contractNames.length
        );
    }
}
