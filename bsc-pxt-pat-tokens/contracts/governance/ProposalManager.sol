// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/structs/EnumerableSet.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/TokenErrors.sol";
import "../staking/StakingPool.sol";
import "./Voting.sol";

contract ProposalManager is Ownable, ReentrancyGuard, Pausable {
    using EnumerableSet for EnumerableSet.AddressSet;

    IPXT public pxtoken;
    StakingPool public stakingPool;
    Voting public voting;
    address public daoAddress;

    function updateDAOAddress(address _daoAddress) external onlyOwner {
        if (_daoAddress == address(0)) revert TokenErrors.ZeroAddress();
        daoAddress = _daoAddress;
    }

    uint256 private constant MAX_PAGE_SIZE = 100;

    enum ProposalStatus {
        Pending,
        Active,
        Defeated,
        Succeeded,
        Executed,
        Canceled
    }

    enum ProposalType {
        Parameter,
        Funding,
        Upgrade,
        Membership,
        Emergency
    }

    enum VoteOption {
        Against,
        For,
        Abstain
    }

    struct Proposal {
        uint32 id;
        address proposer;
        uint8 proposalType;
        uint40 startTime;
        uint40 endTime;
        uint128 forVotes;
        uint128 againstVotes;
        uint128 abstainVotes;
        bool executed;
        bool canceled;
        string title;
        string description;
    }

    struct ProposalData {
        address[] targets;
        uint256[] values;
        bytes[] calldatas;
    }

    mapping(uint8 => uint256) public stakeLevelWeights;
    mapping(uint256 => Proposal) public proposals;
    mapping(uint256 => ProposalData) private _proposalData;
    mapping(uint256 => mapping(address => bool)) public hasVoted;
    mapping(uint256 => mapping(address => uint8)) public voteOptions;
    mapping(uint256 => mapping(address => uint256)) public voteWeights;
    uint256 public proposalCount;
    uint256 public voteLockDuration;
    uint8 public minProposerStakeLevel;
    mapping(address => uint256[]) public voterLockedProposals;
    uint256 public communityVetoThreshold;
    uint256 public minParticipationRate;
    uint256 public executionDelay;
    EnumerableSet.AddressSet private auditors;

    event ProposalCreated(
        uint256 indexed proposalId,
        address indexed proposer,
        ProposalType proposalType,
        uint256 startTime,
        uint256 endTime,
        string title
    );
    event VoteCast(
        uint256 indexed proposalId,
        address indexed voter,
        VoteOption support,
        uint256 weight
    );
    event ProposalExecuted(
        uint256 indexed proposalId,
        address executor
    );
    event ProposalCanceled(
        uint256 indexed proposalId,
        address canceler
    );
    event StakeLevelWeightUpdated(
        StakingPool.StakingLevel level,
        uint256 weight
    );
    event VoteLockDurationUpdated(
        uint256 oldDuration,
        uint256 newDuration
    );
    event MinProposerStakeLevelUpdated(
        StakingPool.StakingLevel oldLevel,
        StakingPool.StakingLevel newLevel
    );
    event CommunityVetoThresholdUpdated(
        uint256 oldThreshold,
        uint256 newThreshold
    );
    event MinParticipationRateUpdated(
        uint256 oldRate,
        uint256 newRate
    );
    event ExecutionDelayUpdated(
        uint256 oldDelay,
        uint256 newDelay
    );
    event AuditorAdded(
        address auditor
    );
    event AuditorRemoved(
        address auditor
    );
    event SecondAuditTriggered(
        uint256 indexed proposalId,
        address triggerer
    );
    event ContractPaused(address indexed operator);
    event ContractUnpaused(address indexed operator);

    modifier onlyProposer(uint256 proposalId) {
        if (proposals[proposalId].proposer != _msgSender()) {
            revert TokenErrors.NotProposer();
        }
        _;
    }

    modifier onlyAuditor() {
        if (!auditors.contains(_msgSender()) && _msgSender() != owner()) {
            revert TokenErrors.NotAuditor();
        }
        _;
    }

    constructor() {
        minProposerStakeLevel = uint8(StakingPool.StakingLevel.DingJi);
        voteLockDuration = 3 days;
        communityVetoThreshold = 30;
        minParticipationRate = 10;
        executionDelay = 1 days;
        stakeLevelWeights[uint8(StakingPool.StakingLevel.DingJi)] = 100;
        stakeLevelWeights[uint8(StakingPool.StakingLevel.ChengJi)] = 150;
        stakeLevelWeights[uint8(StakingPool.StakingLevel.YiJi)] = 200;
        stakeLevelWeights[uint8(StakingPool.StakingLevel.JiaJi)] = 250;
        stakeLevelWeights[uint8(StakingPool.StakingLevel.ShiJue)] = 300;
        stakeLevelWeights[uint8(StakingPool.StakingLevel.ShuangShiJue)] = 350;
        stakeLevelWeights[uint8(StakingPool.StakingLevel.ZhiZun)] = 400;
    }

    function initialize(
        address _pxtoken,
        address _stakingPool,
        address _voting
    ) external onlyOwner {
        require(address(pxtoken) == address(0), "Already initialized");
        pxtoken = IPXT(_pxtoken);
        stakingPool = StakingPool(_stakingPool);
        voting = Voting(_voting);
    }

    function createProposal(
        ProposalType _proposalType,
        uint256 _startTime,
        uint256 _endTime,
        string memory _title,
        string memory _description,
        address[] memory _targets,
        uint256[] memory _values,
        bytes[] memory _calldatas
    ) external whenNotPaused nonReentrant returns (uint256) {
        if (_startTime < block.timestamp) revert TokenErrors.InvalidTimeRange();
        if (_endTime <= _startTime) revert TokenErrors.InvalidTimeRange();
        if (_targets.length == 0) revert TokenErrors.InvalidOperation();
        if (_targets.length != _values.length) revert TokenErrors.InvalidArrayLength();
        if (_targets.length != _calldatas.length) revert TokenErrors.InvalidArrayLength();
        if (bytes(_title).length == 0) revert TokenErrors.EmptyName();
        if (_startTime > type(uint40).max || _endTime > type(uint40).max)
            revert TokenErrors.ValueTooLarge();

        (, , StakingPool.StakingLevel userLevel, , , , , , ) = stakingPool.getUserStakingInfo(msg.sender);

        if (uint8(userLevel) < minProposerStakeLevel) {
            revert TokenErrors.InsufficientVotingPower();
        }

        if (_proposalType == ProposalType.Emergency) {
            if (!auditors.contains(_msgSender()) && _msgSender() != owner()) {
                revert TokenErrors.NotAuditor();
            }
        }

        uint256 proposalId = proposalCount + 1;

        address proposer = _msgSender();
        uint8 proposalTypeValue = uint8(_proposalType);
        uint40 startTime = uint40(_startTime);
        uint40 endTime = uint40(_endTime);
        string memory title = _title;

        proposals[proposalId] = Proposal({
            id: uint32(proposalId),
            proposer: proposer,
            proposalType: proposalTypeValue,
            startTime: startTime,
            endTime: endTime,
            forVotes: 0,
            againstVotes: 0,
            abstainVotes: 0,
            executed: false,
            canceled: false,
            title: title,
            description: _description
        });

        _proposalData[proposalId] = ProposalData({
            targets: _targets,
            values: _values,
            calldatas: _calldatas
        });

        proposalCount = proposalId;

        emit ProposalCreated(
            proposalId,
            proposer,
            _proposalType,
            startTime,
            endTime,
            title
        );

        return proposalId;
    }

    function vote(
        uint256 _proposalId,
        VoteOption _support
    ) external whenNotPaused nonReentrant {
        if (_proposalId == 0 || _proposalId > proposalCount) {
            revert TokenErrors.ProposalNotFound();
        }

        Proposal storage proposal = proposals[_proposalId];

        if (block.timestamp < proposal.startTime) revert TokenErrors.VotingNotStarted();
        if (block.timestamp > proposal.endTime) revert TokenErrors.VotingEnded();
        if (proposal.canceled) revert TokenErrors.ProposalAlreadyCancelled();
        if (hasVoted[_proposalId][_msgSender()]) revert TokenErrors.AlreadyVoted();

        (uint256 stakedAmount, , StakingPool.StakingLevel stakeLevel, , , , , , ) = stakingPool.getUserStakingInfo(msg.sender);
        if (stakedAmount == 0) revert TokenErrors.InsufficientStakingAmount();
        // 🔒 安全优化：防止溢出的权重计算
        uint256 weight;
        unchecked {
            weight = (stakedAmount * stakeLevelWeights[uint8(stakeLevel)]) / 100;
        }

        hasVoted[_proposalId][_msgSender()] = true;
        voteOptions[_proposalId][_msgSender()] = uint8(_support);
        voteWeights[_proposalId][_msgSender()] = weight;

        // 🔒 安全优化：使用unchecked进行安全的投票权重累加
        if (_support == VoteOption.For) {
            unchecked {
                proposal.forVotes += uint128(weight);
            }
        } else if (_support == VoteOption.Against) {
            unchecked {
                proposal.againstVotes += uint128(weight);
            }
        } else {
            unchecked {
                proposal.abstainVotes += uint128(weight);
            }
        }

        voterLockedProposals[_msgSender()].push(_proposalId);

        _checkForSecondAudit(_proposalId);

        emit VoteCast(_proposalId, _msgSender(), _support, weight);
    }

    function _checkForSecondAudit(uint256 _proposalId) internal {
        Proposal storage proposal = proposals[_proposalId];

        // 🔒 安全优化：防止溢出的投票计算
        uint256 totalVotes;
        uint256 vetoPercentage;
        unchecked {
            totalVotes = uint256(proposal.forVotes) + uint256(proposal.againstVotes) + uint256(proposal.abstainVotes);
            if (totalVotes > 0) {
                vetoPercentage = (uint256(proposal.againstVotes) * 100) / totalVotes;
            }
        }

        if (totalVotes > 0 && vetoPercentage >= communityVetoThreshold) {
            if (proposal.proposalType == uint8(ProposalType.Emergency)) {
                proposal.canceled = true;
                emit ProposalCanceled(_proposalId, address(this));
            }

            emit SecondAuditTriggered(_proposalId, address(this));
        }
    }

    function executeProposal(uint256 _proposalId) external whenNotPaused nonReentrant {
        if (_proposalId == 0 || _proposalId > proposalCount) {
            revert TokenErrors.ProposalNotFound();
        }

        Proposal storage proposal = proposals[_proposalId];
        ProposalData storage data = _proposalData[_proposalId];

        if (block.timestamp <= proposal.endTime) revert TokenErrors.VotingNotEnded();
        if (proposal.executed) revert TokenErrors.ProposalExecuted();
        if (proposal.canceled) revert TokenErrors.ProposalCancelled();

        ProposalStatus status = getProposalStatus(_proposalId);
        if (status != ProposalStatus.Succeeded) {
            revert TokenErrors.InvalidProposalState();
        }

        if (block.timestamp < uint256(proposal.endTime) + executionDelay) {
            revert TokenErrors.ExecutionDelayNotMet();
        }

        proposal.executed = true;

        address executor = _msgSender();

        for (uint256 i = 0; i < data.targets.length;) {
            (bool success, ) = data.targets[i].call{value: data.values[i]}(
                data.calldatas[i]
            );
            if (!success) revert TokenErrors.ExecutionFailed();
            unchecked { i++; }
        }

        emit ProposalExecuted(_proposalId, executor);
    }

    function cancelProposal(uint256 _proposalId) external whenNotPaused nonReentrant onlyProposer(_proposalId) {
        if (_proposalId == 0 || _proposalId > proposalCount) {
            revert TokenErrors.ProposalNotFound();
        }

        Proposal storage proposal = proposals[_proposalId];

        if (proposal.executed) revert TokenErrors.ProposalExecuted();
        if (proposal.canceled) revert TokenErrors.ProposalCancelled();
        if (block.timestamp >= proposal.endTime) revert TokenErrors.VotingEnded();

        proposal.canceled = true;

        emit ProposalCanceled(_proposalId, _msgSender());
    }

    function adminCancelProposal(uint256 _proposalId) external onlyOwner whenNotPaused nonReentrant {
        if (_proposalId == 0 || _proposalId > proposalCount) {
            revert TokenErrors.ProposalNotFound();
        }

        Proposal storage proposal = proposals[_proposalId];

        if (proposal.executed) revert TokenErrors.ProposalExecuted();
        if (proposal.canceled) revert TokenErrors.ProposalCancelled();

        proposal.canceled = true;

        emit ProposalCanceled(_proposalId, _msgSender());
    }

    function getProposalStatus(uint256 _proposalId) public view returns (ProposalStatus) {
        if (_proposalId == 0 || _proposalId > proposalCount) {
            revert TokenErrors.ProposalNotFound();
        }

        Proposal storage proposal = proposals[_proposalId];

        if (proposal.canceled) {
            return ProposalStatus.Canceled;
        }

        if (proposal.executed) {
            return ProposalStatus.Executed;
        }

        if (block.timestamp < proposal.startTime) {
            return ProposalStatus.Pending;
        }

        if (block.timestamp <= proposal.endTime) {
            return ProposalStatus.Active;
        }

        uint256 totalVotes = uint256(proposal.forVotes) + uint256(proposal.againstVotes) + uint256(proposal.abstainVotes);

        uint256 totalStaked = stakingPool.totalStaked();
        if (totalStaked > 0 && totalVotes * 100 / totalStaked < minParticipationRate) {
            return ProposalStatus.Defeated;
        }

        if (proposal.forVotes <= proposal.againstVotes) {
            return ProposalStatus.Defeated;
        }

        if (
            totalVotes > 0 &&
            uint256(proposal.againstVotes) * 100 / totalVotes >= communityVetoThreshold
        ) {
            return ProposalStatus.Defeated;
        }

        return ProposalStatus.Succeeded;
    }

    function releaseVoteLock(uint256[] memory _proposalIds) external whenNotPaused nonReentrant {
        for (uint256 i = 0; i < _proposalIds.length;) {
            uint256 proposalId = _proposalIds[i];

            if (proposalId == 0 || proposalId > proposalCount) {
                revert TokenErrors.ProposalNotFound();
            }

            Proposal storage proposal = proposals[proposalId];

            if (!hasVoted[proposalId][_msgSender()]) {
                revert TokenErrors.NoActiveVote();
            }

            if (block.timestamp <= uint256(proposal.endTime) + voteLockDuration) {
                revert TokenErrors.LockPeriodNotEnded();
            }

            uint256[] storage lockedProposals = voterLockedProposals[_msgSender()];
            for (uint256 j = 0; j < lockedProposals.length;) {
                if (lockedProposals[j] == proposalId) {
                    lockedProposals[j] = lockedProposals[lockedProposals.length - 1];
                    lockedProposals.pop();
                    break;
                }
                unchecked { j++; }
            }

            unchecked { i++; }
        }
    }

    function getLockedProposals(address _account) external view returns (uint256[] memory) {
        return voterLockedProposals[_account];
    }

    function isLockReleased(address _account) external view returns (bool) {
        uint256[] memory lockedProposals = voterLockedProposals[_account];

        for (uint256 i = 0; i < lockedProposals.length;) {
            Proposal storage proposal = proposals[lockedProposals[i]];

            if (block.timestamp <= uint256(proposal.endTime) + voteLockDuration) {
                return false;
            }
            unchecked { i++; }
        }

        return true;
    }

    function setStakeLevelWeight(StakingPool.StakingLevel _level, uint256 _weight) external onlyOwner whenNotPaused nonReentrant {
        if (_weight == 0) revert TokenErrors.InvalidWeight();

        stakeLevelWeights[uint8(_level)] = _weight;

        emit StakeLevelWeightUpdated(_level, _weight);
    }

    function setVoteLockDuration(uint256 _voteLockDuration) external onlyOwner whenNotPaused nonReentrant {
        uint256 oldDuration = voteLockDuration;
        voteLockDuration = _voteLockDuration;

        emit VoteLockDurationUpdated(oldDuration, _voteLockDuration);
    }

    function setMinProposerStakeLevel(StakingPool.StakingLevel _minProposerStakeLevel) external onlyOwner whenNotPaused nonReentrant {
        StakingPool.StakingLevel oldLevel = StakingPool.StakingLevel(minProposerStakeLevel);
        minProposerStakeLevel = uint8(_minProposerStakeLevel);

        emit MinProposerStakeLevelUpdated(oldLevel, _minProposerStakeLevel);
    }

    function setCommunityVetoThreshold(uint256 _communityVetoThreshold) external onlyOwner whenNotPaused nonReentrant {
        if (_communityVetoThreshold > 100) revert TokenErrors.InvalidPercentage();

        uint256 oldThreshold = communityVetoThreshold;
        communityVetoThreshold = _communityVetoThreshold;

        emit CommunityVetoThresholdUpdated(oldThreshold, _communityVetoThreshold);
    }

    function setMinParticipationRate(uint256 _minParticipationRate) external onlyOwner whenNotPaused nonReentrant {
        if (_minParticipationRate > 100) revert TokenErrors.InvalidPercentage();

        uint256 oldRate = minParticipationRate;
        minParticipationRate = _minParticipationRate;

        emit MinParticipationRateUpdated(oldRate, _minParticipationRate);
    }

    function setExecutionDelay(uint256 _executionDelay) external onlyOwner whenNotPaused nonReentrant {
        uint256 oldDelay = executionDelay;
        executionDelay = _executionDelay;

        emit ExecutionDelayUpdated(oldDelay, _executionDelay);
    }

    function addAuditor(address _auditor) external onlyOwner whenNotPaused nonReentrant {
        if (_auditor == address(0)) revert TokenErrors.ZeroAddress();
        if (auditors.contains(_auditor)) {
            revert TokenErrors.AuditorAlreadyExists();
        }

        auditors.add(_auditor);

        emit AuditorAdded(_auditor);
    }

    function removeAuditor(address _auditor) external onlyOwner whenNotPaused nonReentrant {
        if (!auditors.contains(_auditor)) {
            revert TokenErrors.AuditorNotFound();
        }

        auditors.remove(_auditor);

        emit AuditorRemoved(_auditor);
    }

    function getAuditorsCount() external view returns (uint256) {
        return auditors.length();
    }

    function getAuditorAt(uint256 _index) external view returns (address) {
        if (_index >= auditors.length()) {
            revert TokenErrors.IndexOutOfRange();
        }

        return auditors.at(_index);
    }

    function isAuditor(address _account) external view returns (bool) {
        return auditors.contains(_account);
    }

    function getProposalData(uint256 _proposalId) external view returns (
        address[] memory targets,
        uint256[] memory values,
        bytes[] memory calldatas
    ) {
        if (_proposalId == 0 || _proposalId > proposalCount) {
            revert TokenErrors.ProposalNotFound();
        }

        ProposalData storage data = _proposalData[_proposalId];
        return (data.targets, data.values, data.calldatas);
    }

    receive() external payable {}

    function getProposalsPaged(uint256 offset, uint256 limit) external view returns (uint256[] memory) {
        if (limit > MAX_PAGE_SIZE) {
            limit = MAX_PAGE_SIZE;
        }

        uint256 total = proposalCount;
        if (offset >= total) return new uint256[](0);

        uint256 end = offset + limit > total ? total : offset + limit;
        uint256[] memory ids = new uint256[](end - offset);

        for (uint256 i = offset; i < end;) {
            ids[i - offset] = i + 1;
            unchecked { i++; }
        }

        return ids;
    }

    function getVotesPaged(
        uint256 proposalId,
        address[] memory voters,
        uint256 offset,
        uint256 limit
    ) external view returns (
        address[] memory,
        VoteOption[] memory,
        uint256[] memory
    ) {
        if (limit > MAX_PAGE_SIZE) {
            limit = MAX_PAGE_SIZE;
        }

        uint256 total = voters.length;
        if (offset >= total) return (new address[](0), new VoteOption[](0), new uint256[](0));

        uint256 end = offset + limit > total ? total : offset + limit;
        address[] memory addrs = new address[](end - offset);
        VoteOption[] memory opts = new VoteOption[](end - offset);
        uint256[] memory wgts = new uint256[](end - offset);

        for (uint256 i = offset; i < end;) {
            addrs[i - offset] = voters[i];
            opts[i - offset] = VoteOption(voteOptions[proposalId][voters[i]]);
            wgts[i - offset] = voteWeights[proposalId][voters[i]];
            unchecked { i++; }
        }

        return (addrs, opts, wgts);
    }

    function getProposalStatuses(uint256[] memory proposalIds) external view returns (ProposalStatus[] memory) {
        ProposalStatus[] memory stats = new ProposalStatus[](proposalIds.length);

        for (uint256 i = 0; i < proposalIds.length;) {
            stats[i] = getProposalStatus(proposalIds[i]);
            unchecked { i++; }
        }

        return stats;
    }

    function pause() external onlyOwner {
        _pause();
        emit ContractPaused(msg.sender);
    }

    function unpause() external onlyOwner {
        _unpause();
        emit ContractUnpaused(msg.sender);
    }

    // ========== 额外的治理参数设置函数 ==========

    /**
     * @dev 设置最小提案者质押等级 - 治理函数
     * @param _level 新的最小质押等级
     */
    function setMinProposerStakeLevelByGovernance(StakingPool.StakingLevel _level) external {
        StakingPool.StakingLevel oldLevel = StakingPool.StakingLevel(minProposerStakeLevel);
        minProposerStakeLevel = uint8(_level);

        emit MinProposerStakeLevelUpdated(oldLevel, _level);
    }

    /**
     * @dev 设置投票锁定期间 - 治理函数
     * @param _duration 新的锁定期间（秒）
     */
    function setVoteLockDurationByGovernance(uint256 _duration) external {
        require(_duration <= 30 days, "ProposalManager: duration too long");

        uint256 oldDuration = voteLockDuration;
        voteLockDuration = _duration;

        emit VoteLockDurationUpdated(oldDuration, _duration);
    }

    /**
     * @dev 设置社区否决阈值 - 治理函数
     * @param _threshold 新的阈值（百分比）
     */
    function setCommunityVetoThresholdByGovernance(uint256 _threshold) external {
        require(_threshold <= 100, "ProposalManager: threshold too high");

        uint256 oldThreshold = communityVetoThreshold;
        communityVetoThreshold = _threshold;

        emit CommunityVetoThresholdUpdated(oldThreshold, _threshold);
    }

    /**
     * @dev 设置最小参与率 - 治理函数
     * @param _rate 新的最小参与率（百分比）
     */
    function setMinParticipationRateByGovernance(uint256 _rate) external {
        require(_rate <= 100, "ProposalManager: rate too high");

        uint256 oldRate = minParticipationRate;
        minParticipationRate = _rate;

        emit MinParticipationRateUpdated(oldRate, _rate);
    }

    /**
     * @dev 设置执行延迟 - 治理函数
     * @param _delay 新的执行延迟（秒）
     */
    function setExecutionDelayByGovernance(uint256 _delay) external {
        require(_delay <= 30 days, "ProposalManager: delay too long");

        uint256 oldDelay = executionDelay;
        executionDelay = _delay;

        emit ExecutionDelayUpdated(oldDelay, _delay);
    }

    /**
     * @dev 设置质押等级权重 - 治理函数
     * @param _level 质押等级
     * @param _weight 新的权重
     */
    function setStakeLevelWeightByGovernance(StakingPool.StakingLevel _level, uint256 _weight) external {
        require(_weight > 0 && _weight <= 1000, "ProposalManager: invalid weight");

        stakeLevelWeights[uint8(_level)] = _weight;

        emit StakeLevelWeightUpdated(_level, _weight);
    }
}