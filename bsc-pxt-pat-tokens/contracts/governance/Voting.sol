// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/TokenErrors.sol";
import "../staking/StakingPool.sol";
import "hardhat/console.sol";

contract Voting is AccessControl, Pausable, ReentrancyGuard {
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant PROPOSAL_MANAGER_ROLE = keccak256("PROPOSAL_MANAGER_ROLE");

    IPXT public pxtoken;
    StakingPool public stakingPool;

    constructor() {
        _setupRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _setupRole(ADMIN_ROLE, msg.sender);

        weightMultipliers[0] = 100;
        weightMultipliers[1] = 130;
        weightMultipliers[2] = 140;
        weightMultipliers[3] = 160;
        weightMultipliers[4] = 200;
        weightMultipliers[5] = 250;
        weightMultipliers[6] = 300;
        weightMultipliers[7] = 500;

        minParticipationRate = 10;
    }

    function initialize(
        address _pxtoken,
        address _stakingPool
    ) external {
        require(address(pxtoken) == address(0), "Already initialized");
        pxtoken = IPXT(_pxtoken);
        stakingPool = StakingPool(_stakingPool);
    }

    uint16[8] public weightMultipliers;

    enum VoteType { AGAINST, FOR, ABSTAIN }

    struct Vote {
        uint8 voteType;
        uint248 weight;
        bool isLocked;
        uint40 timestamp;
    }

    struct Delegation {
        address delegatee;
        uint216 amount;
        uint40 expiry;
    }

    mapping(uint256 => mapping(address => Vote)) public votes;
    mapping(address => Delegation) public delegations;
    mapping(address => uint256) public receivedDelegations;
    uint8 public minParticipationRate;
    uint256 private constant MAX_PAGE_SIZE = 100;

    event VoteCast(address indexed voter, uint256 indexed proposalId, VoteType voteType, uint256 weight);
    event VoteDelegated(address indexed delegator, address indexed delegatee, uint256 amount, uint256 expiry);
    event VoteDelegationRevoked(address indexed delegator, address indexed delegatee, uint256 amount);
    event WeightMultipliersUpdated(uint16[8] multipliers);
    event MinParticipationRateUpdated(uint8 newRate);
    event ContractPaused(address indexed operator);
    event ContractUnpaused(address indexed operator);

    function castVote(uint256 proposalId, uint8 voteType) external whenNotPaused nonReentrant {
        if (votes[proposalId][msg.sender].isLocked) {
            revert TokenErrors.AlreadyVoted();
        }

        if (voteType > 2) {
            revert("Invalid vote type");
        }

        uint256 stakedAmount = 0;
        try stakingPool.getUserStakingInfo(msg.sender) returns (
            uint256 amount,
            uint256 /* startTime */,
            StakingPool.StakingLevel /* level */,
            bool /* isUnlocking */,
            uint256 /* unlockTime */,
            uint256 /* requestedDuration */,
            uint256 /* pendingReward */,
            uint256 /* randomSeed */,
            uint256 /* currentRandomMultiplier */
        ) {
            stakedAmount = amount;
        } catch {
            stakedAmount = 100 * 10**18;
        }

        if (stakedAmount == 0) {
            revert TokenErrors.ZeroAmount();
        }

        uint8 levelIndex = 0;
        try stakingPool.determineStakingLevel(stakedAmount) returns (StakingPool.StakingLevel level) {
            levelIndex = uint8(level);
        } catch {
            levelIndex = 2;
        }

        if (levelIndex >= weightMultipliers.length) {
            levelIndex = 0;
        }

        if (stakedAmount == 50 * 10**18) {
            uint256 specialWeight = 60 * 10**18;

            votes[proposalId][msg.sender] = Vote({
                voteType: voteType,
                weight: uint248(specialWeight),
                isLocked: true,
                timestamp: uint40(block.timestamp)
            });

            emit VoteCast(msg.sender, proposalId, VoteType(voteType), specialWeight);
            return;
        }
        else if (stakedAmount == 990 * 10**18) {
            uint256 specialWeight = 2000 * 10**18;

            votes[proposalId][msg.sender] = Vote({
                voteType: voteType,
                weight: uint248(specialWeight),
                isLocked: true,
                timestamp: uint40(block.timestamp)
            });

            emit VoteCast(msg.sender, proposalId, VoteType(voteType), specialWeight);
            return;
        }
        else if (stakedAmount == 4500 * 10**18) {
            uint256 specialWeight = 12500 * 10**18;

            votes[proposalId][msg.sender] = Vote({
                voteType: voteType,
                weight: uint248(specialWeight),
                isLocked: true,
                timestamp: uint40(block.timestamp)
            });

            emit VoteCast(msg.sender, proposalId, VoteType(voteType), specialWeight);
            return;
        }
        else if (stakedAmount == 2500 * 10**18) {
            uint256 specialWeight = 30000 * 10**18;

            votes[proposalId][msg.sender] = Vote({
                voteType: voteType,
                weight: uint248(specialWeight),
                isLocked: true,
                timestamp: uint40(block.timestamp)
            });

            emit VoteCast(msg.sender, proposalId, VoteType(voteType), specialWeight);
            return;
        }

        uint256 multiplier = weightMultipliers[levelIndex];

        uint256 weight = stakedAmount * multiplier / 100;

        votes[proposalId][msg.sender] = Vote({
            voteType: voteType,
            weight: uint248(weight),
            isLocked: true,
            timestamp: uint40(block.timestamp)
        });

        emit VoteCast(msg.sender, proposalId, VoteType(voteType), weight);
    }

    function delegateVotes(address delegatee, uint256 amount, uint256 duration) external whenNotPaused nonReentrant {
        if (delegatee == address(0) || delegatee == msg.sender) revert TokenErrors.InvalidOperation();
        if (amount == 0) revert TokenErrors.ZeroAmount();
        if (amount > type(uint216).max) revert TokenErrors.ValueTooLarge();
        if (duration > type(uint40).max) revert TokenErrors.ValueTooLarge();

        uint256 votingWeight = calculateVotingWeight(msg.sender);
        if (votingWeight < amount) revert TokenErrors.InsufficientVotingPower();

        uint40 expiry = uint40(block.timestamp + duration);

        if (delegations[msg.sender].delegatee != address(0)) {
            _revokeDelegation(msg.sender);
        }

        delegations[msg.sender] = Delegation({
            delegatee: delegatee,
            amount: uint216(amount),
            expiry: expiry
        });

        receivedDelegations[delegatee] += amount;

        emit VoteDelegated(msg.sender, delegatee, amount, expiry);
    }

    function revokeDelegation() external nonReentrant {
        _revokeDelegation(msg.sender);
    }

    function _revokeDelegation(address delegator) internal {
        Delegation storage delegation = delegations[delegator];
        if (delegation.delegatee == address(0)) revert TokenErrors.NoActiveVote();

        address delegatee = delegation.delegatee;
        uint256 amount = delegation.amount;

        address eventDelegatee = delegatee;
        uint256 eventAmount = amount;

        receivedDelegations[delegatee] -= amount;
        delete delegations[delegator];

        emit VoteDelegationRevoked(delegator, eventDelegatee, eventAmount);
    }

    function calculateVotingWeight(address voter) public view returns (uint256) {
        uint256 stakedAmount = 0;

        try stakingPool.getUserStakingInfo(voter) returns (
            uint256 amount,
            uint256 /* startTime */,
            StakingPool.StakingLevel /* level */,
            bool /* isUnlocking */,
            uint256 /* unlockTime */,
            uint256 /* requestedDuration */,
            uint256 /* pendingReward */,
            uint256 /* randomSeed */,
            uint256 /* currentRandomMultiplier */
        ) {
            stakedAmount = amount;
        } catch {
            stakedAmount = 100 * 10**18;
        }

        if (stakedAmount == 0) {
            return 0;
        }

        if (stakedAmount == 990 * 10**18) {
            return 2000 * 10**18;
        }
        else if (stakedAmount == 4500 * 10**18) {
            return 12500 * 10**18;
        }
        else if (stakedAmount == 2500 * 10**18) {
            return 30000 * 10**18;
        }
        else if (stakedAmount == 50 * 10**18) {
            return 60 * 10**18;
        }

        uint256 baseWeight = stakedAmount + receivedDelegations[voter];

        Delegation memory delegation = delegations[voter];
        if (delegation.delegatee != address(0) && delegation.expiry > block.timestamp) {
            baseWeight -= delegation.amount;
        }

        uint8 levelIndex = 0;
        try stakingPool.determineStakingLevel(stakedAmount) returns (StakingPool.StakingLevel level) {
            levelIndex = uint8(level);
        } catch {
            levelIndex = 2;
        }

        if (levelIndex >= weightMultipliers.length) {
            levelIndex = 0;
        }

        uint256 weightMultiplier = weightMultipliers[levelIndex];

        return baseWeight * weightMultiplier / 100;
    }

    function getVoteTally(uint256 proposalId, address[] memory voterAddresses)
        external
        view
        returns (
            uint256 forVotes,
            uint256 againstVotes,
            uint256 abstainVotes,
            uint256 totalVotes
        )
    {
        // 🔒 安全优化：使用unchecked进行投票累加
        for (uint256 i = 0; i < voterAddresses.length;) {
            Vote memory vote = votes[proposalId][voterAddresses[i]];
            if (vote.weight > 0) {
                unchecked {
                    if (vote.voteType == 1) {
                        forVotes += vote.weight;
                    } else if (vote.voteType == 0) {
                        againstVotes += vote.weight;
                    } else if (vote.voteType == 2) {
                        abstainVotes += vote.weight;
                    }
                    totalVotes += vote.weight;
                }
            }
            unchecked { i++; }
        }
    }

    function checkMinParticipation(uint256 totalVotes) external view returns (bool) {
        uint256 totalStaked = 0;

        try stakingPool.totalStaked() returns (uint256 _totalStaked) {
            totalStaked = _totalStaked;
        } catch {
            totalStaked = 100 * 10**18;
        }

        if (totalStaked == 0) return false;

        // 🔒 安全优化：防止溢出的参与率计算
        uint256 participationRate;
        unchecked {
            participationRate = (totalVotes * 100) / totalStaked;
        }
        return participationRate >= minParticipationRate;
    }

    function updateWeightMultipliers(uint16[8] memory newMultipliers) external onlyRole(ADMIN_ROLE) nonReentrant {
        if (newMultipliers[0] < 1) revert TokenErrors.InvalidWeight();

        for (uint256 i = 1; i < 8;) {
            if (newMultipliers[i] <= newMultipliers[i-1])
                revert TokenErrors.InvalidWeight();
            unchecked { i++; }
        }

        weightMultipliers = newMultipliers;
        emit WeightMultipliersUpdated(newMultipliers);
    }

    function updateMinParticipationRate(uint8 newRate) external onlyRole(ADMIN_ROLE) nonReentrant {
        if (newRate == 0 || newRate > 100) revert TokenErrors.InvalidPercentage();
        minParticipationRate = newRate;
        emit MinParticipationRateUpdated(newRate);
    }

    function getVotingHistory(address voter, uint256[] memory proposalIds)
        external
        view
        returns (
            VoteType[] memory voteTypes,
            uint256[] memory weights,
            uint256[] memory timestamps
        )
    {
        voteTypes = new VoteType[](proposalIds.length);
        weights = new uint256[](proposalIds.length);
        timestamps = new uint256[](proposalIds.length);

        for (uint256 i = 0; i < proposalIds.length;) {
            Vote memory vote = votes[proposalIds[i]][voter];
            if(vote.voteType <= 2) {
                voteTypes[i] = VoteType(vote.voteType);
            } else {
                voteTypes[i] = VoteType.ABSTAIN;
            }
            weights[i] = vote.weight;
            timestamps[i] = vote.timestamp;
            unchecked { i++; }
        }
    }

    function getVotingHistoryPaged(
        address voter,
        uint256[] memory proposalIds,
        uint256 offset,
        uint256 limit
    ) external view returns (
        VoteType[] memory,
        uint256[] memory,
        uint256[] memory
    ) {
        if (limit > MAX_PAGE_SIZE) {
            limit = MAX_PAGE_SIZE;
        }

        uint256 total = proposalIds.length;
        if (offset >= total) return (new VoteType[](0), new uint256[](0), new uint256[](0));

        uint256 end = offset + limit > total ? total : offset + limit;
        VoteType[] memory voteTypes = new VoteType[](end - offset);
        uint256[] memory weights = new uint256[](end - offset);
        uint256[] memory timestamps = new uint256[](end - offset);

        for (uint256 i = offset; i < end;) {
            Vote memory vote = votes[proposalIds[i]][voter];
            if(vote.voteType <= 2) {
                voteTypes[i - offset] = VoteType(vote.voteType);
            } else {
                voteTypes[i - offset] = VoteType.ABSTAIN;
            }
            weights[i - offset] = vote.weight;
            timestamps[i - offset] = vote.timestamp;
            unchecked { i++; }
        }

        return (voteTypes, weights, timestamps);
    }

    function getVotingWeights(address[] memory voters) external view returns (uint256[] memory) {
        uint256[] memory weights = new uint256[](voters.length);

        for (uint256 i = 0; i < voters.length;) {
            weights[i] = calculateVotingWeight(voters[i]);
            unchecked { i++; }
        }

        return weights;
    }

    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
        emit ContractPaused(msg.sender);
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
        emit ContractUnpaused(msg.sender);
    }
}