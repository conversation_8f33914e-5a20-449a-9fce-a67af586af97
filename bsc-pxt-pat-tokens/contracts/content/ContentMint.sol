// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/Counters.sol";

import "./interfaces/IContentRegistry.sol";
import "../interfaces/TokenErrors.sol";

/**
 * @title ContentMint
 * @dev 内容铸造NFT合约 - 学习xLog的内容铸造机制
 * 用户可以铸造内容NFT来支持创作者，类似于xLog的mint功能
 */
contract ContentMint is ERC721, ERC721URIStorage, Ownable, Pausable {
    using Counters for Counters.Counter;
    
    // 状态变量
    IContentRegistry public immutable contentRegistry;  // 内容注册合约
    address public treasuryAddress;                     // 国库地址
    
    Counters.Counter private _tokenIdCounter;           // NFT ID计数器
    
    // 铸造信息结构体
    struct MintInfo {
        uint256 tokenId;        // NFT ID
        uint256 contentId;      // 内容ID
        address minter;         // 铸造者
        address creator;        // 创作者
        uint256 mintPrice;      // 铸造价格
        uint256 timestamp;      // 铸造时间
        string contentType;     // 内容类型
        string title;           // 内容标题
    }
    
    // 存储映射
    mapping(uint256 => MintInfo) public mintInfos;              // NFT铸造信息
    mapping(uint256 => uint256[]) public contentMints;         // 内容的所有铸造NFT
    mapping(address => uint256[]) public userMints;            // 用户铸造的所有NFT
    mapping(address => mapping(uint256 => bool)) public hasMinted; // 用户是否已铸造某内容
    
    // 统计数据
    uint256 public totalMinted;         // 总铸造数量
    uint256 public totalVolume;         // 总交易量
    
    // 常量
    uint256 public constant PLATFORM_FEE_PERCENTAGE = 1000; // 10% 平台费用
    uint256 public constant BASIS_POINTS = 10000;           // 基点
    
    // 事件
    event ContentMinted(
        uint256 indexed tokenId,
        uint256 indexed contentId,
        address indexed minter,
        address creator,
        uint256 mintPrice
    );
    
    event MintPriceUpdated(uint256 indexed contentId, uint256 newPrice);
    
    constructor(
        address _contentRegistry,
        address _treasuryAddress
    ) ERC721("PXPAT Content NFT", "PXPAT-CONTENT") {
        if (_contentRegistry == address(0) || _treasuryAddress == address(0)) {
            revert TokenErrors.ZeroAddress();
        }
        
        contentRegistry = IContentRegistry(_contentRegistry);
        treasuryAddress = _treasuryAddress;
    }
    
    /**
     * @dev 铸造内容NFT
     * @param contentId 内容ID
     * @return tokenId 铸造的NFT ID
     *
     * 注意：现在直接使用用户支付的金额进行收益分配，
     * 不再基于计算价格，这样更灵活且避免了价格计算错误
     */
    function mintContent(uint256 contentId) external payable whenNotPaused returns (uint256 tokenId) {
        // 获取内容信息
        IContentRegistry.ContentInfo memory content = contentRegistry.getContent(contentId);

        if (!content.isActive) {
            revert TokenErrors.ContentNotActive();
        }

        // 检查最低支付金额 (0.01 BNB)
        if (msg.value < 0.01 ether) {
            revert TokenErrors.InsufficientPayment();
        }
        
        // 检查是否已经铸造过 (可选限制)
        // if (hasMinted[msg.sender][contentId]) {
        //     revert TokenErrors.AlreadyMinted();
        // }
        
        // 创建NFT
        _tokenIdCounter.increment();
        tokenId = _tokenIdCounter.current();
        
        _safeMint(msg.sender, tokenId);
        
        // 设置NFT元数据URI (指向内容的IPFS)
        _setTokenURI(tokenId, content.metadataURI);
        
        // 记录铸造信息 - 使用实际支付金额
        mintInfos[tokenId] = MintInfo({
            tokenId: tokenId,
            contentId: contentId,
            minter: msg.sender,
            creator: content.creator,
            mintPrice: msg.value,  // 修复：记录实际支付金额
            timestamp: block.timestamp,
            contentType: content.contentType,
            title: content.title
        });
        
        // 更新映射
        contentMints[contentId].push(tokenId);
        userMints[msg.sender].push(tokenId);
        hasMinted[msg.sender][contentId] = true;
        
        // 🔒 安全优化：使用unchecked进行统计更新
        unchecked {
            totalMinted++;
            totalVolume += msg.value;  // 修复：使用实际支付的金额
        }

        // 分配收益 - 使用实际支付金额
        _distributeEarnings(content.creator, msg.value);

        // 注意：现在直接使用用户支付的全部金额，不退还
        
        emit ContentMinted(tokenId, contentId, msg.sender, content.creator, msg.value);
        
        return tokenId;
    }
    
    /**
     * @dev 批量铸造内容NFT
     */
    function batchMintContent(uint256[] memory contentIds) external payable whenNotPaused returns (uint256[] memory tokenIds) {
        uint256 length = contentIds.length;
        if (length == 0 || length > 10) { // 限制批量数量
            revert TokenErrors.InvalidArrayLength();
        }
        
        tokenIds = new uint256[](length);
        uint256 totalRequired = 0;
        
        // 🔒 安全优化：计算总费用
        for (uint256 i = 0; i < length;) {
            IContentRegistry.ContentInfo memory content = contentRegistry.getContent(contentIds[i]);
            unchecked {
                totalRequired += _calculateMintPrice(content.patFee, content.contentType);
                i++;
            }
        }
        
        if (msg.value < totalRequired) {
            revert TokenErrors.InsufficientPayment();
        }
        
        // 🔒 安全优化：执行批量铸造
        uint256 usedValue = 0;
        for (uint256 i = 0; i < length;) {
            // 临时设置msg.value为单个铸造所需金额
            uint256 singlePrice = _calculateMintPrice(
                contentRegistry.getContent(contentIds[i]).patFee,
                contentRegistry.getContent(contentIds[i]).contentType
            );

            // 这里需要重构mintContent以支持内部调用
            tokenIds[i] = _mintContentInternal(contentIds[i], singlePrice);
            unchecked {
                usedValue += singlePrice;
                i++;
            }
        }
        
        // 退还多余的ETH
        if (msg.value > usedValue) {
            payable(msg.sender).transfer(msg.value - usedValue);
        }
        
        return tokenIds;
    }
    
    /**
     * @dev 内部铸造函数
     */
    function _mintContentInternal(uint256 contentId, uint256 mintPrice) internal returns (uint256 tokenId) {
        IContentRegistry.ContentInfo memory content = contentRegistry.getContent(contentId);
        
        _tokenIdCounter.increment();
        tokenId = _tokenIdCounter.current();
        
        _safeMint(msg.sender, tokenId);
        _setTokenURI(tokenId, content.metadataURI);
        
        mintInfos[tokenId] = MintInfo({
            tokenId: tokenId,
            contentId: contentId,
            minter: msg.sender,
            creator: content.creator,
            mintPrice: mintPrice,
            timestamp: block.timestamp,
            contentType: content.contentType,
            title: content.title
        });
        
        contentMints[contentId].push(tokenId);
        userMints[msg.sender].push(tokenId);
        hasMinted[msg.sender][contentId] = true;
        
        unchecked {
            totalMinted++;
            totalVolume += mintPrice;
        }
        
        _distributeEarnings(content.creator, mintPrice);
        
        emit ContentMinted(tokenId, contentId, msg.sender, content.creator, mintPrice);
        
        return tokenId;
    }
    
    /**
     * @dev 计算铸造价格
     */
    function _calculateMintPrice(uint256 /* patFee */, string memory contentType) internal pure returns (uint256) {
        // 修复：使用固定的合理价格，而不是基于PAT费用的巨大数字
        // 基础价格设为0.1 BNB (100000000000000000 wei)
        uint256 basePrice = 0.1 ether;

        // 根据内容类型调整价格
        bytes32 typeHash = keccak256(bytes(contentType));

        if (typeHash == keccak256(bytes("video"))) {
            return basePrice * 10;  // 视频: 1.0 BNB
        } else if (typeHash == keccak256(bytes("article"))) {
            return basePrice * 2;   // 文章: 0.2 BNB
        } else if (typeHash == keccak256(bytes("image"))) {
            return basePrice * 3;   // 图片: 0.3 BNB
        } else if (typeHash == keccak256(bytes("audio"))) {
            return basePrice * 5;   // 音频: 0.5 BNB
        } else {
            return basePrice * 5;   // 其他类型: 0.5 BNB
        }
    }
    
    /**
     * @dev 分配铸造收益
     */
    function _distributeEarnings(address creator, uint256 mintPrice) internal {
        uint256 platformFee = (mintPrice * PLATFORM_FEE_PERCENTAGE) / BASIS_POINTS;
        uint256 creatorEarnings = mintPrice - platformFee;

        // 转账给创作者
        if (creatorEarnings > 0) {
            payable(creator).transfer(creatorEarnings);
        }

        // 转账给平台
        if (platformFee > 0) {
            payable(treasuryAddress).transfer(platformFee);
        }
    }

    // ============ 查询函数 ============

    /**
     * @dev 获取NFT铸造信息
     */
    function getMintInfo(uint256 tokenId) external view returns (MintInfo memory) {
        if (!_exists(tokenId)) {
            revert TokenErrors.InvalidContentId();
        }
        return mintInfos[tokenId];
    }

    /**
     * @dev 获取内容的所有铸造NFT
     */
    function getContentMints(uint256 contentId) external view returns (uint256[] memory) {
        return contentMints[contentId];
    }

    /**
     * @dev 获取用户铸造的所有NFT
     */
    function getUserMints(address user) external view returns (uint256[] memory) {
        return userMints[user];
    }

    /**
     * @dev 获取内容的铸造价格
     */
    function getMintPrice(uint256 contentId) external view returns (uint256) {
        IContentRegistry.ContentInfo memory content = contentRegistry.getContent(contentId);
        return _calculateMintPrice(content.patFee, content.contentType);
    }

    /**
     * @dev 检查用户是否已铸造某内容
     */
    function hasUserMinted(address user, uint256 contentId) external view returns (bool) {
        return hasMinted[user][contentId];
    }

    /**
     * @dev 获取铸造统计信息
     */
    function getMintStats() external view returns (
        uint256 _totalMinted,
        uint256 _totalVolume,
        uint256 _averagePrice
    ) {
        _totalMinted = totalMinted;
        _totalVolume = totalVolume;
        _averagePrice = totalMinted > 0 ? totalVolume / totalMinted : 0;
    }

    /**
     * @dev 获取内容铸造统计
     */
    function getContentMintStats(uint256 contentId) external view returns (
        uint256 mintCount,
        uint256 totalEarnings,
        uint256 averagePrice
    ) {
        uint256[] memory mints = contentMints[contentId];
        mintCount = mints.length;

        if (mintCount == 0) {
            return (0, 0, 0);
        }

        uint256 earnings = 0;
        for (uint256 i = 0; i < mintCount;) {
            unchecked {
                earnings += mintInfos[mints[i]].mintPrice;
                i++;
            }
        }

        totalEarnings = earnings;
        averagePrice = earnings / mintCount;
    }

    // ============ 管理函数 ============

    /**
     * @dev 设置国库地址
     */
    function setTreasuryAddress(address newTreasury) external onlyOwner {
        if (newTreasury == address(0)) {
            revert TokenErrors.ZeroAddress();
        }
        treasuryAddress = newTreasury;
    }

    /**
     * @dev 暂停合约
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev 恢复合约
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev 紧急提取ETH (仅管理员)
     */
    function emergencyWithdraw() external onlyOwner {
        uint256 balance = address(this).balance;
        if (balance > 0) {
            payable(owner()).transfer(balance);
        }
    }

    // ============ 重写函数 ============

    function _burn(uint256 tokenId) internal override(ERC721, ERC721URIStorage) {
        super._burn(tokenId);
    }

    function tokenURI(uint256 tokenId) public view override(ERC721, ERC721URIStorage) returns (string memory) {
        return super.tokenURI(tokenId);
    }

    function supportsInterface(bytes4 interfaceId) public view override(ERC721, ERC721URIStorage) returns (bool) {
        return super.supportsInterface(interfaceId);
    }
}
