// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/Counters.sol";

import "../interfaces/TokenErrors.sol";

/**
 * @title ContentCharacter
 * @dev 内容创作者身份NFT合约 - 学习xLog的Character概念
 * 每个内容创作者拥有一个Character NFT，代表其链上身份和声誉
 */
contract ContentCharacter is ERC721, ERC721URIStorage, Ownable, Pausable {
    using Counters for Counters.Counter;
    
    // 状态变量
    Counters.Counter private _characterIdCounter;    // Character ID计数器

    // 授权合约地址
    mapping(address => bool) public authorizedContracts;  // 授权的合约地址
    
    // Character信息结构体
    struct CharacterInfo {
        uint256 characterId;        // Character ID
        address owner;              // 拥有者地址
        string handle;              // 用户名/句柄
        string bio;                 // 个人简介
        string avatar;              // 头像IPFS哈希
        string website;             // 个人网站
        uint256 contentCount;       // 发布内容数量
        uint256 totalEarnings;      // 总收益
        uint256 followerCount;      // 粉丝数量
        uint256 createdAt;          // 创建时间
        bool isVerified;            // 是否认证
    }
    
    // 存储映射
    mapping(uint256 => CharacterInfo) public characters;           // Character信息
    mapping(address => uint256) public ownerToCharacter;          // 地址到Character的映射
    mapping(string => uint256) public handleToCharacter;         // 句柄到Character的映射
    mapping(string => bool) public handleExists;                 // 句柄是否存在
    
    // 关注关系映射
    mapping(uint256 => mapping(uint256 => bool)) public isFollowing;  // Character A 是否关注 Character B
    mapping(uint256 => uint256[]) public followers;                   // Character的粉丝列表
    mapping(uint256 => uint256[]) public following;                   // Character关注的列表
    
    // 统计数据
    uint256 public totalCharacters;     // 总Character数量
    uint256 public totalFollows;        // 总关注数量
    
    // 事件定义
    event CharacterCreated(
        uint256 indexed characterId,
        address indexed owner,
        string handle
    );
    
    event CharacterUpdated(
        uint256 indexed characterId,
        string bio,
        string avatar,
        string website
    );
    
    event CharacterFollowed(
        uint256 indexed follower,
        uint256 indexed following
    );
    
    event CharacterUnfollowed(
        uint256 indexed follower,
        uint256 indexed following
    );
    
    event CharacterVerified(uint256 indexed characterId);
    
    // 修饰符
    modifier onlyCharacterOwner(uint256 characterId) {
        if (characters[characterId].owner != msg.sender) {
            revert TokenErrors.Unauthorized();
        }
        _;
    }
    
    modifier validCharacterId(uint256 characterId) {
        if (characterId == 0 || characterId > _characterIdCounter.current()) {
            revert TokenErrors.InvalidContentId(); // 复用错误类型
        }
        _;
    }
    
    modifier handleNotExists(string memory handle) {
        if (handleExists[handle]) {
            revert TokenErrors.ContentAlreadyExists(); // 复用错误类型
        }
        _;
    }

    modifier onlyAuthorized() {
        if (!authorizedContracts[msg.sender] && msg.sender != owner()) {
            revert TokenErrors.Unauthorized();
        }
        _;
    }
    
    constructor() ERC721("PXPAT Character", "PXPAT-CHAR") {}
    
    /**
     * @dev 创建Character
     */
    function createCharacter(
        string memory handle,
        string memory bio,
        string memory avatar,
        string memory website
    ) external whenNotPaused handleNotExists(handle) returns (uint256 characterId) {
        // 检查用户是否已有Character
        if (ownerToCharacter[msg.sender] != 0) {
            revert TokenErrors.ContentAlreadyExists();
        }
        
        // 验证句柄格式
        if (bytes(handle).length == 0 || bytes(handle).length > 50) {
            revert TokenErrors.InvalidMetadata();
        }
        
        // 创建Character
        _characterIdCounter.increment();
        characterId = _characterIdCounter.current();
        
        // 铸造NFT
        _safeMint(msg.sender, characterId);
        
        // 设置Character信息
        characters[characterId] = CharacterInfo({
            characterId: characterId,
            owner: msg.sender,
            handle: handle,
            bio: bio,
            avatar: avatar,
            website: website,
            contentCount: 0,
            totalEarnings: 0,
            followerCount: 0,
            createdAt: block.timestamp,
            isVerified: false
        });
        
        // 更新映射
        ownerToCharacter[msg.sender] = characterId;
        handleToCharacter[handle] = characterId;
        handleExists[handle] = true;
        unchecked {
            totalCharacters++;
        }
        
        emit CharacterCreated(characterId, msg.sender, handle);
        
        return characterId;
    }
    
    /**
     * @dev 更新Character信息
     */
    function updateCharacter(
        uint256 characterId,
        string memory bio,
        string memory avatar,
        string memory website
    ) external validCharacterId(characterId) onlyCharacterOwner(characterId) whenNotPaused {
        CharacterInfo storage character = characters[characterId];
        
        character.bio = bio;
        character.avatar = avatar;
        character.website = website;
        
        emit CharacterUpdated(characterId, bio, avatar, website);
    }
    
    /**
     * @dev 关注Character
     */
    function followCharacter(uint256 targetCharacterId) external validCharacterId(targetCharacterId) whenNotPaused {
        uint256 followerCharacterId = ownerToCharacter[msg.sender];
        
        if (followerCharacterId == 0) {
            revert TokenErrors.Unauthorized();
        }
        
        if (followerCharacterId == targetCharacterId) {
            revert TokenErrors.InvalidMetadata(); // 不能关注自己
        }
        
        if (isFollowing[followerCharacterId][targetCharacterId]) {
            revert TokenErrors.ContentAlreadyExists(); // 已经关注了
        }
        
        // 建立关注关系
        isFollowing[followerCharacterId][targetCharacterId] = true;
        followers[targetCharacterId].push(followerCharacterId);
        following[followerCharacterId].push(targetCharacterId);
        
        // 🔒 安全优化：使用unchecked进行统计更新
        unchecked {
            characters[targetCharacterId].followerCount++;
            totalFollows++;
        }
        
        emit CharacterFollowed(followerCharacterId, targetCharacterId);
    }
    
    /**
     * @dev 取消关注Character
     */
    function unfollowCharacter(uint256 targetCharacterId) external validCharacterId(targetCharacterId) whenNotPaused {
        uint256 followerCharacterId = ownerToCharacter[msg.sender];
        
        if (followerCharacterId == 0) {
            revert TokenErrors.Unauthorized();
        }
        
        if (!isFollowing[followerCharacterId][targetCharacterId]) {
            revert TokenErrors.ContentNotFound(); // 没有关注
        }
        
        // 取消关注关系
        isFollowing[followerCharacterId][targetCharacterId] = false;
        
        // 从数组中移除（简化实现，实际可能需要优化）
        _removeFromArray(followers[targetCharacterId], followerCharacterId);
        _removeFromArray(following[followerCharacterId], targetCharacterId);
        
        // 🔒 安全优化：使用unchecked进行统计更新
        unchecked {
            characters[targetCharacterId].followerCount--;
            totalFollows--;
        }
        
        emit CharacterUnfollowed(followerCharacterId, targetCharacterId);
    }
    
    /**
     * @dev 认证Character (仅管理员)
     */
    function verifyCharacter(uint256 characterId) external validCharacterId(characterId) onlyOwner {
        characters[characterId].isVerified = true;
        emit CharacterVerified(characterId);
    }
    
    /**
     * @dev 增加内容计数 (由ContentRegistry调用)
     */
    function incrementContentCount(address creator) external onlyAuthorized {
        uint256 characterId = ownerToCharacter[creator];
        if (characterId != 0) {
            unchecked {
                characters[characterId].contentCount++;
            }
        }
    }
    
    /**
     * @dev 增加收益 (由ContentMint调用)
     */
    function addEarnings(address creator, uint256 amount) external onlyAuthorized {
        uint256 characterId = ownerToCharacter[creator];
        if (characterId != 0) {
            unchecked {
                characters[characterId].totalEarnings += amount;
            }
        }
    }
    
    // ============ 查询函数 ============
    
    /**
     * @dev 获取Character信息
     */
    function getCharacter(uint256 characterId) external view validCharacterId(characterId) returns (CharacterInfo memory) {
        return characters[characterId];
    }
    
    /**
     * @dev 根据地址获取Character
     */
    function getCharacterByOwner(address owner) external view returns (CharacterInfo memory) {
        uint256 characterId = ownerToCharacter[owner];
        if (characterId == 0) {
            revert TokenErrors.ContentNotFound();
        }
        return characters[characterId];
    }
    
    /**
     * @dev 根据句柄获取Character
     */
    function getCharacterByHandle(string memory handle) external view returns (CharacterInfo memory) {
        uint256 characterId = handleToCharacter[handle];
        if (characterId == 0) {
            revert TokenErrors.ContentNotFound();
        }
        return characters[characterId];
    }
    
    /**
     * @dev 获取粉丝列表
     */
    function getFollowers(uint256 characterId) external view validCharacterId(characterId) returns (uint256[] memory) {
        return followers[characterId];
    }
    
    /**
     * @dev 获取关注列表
     */
    function getFollowing(uint256 characterId) external view validCharacterId(characterId) returns (uint256[] memory) {
        return following[characterId];
    }
    
    // ============ 内部函数 ============
    
    /**
     * @dev 从数组中移除元素 - Gas优化版本
     */
    function _removeFromArray(uint256[] storage array, uint256 value) internal {
        uint256 length = array.length;
        for (uint256 i = 0; i < length;) {
            if (array[i] == value) {
                // 🔒 安全优化：使用最后一个元素替换要删除的元素
                array[i] = array[length - 1];
                array.pop();
                break;
            }
            unchecked {
                i++;
            }
        }
    }
    
    // ============ 管理函数 ============

    /**
     * @dev 设置授权合约 (仅管理员)
     */
    function setAuthorizedContract(address contractAddress, bool authorized) external onlyOwner {
        if (contractAddress == address(0)) {
            revert TokenErrors.ZeroAddress();
        }
        authorizedContracts[contractAddress] = authorized;
    }

    /**
     * @dev 暂停合约
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev 恢复合约
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    // ============ 重写函数 ============
    
    function _burn(uint256 tokenId) internal override(ERC721, ERC721URIStorage) {
        super._burn(tokenId);
    }
    
    function tokenURI(uint256 tokenId) public view override(ERC721, ERC721URIStorage) returns (string memory) {
        return super.tokenURI(tokenId);
    }
    
    function supportsInterface(bytes4 interfaceId) public view override(ERC721, ERC721URIStorage) returns (bool) {
        return super.supportsInterface(interfaceId);
    }
}
