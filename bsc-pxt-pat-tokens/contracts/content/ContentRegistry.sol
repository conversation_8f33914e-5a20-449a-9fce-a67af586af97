// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/utils/Counters.sol";

import "./interfaces/IContentRegistry.sol";
import "./libraries/ContentMetadata.sol";
import "../interfaces/TokenErrors.sol";

/**
 * @title ContentRegistry
 * @dev 内容注册合约 - 学习xLog的IPFS + 链上元数据方案，使用PAT代币消耗
 */
contract ContentRegistry is IContentRegistry, Ownable, Pausable, ReentrancyGuard {
    using SafeERC20 for IERC20;
    using Counters for Counters.Counter;
    using ContentMetadata for string;
    
    // 状态变量
    IERC20 public immutable patToken;           // PAT代币合约
    address public treasuryAddress;             // 国库地址
    
    Counters.Counter private _contentIdCounter; // 内容ID计数器
    Counters.Counter private _mintIdCounter;    // 铸造ID计数器
    
    // 存储映射
    mapping(uint256 => ContentInfo) public contents;                    // 内容信息
    mapping(address => uint256[]) public creatorContents;              // 创建者内容列表
    mapping(string => uint256) public contentTypeFees;                 // 内容类型费用
    mapping(bytes32 => bool) public contentHashes;                     // 内容哈希去重
    mapping(uint256 => mapping(address => bool)) public contentMinters; // 内容铸造者
    mapping(uint256 => uint256[]) public contentMints;                 // 内容铸造记录

    // 动态内容类型管理
    mapping(string => ContentMetadata.ContentTypeInfo) public contentTypes;  // 内容类型信息
    mapping(string => bool) public contentTypeExists;                        // 内容类型是否存在
    string[] public allContentTypes;                                         // 所有内容类型列表
    
    // 统计数据
    uint256 public totalPATConsumed;    // 总PAT消耗
    uint256 public totalMints;          // 总铸造次数
    
    // 常量
    uint256 public constant MINT_FEE_PERCENTAGE = 500;  // 5% 铸造费用给平台
    uint256 public constant BASIS_POINTS = 10000;       // 基点
    
    // 修饰符
    modifier onlyContentOwner(uint256 contentId) {
        if (contents[contentId].creator != msg.sender) {
            revert TokenErrors.Unauthorized();
        }
        _;
    }
    
    modifier validContentId(uint256 contentId) {
        if (contentId == 0 || contentId > _contentIdCounter.current()) {
            revert TokenErrors.InvalidContentId();
        }
        _;
    }
    
    modifier contentNotLocked(uint256 contentId) {
        if (contents[contentId].isLocked) {
            revert TokenErrors.ContentLocked();
        }
        _;
    }
    
    constructor(
        address _patToken,
        address _treasuryAddress
    ) {
        if (_patToken == address(0) || _treasuryAddress == address(0)) {
            revert TokenErrors.ZeroAddress();
        }
        
        patToken = IERC20(_patToken);
        treasuryAddress = _treasuryAddress;
        
        // 初始化内容类型费用
        _initializeContentTypeFees();
    }
    
    /**
     * @dev 注册内容到区块链
     */
    function registerContent(
        string memory contentType,
        string memory title,
        string memory ipfsHash,
        string memory metadataURI,
        string[] memory reviewers
    ) external override whenNotPaused nonReentrant returns (uint256 contentId) {
        // 验证输入参数
        if (!ContentMetadata.validateMetadata(title, contentType, ipfsHash)) {
            revert TokenErrors.InvalidMetadata();
        }

        // 验证内容类型是否存在且激活
        if (!contentTypeExists[contentType] || !contentTypes[contentType].isActive) {
            revert TokenErrors.InvalidContentType();
        }

        if (reviewers.length == 0) {
            revert TokenErrors.InvalidReviewers();
        }
        
        // 检查内容是否已存在
        bytes32 contentHash = ContentMetadata.generateContentHash(
            msg.sender, title, ipfsHash, block.timestamp
        );
        
        if (contentHashes[contentHash]) {
            revert TokenErrors.ContentAlreadyExists();
        }
        
        // 获取PAT费用
        uint256 patFee = getContentFee(contentType);
        
        // 收取PAT费用
        patToken.safeTransferFrom(msg.sender, treasuryAddress, patFee);
        unchecked {
            totalPATConsumed += patFee;
        }
        
        // 创建内容记录
        _contentIdCounter.increment();
        contentId = _contentIdCounter.current();
        
        contents[contentId] = ContentInfo({
            contentId: contentId,
            creator: msg.sender,
            contentType: contentType,
            title: title,
            ipfsHash: ipfsHash,
            metadataURI: metadataURI,
            patFee: patFee,
            timestamp: block.timestamp,
            isLocked: false,
            isActive: true,
            mintCount: 0,
            totalEarnings: 0
        });
        
        // 更新映射
        creatorContents[msg.sender].push(contentId);
        contentHashes[contentHash] = true;
        
        emit ContentRegistered(contentId, msg.sender, contentType, ipfsHash, patFee);
        
        return contentId;
    }
    
    /**
     * @dev 更新内容 (仅未锁定时可用)
     */
    function updateContent(
        uint256 contentId,
        string memory newIpfsHash,
        string memory newMetadataURI
    ) external override 
        validContentId(contentId) 
        onlyContentOwner(contentId) 
        contentNotLocked(contentId) 
        whenNotPaused 
    {
        if (!ContentMetadata.isValidIPFSHash(newIpfsHash)) {
            revert TokenErrors.InvalidIPFSHash();
        }
        
        contents[contentId].ipfsHash = newIpfsHash;
        contents[contentId].metadataURI = newMetadataURI;
        
        emit ContentUpdated(contentId, newIpfsHash, newMetadataURI);
    }
    
    /**
     * @dev 锁定内容 (锁定后不可修改，增加铸造可信度)
     */
    function lockContent(uint256 contentId) external override 
        validContentId(contentId) 
        onlyContentOwner(contentId) 
        contentNotLocked(contentId) 
    {
        contents[contentId].isLocked = true;
        emit ContentLocked(contentId);
    }
    
    /**
     * @dev 停用内容
     */
    function deactivateContent(uint256 contentId) external override 
        validContentId(contentId) 
        onlyContentOwner(contentId) 
    {
        contents[contentId].isActive = false;
        emit ContentDeactivated(contentId);
    }
    
    /**
     * @dev 铸造内容NFT (学习xLog的铸造机制)
     */
    function mintContent(uint256 contentId) external payable override
        validContentId(contentId)
        whenNotPaused
        nonReentrant
        returns (uint256 mintId)
    {
        ContentInfo storage content = contents[contentId];

        if (!content.isActive) {
            revert TokenErrors.ContentNotActive();
        }

        // 计算铸造费用 (基于内容类型的PAT费用)
        uint256 mintPrice = content.patFee / 10; // 铸造费用为注册费用的10%

        if (msg.value < mintPrice) {
            revert TokenErrors.InsufficientPayment();
        }

        // 创建铸造记录
        _mintIdCounter.increment();
        mintId = _mintIdCounter.current();

        // 🔒 安全优化：使用unchecked进行安全的统计更新
        unchecked {
            content.mintCount++;
            content.totalEarnings += mintPrice;
            totalMints++;
        }

        // 记录铸造者
        contentMinters[contentId][msg.sender] = true;
        contentMints[contentId].push(mintId);

        // 分配收益: 95%给创作者，5%给平台
        uint256 creatorEarnings = (mintPrice * (BASIS_POINTS - MINT_FEE_PERCENTAGE)) / BASIS_POINTS;
        uint256 platformFee = mintPrice - creatorEarnings;

        // 转账给创作者
        if (creatorEarnings > 0) {
            payable(content.creator).transfer(creatorEarnings);
        }

        // 转账给国库
        if (platformFee > 0) {
            payable(treasuryAddress).transfer(platformFee);
        }

        // 退还多余的ETH
        if (msg.value > mintPrice) {
            payable(msg.sender).transfer(msg.value - mintPrice);
        }

        emit ContentMinted(contentId, msg.sender, mintPrice, mintId);

        return mintId;
    }

    // ============ 查询函数 ============

    /**
     * @dev 获取内容信息
     */
    function getContent(uint256 contentId) external view override
        validContentId(contentId)
        returns (ContentInfo memory)
    {
        return contents[contentId];
    }

    /**
     * @dev 获取创建者的所有内容
     */
    function getContentsByCreator(address creator) external view override
        returns (uint256[] memory)
    {
        return creatorContents[creator];
    }

    /**
     * @dev 获取内容统计信息
     */
    function getContentStats() external view override returns (ContentStats memory) {
        uint256 activeCount = 0;
        uint256 totalCount = _contentIdCounter.current();

        // 🔒 安全优化：使用unchecked进行循环优化
        for (uint256 i = 1; i <= totalCount;) {
            if (contents[i].isActive) {
                unchecked {
                    activeCount++;
                }
            }
            unchecked {
                i++;
            }
        }

        return ContentStats({
            totalContents: totalCount,
            activeContents: activeCount,
            totalPATConsumed: totalPATConsumed,
            totalMints: totalMints
        });
    }

    /**
     * @dev 获取内容类型费用
     */
    function getContentFee(string memory contentType) public view override returns (uint256) {
        uint256 fee = contentTypeFees[contentType];
        return fee > 0 ? fee : ContentMetadata.getDefaultContentTypeFee(contentType);
    }

    /**
     * @dev 检查是否为内容拥有者
     */
    function isContentOwner(uint256 contentId, address user) external view override
        validContentId(contentId)
        returns (bool)
    {
        return contents[contentId].creator == user;
    }

    /**
     * @dev 获取内容总数
     */
    function getContentCount() external view override returns (uint256) {
        return _contentIdCounter.current();
    }

    // ============ 动态内容类型管理函数 ============

    /**
     * @dev 添加新的内容类型 (仅管理员)
     */
    function addContentType(
        string memory contentType,
        uint256 defaultFee,
        string memory description
    ) external override onlyOwner {
        if (!ContentMetadata.isValidContentTypeFormat(contentType)) {
            revert TokenErrors.InvalidContentType();
        }

        if (contentTypeExists[contentType]) {
            revert TokenErrors.ContentAlreadyExists();
        }

        // 创建内容类型信息
        contentTypes[contentType] = ContentMetadata.ContentTypeInfo({
            typeName: contentType,
            defaultFee: defaultFee,
            isActive: true,
            description: description,
            createdAt: block.timestamp
        });

        // 更新映射和数组
        contentTypeExists[contentType] = true;
        contentTypeFees[contentType] = defaultFee;
        allContentTypes.push(contentType);

        emit ContentTypeAdded(contentType, defaultFee, description);
    }

    /**
     * @dev 更新内容类型信息 (仅管理员)
     */
    function updateContentType(
        string memory contentType,
        uint256 newFee,
        string memory newDescription
    ) external override onlyOwner {
        if (!contentTypeExists[contentType]) {
            revert TokenErrors.InvalidContentType();
        }

        ContentMetadata.ContentTypeInfo storage typeInfo = contentTypes[contentType];
        typeInfo.defaultFee = newFee;
        typeInfo.description = newDescription;

        contentTypeFees[contentType] = newFee;

        emit ContentTypeUpdated(contentType, newFee, newDescription);
    }

    /**
     * @dev 停用内容类型 (仅管理员)
     */
    function deactivateContentType(string memory contentType) external override onlyOwner {
        if (!contentTypeExists[contentType]) {
            revert TokenErrors.InvalidContentType();
        }

        contentTypes[contentType].isActive = false;

        emit ContentTypeDeactivated(contentType);
    }

    /**
     * @dev 检查内容类型是否激活
     */
    function isContentTypeActive(string memory contentType) external view override returns (bool) {
        return contentTypeExists[contentType] && contentTypes[contentType].isActive;
    }

    /**
     * @dev 获取内容类型信息
     */
    function getContentTypeInfo(string memory contentType) external view override returns (
        string memory typeName,
        uint256 defaultFee,
        bool isActive,
        string memory description,
        uint256 createdAt
    ) {
        if (!contentTypeExists[contentType]) {
            revert TokenErrors.InvalidContentType();
        }

        ContentMetadata.ContentTypeInfo memory typeInfo = contentTypes[contentType];
        return (
            typeInfo.typeName,
            typeInfo.defaultFee,
            typeInfo.isActive,
            typeInfo.description,
            typeInfo.createdAt
        );
    }

    /**
     * @dev 获取所有内容类型
     */
    function getAllContentTypes() external view override returns (string[] memory) {
        return allContentTypes;
    }

    /**
     * @dev 获取激活的内容类型
     */
    function getActiveContentTypes() external view override returns (string[] memory) {
        uint256 activeCount = 0;

        // 🔒 安全优化：计算激活的内容类型数量
        for (uint256 i = 0; i < allContentTypes.length;) {
            if (contentTypes[allContentTypes[i]].isActive) {
                unchecked {
                    activeCount++;
                }
            }
            unchecked {
                i++;
            }
        }

        // 创建结果数组
        string[] memory activeTypes = new string[](activeCount);
        uint256 index = 0;

        for (uint256 i = 0; i < allContentTypes.length;) {
            if (contentTypes[allContentTypes[i]].isActive) {
                activeTypes[index] = allContentTypes[i];
                unchecked {
                    index++;
                }
            }
            unchecked {
                i++;
            }
        }

        return activeTypes;
    }

    // ============ 管理函数 ============

    /**
     * @dev 设置内容类型费用 (仅管理员)
     */
    function setContentTypeFee(string memory contentType, uint256 fee) external override onlyOwner {
        if (!contentTypeExists[contentType]) {
            revert TokenErrors.InvalidContentType();
        }

        contentTypeFees[contentType] = fee;
        contentTypes[contentType].defaultFee = fee;
        emit PATFeeUpdated(contentType, fee);
    }

    /**
     * @dev 设置国库地址 (仅管理员)
     */
    function setTreasuryAddress(address newTreasury) external override onlyOwner {
        if (newTreasury == address(0)) {
            revert TokenErrors.ZeroAddress();
        }

        treasuryAddress = newTreasury;
    }

    /**
     * @dev 暂停合约 (仅管理员)
     */
    function pause() external override onlyOwner {
        _pause();
    }

    /**
     * @dev 恢复合约 (仅管理员)
     */
    function unpause() external override onlyOwner {
        _unpause();
    }

    /**
     * @dev 初始化默认内容类型
     */
    function _initializeContentTypeFees() private {
        // 初始化默认内容类型
        string[8] memory defaultTypes = [
            "video", "novel", "short_drama", "anime",
            "manga", "music", "article", "short_video"
        ];

        for (uint256 i = 0; i < defaultTypes.length;) {
            string memory contentType = defaultTypes[i];
            uint256 defaultFee = ContentMetadata.getDefaultContentTypeFee(contentType);
            string memory description = ContentMetadata.getDefaultContentTypeDescription(contentType);

            // 创建内容类型信息
            contentTypes[contentType] = ContentMetadata.ContentTypeInfo({
                typeName: contentType,
                defaultFee: defaultFee,
                isActive: true,
                description: description,
                createdAt: block.timestamp
            });

            // 更新映射和数组
            contentTypeExists[contentType] = true;
            contentTypeFees[contentType] = defaultFee;
            allContentTypes.push(contentType);

            unchecked {
                i++;
            }
        }
    }
}
