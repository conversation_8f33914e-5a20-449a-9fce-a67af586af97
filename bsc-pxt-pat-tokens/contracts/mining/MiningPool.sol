// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

/**
 * @title MiningPool
 * @dev PXT代币挖矿池合约 - 通过质押PXT获得PAT奖励
 * @notice 这个合约实现了基于质押等级的挖矿奖励机制
 * <AUTHOR> Team
 *
 * 功能特性:
 * - 质押挖矿：质押PXT代币获得PAT奖励
 * - 等级加权：基于质押等级的差异化奖励权重
 * - 动态难度：根据总质押量自动调整挖矿难度
 * - 奖励递减：随时间递减的奖励发放机制
 * - 早期惩罚：提前取消质押的惩罚机制
 * - 统计分析：完整的挖矿数据统计和历史记录
 * - 紧急提取：暂停状态下的紧急资金提取
 */

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/IPAT.sol";
import "../staking/StakingPool.sol";
import "../interfaces/TokenErrors.sol";

contract MiningPool is Ownable, ReentrancyGuard, Pausable, AccessControl {
    using SafeERC20 for IERC20;

    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant OPERATOR_ROLE = keccak256("OPERATOR_ROLE");

    bool private _initialized;

    IPXT public pxtoken;

    constructor() {
        _setupRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _setupRole(ADMIN_ROLE, msg.sender);
        _setupRole(OPERATOR_ROLE, msg.sender);

        deployTime = block.timestamp;
        difficultyAdjustmentFactor = 100;
        difficultyAdjustmentPeriod = 7 days;
        lastDifficultyAdjustment = block.timestamp;
        dailyRewardResetTime = block.timestamp + 1 days;
    }

    IPAT public patoken;

    StakingPool public stakingPool;

    struct StakeInfo {
        uint256 amount;
        uint256 stakedAt;
        uint256 lastRewardTime;
        uint256 rewardDebt;
    }

    mapping(address => StakeInfo) public stakes;

    uint256 public totalStaked;
    uint256 public rewardPerSecond;
    uint256 public accRewardPerShare;
    uint256 public lastUpdateTime;

    uint256 public constant PRECISION = 1e12;
    uint256 public constant YEAR_IN_SECONDS = 365 * 24 * 3600;
    uint256 public constant REDUCTION_PERIOD = YEAR_IN_SECONDS;
    uint256 public constant INITIAL_RATE = 10;
    uint256 public constant THIRD_YEAR_RATE = 20;
    uint256 public deployTime;

    uint256 public minStakeTime = 7 days;
    uint256 public earlyUnstakePenalty = 1000;

    mapping(StakingPool.StakingLevel => uint256) public levelMiningWeights;

    uint256 public difficultyAdjustmentFactor;
    uint256 public targetStakedAmount;
    uint256 public lastDifficultyAdjustment;
    uint256 public difficultyAdjustmentPeriod;

    uint256 public maxDailyReward;
    uint256 public dailyRewardCounter;
    uint256 public dailyRewardResetTime;

    struct MiningStatistics {
        uint256 totalStakersCount;
        uint256 totalRewardsDistributed;
        uint256 totalPenalties;
        uint256 currentHashrate;
        uint256 averageAPY;
        uint256 dailyDistribution;
        uint256 weeklyDistribution;
        uint256 avgStakeDuration;
    }

    MiningStatistics public miningStats;

    address[] public activeStakers;

    mapping(address => bool) public isActiveStaker;

    struct RewardHistory {
        uint256 timestamp;
        uint256 dailyReward;
        uint256 activeDifficulty;
        uint256 activeStakers;
        uint256 totalStaked;
    }

    mapping(uint256 => RewardHistory) public dailyRewardHistory;

    event Staked(address indexed user, uint256 amount);
    event Unstaked(address indexed user, uint256 amount, uint256 penalty);
    event RewardClaimed(address indexed user, uint256 amount);
    event PoolUpdated(uint256 rewardPerSecond, uint256 accRewardPerShare);
    event EmergencyWithdraw(address indexed user, uint256 amount);
    event DifficultyAdjusted(uint256 oldFactor, uint256 newFactor);
    event LevelWeightUpdated(StakingPool.StakingLevel level, uint256 weight);
    event SecurityParamUpdated(string param, uint256 value);
    event MiningStatsUpdated(uint256 totalStakersCount, uint256 totalRewards, uint256 currentHashrate, uint256 averageAPY);
    event Initialized(address indexed pxtoken, address indexed patoken, address indexed stakingPool);
    event TokenRecovered(address indexed token, uint256 amount);

    function initialize(
        address _pxtoken,
        address _patoken,
        address _stakingPool,
        uint256 _rewardPerSecond,
        uint256 _maxDailyReward,
        uint256 _targetStakedAmount
    ) external onlyRole(ADMIN_ROLE) {
        if (_initialized) revert TokenErrors.AlreadyInitialized();
        if (_pxtoken == address(0)) revert TokenErrors.InvalidAddress("PXT Token");
        if (_patoken == address(0)) revert TokenErrors.InvalidAddress("PAT Token");
        if (_stakingPool == address(0)) revert TokenErrors.InvalidAddress("Staking Pool");
        if (_rewardPerSecond == 0) revert TokenErrors.ZeroAmount();
        if (_maxDailyReward == 0) revert TokenErrors.ZeroAmount();
        if (_targetStakedAmount == 0) revert TokenErrors.ZeroAmount();

        pxtoken = IPXT(_pxtoken);
        patoken = IPAT(_patoken);
        stakingPool = StakingPool(_stakingPool);
        rewardPerSecond = _rewardPerSecond;
        maxDailyReward = _maxDailyReward;
        targetStakedAmount = _targetStakedAmount;

        _setDefaultLevelWeights();

        _initialized = true;

        emit Initialized(_pxtoken, _patoken, _stakingPool);
    }

    function _setDefaultLevelWeights() internal {
        levelMiningWeights[StakingPool.StakingLevel.DingJi] = 130;
        levelMiningWeights[StakingPool.StakingLevel.ChengJi] = 140;
        levelMiningWeights[StakingPool.StakingLevel.YiJi] = 160;
        levelMiningWeights[StakingPool.StakingLevel.JiaJi] = 200;
        levelMiningWeights[StakingPool.StakingLevel.ShiJue] = 250;
        levelMiningWeights[StakingPool.StakingLevel.ShuangShiJue] = 300;
        levelMiningWeights[StakingPool.StakingLevel.ZhiZun] = 500;
    }

    function updatePool() public {
        if (block.timestamp <= lastUpdateTime) {
            return;
        }

        if (totalStaked == 0) {
            lastUpdateTime = block.timestamp;
            return;
        }

        if (block.timestamp >= lastDifficultyAdjustment + difficultyAdjustmentPeriod) {
            _adjustDifficulty();
        }

        if (block.timestamp >= dailyRewardResetTime) {
            _recordDailyStats();

            dailyRewardCounter = 0;
            dailyRewardResetTime = block.timestamp + 1 days;
        }

        uint256 timeElapsed = block.timestamp - lastUpdateTime;

        uint256 currentRewardRate = getCurrentRewardRate();

        // 🔒 安全优化：防止溢出的奖励计算
        uint256 reward;
        unchecked {
            reward = timeElapsed * currentRewardRate;
            accRewardPerShare += (reward * PRECISION) / totalStaked;
        }

        lastUpdateTime = block.timestamp;

        _updateMiningStats();

        emit PoolUpdated(currentRewardRate, accRewardPerShare);
    }

    function _recordDailyStats() internal {
        uint256 today = getDateAsUint(block.timestamp);

        dailyRewardHistory[today] = RewardHistory({
            timestamp: block.timestamp,
            dailyReward: dailyRewardCounter,
            activeDifficulty: difficultyAdjustmentFactor,
            activeStakers: activeStakers.length,
            totalStaked: totalStaked
        });

        miningStats.dailyDistribution = dailyRewardCounter;

        uint256 weeklyTotal = 0;
        // 🔒 安全优化：周奖励统计循环优化
        for (uint256 i = 0; i < 7;) {
            uint256 pastDate = getDateAsUint(block.timestamp - (i * 1 days));
            unchecked {
                weeklyTotal += dailyRewardHistory[pastDate].dailyReward;
                i++;
            }
        }
        miningStats.weeklyDistribution = weeklyTotal;
    }

    function getDateAsUint(uint256 timestamp) public pure returns (uint256) {
        uint256 SECONDS_PER_DAY = 24 * 60 * 60;
        uint256 daysSince1970 = timestamp / SECONDS_PER_DAY;

        uint256 year = 1970;
        uint256 month = 1;
        uint256 day = 1;

        while (daysSince1970 >= _getDaysInYear(year)) {
            daysSince1970 -= _getDaysInYear(year);
            year++;
        }

        uint8[12] memory daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

        if (_isLeapYear(year)) {
            daysInMonth[1] = 29;
        }

        while (daysSince1970 >= daysInMonth[month - 1]) {
            daysSince1970 -= daysInMonth[month - 1];
            month++;
        }

        day += daysSince1970;

        return year * 10000 + month * 100 + day;
    }

    function _isLeapYear(uint256 year) internal pure returns (bool) {
        return ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0);
    }

    function _getDaysInYear(uint256 year) internal pure returns (uint256) {
        return _isLeapYear(year) ? 366 : 365;
    }
    
    function _updateMiningStats() internal {
        miningStats.totalStakersCount = activeStakers.length;

        // 🔒 安全优化：防止溢出的挖矿统计计算
        unchecked {
            miningStats.currentHashrate = (totalStaked * 100) / difficultyAdjustmentFactor;
        }

        if (totalStaked > 0) {
            uint256 dailyRewards;
            uint256 annualRewards;
            unchecked {
                dailyRewards = getCurrentRewardRate() * 24 * 3600;
                annualRewards = dailyRewards * 365;
                miningStats.averageAPY = (annualRewards * 10000) / totalStaked;
            }
        }

        if (activeStakers.length > 0) {
            uint256 totalStakeDuration = 0;
            // 🔒 安全优化：质押时长统计循环优化
            for (uint256 i = 0; i < activeStakers.length;) {
                address staker = activeStakers[i];
                if (stakes[staker].amount > 0) {
                    unchecked {
                        totalStakeDuration += (block.timestamp - stakes[staker].stakedAt);
                    }
                }
                unchecked { i++; }
            }
            unchecked {
                miningStats.avgStakeDuration = totalStakeDuration / activeStakers.length;
            }
        }

        emit MiningStatsUpdated(
            miningStats.totalStakersCount,
            miningStats.totalRewardsDistributed,
            miningStats.currentHashrate,
            miningStats.averageAPY
        );
    }

    function getCurrentRewardRate() public view returns (uint256) {
        // 🔒 安全优化：防止溢出的年份计算
        uint256 yearsElapsed;
        unchecked {
            yearsElapsed = (block.timestamp - deployTime) / REDUCTION_PERIOD;
        }

        uint256 currentRate = rewardPerSecond;

        // 🔒 安全优化：奖励率递减计算循环优化
        for (uint256 i = 0; i < yearsElapsed && i < 2;) {
            unchecked {
                currentRate = (currentRate * (100 - INITIAL_RATE)) / 100;
                i++;
            }
        }

        if (yearsElapsed >= 2) {
            for (uint256 i = 0; i < yearsElapsed - 2;) {
                unchecked {
                    currentRate = (currentRate * (100 - THIRD_YEAR_RATE)) / 100;
                    i++;
                }
            }
        }

        unchecked {
            currentRate = (currentRate * 100) / difficultyAdjustmentFactor;
        }

        return currentRate;
    }
    
    function _adjustDifficulty() internal {
        uint256 oldFactor = difficultyAdjustmentFactor;

        if (totalStaked > 0 && targetStakedAmount > 0) {
            // 🔒 安全优化：防止溢出的难度调整计算
            uint256 stakeRatio;
            unchecked {
                stakeRatio = (totalStaked * 100) / targetStakedAmount;
            }

            if (stakeRatio > 120) {
                unchecked {
                    difficultyAdjustmentFactor = (difficultyAdjustmentFactor * 110) / 100;
                }
            } else if (stakeRatio < 80) {
                unchecked {
                    difficultyAdjustmentFactor = (difficultyAdjustmentFactor * 90) / 100;
                }
            }

            if (difficultyAdjustmentFactor < 50) {
                difficultyAdjustmentFactor = 50;
            } else if (difficultyAdjustmentFactor > 300) {
                difficultyAdjustmentFactor = 300;
            }
        }

        lastDifficultyAdjustment = block.timestamp;

        if (oldFactor != difficultyAdjustmentFactor) {
            emit DifficultyAdjusted(oldFactor, difficultyAdjustmentFactor);
        }
    }

    function stake(uint256 _amount) external nonReentrant whenNotPaused {
        if (_amount == 0) revert TokenErrors.ZeroAmount();

        updatePool();

        if (stakes[_msgSender()].amount > 0) {
            uint256 pending = _calculateReward(_msgSender());

            if (pending > 0) {
                _distributeReward(_msgSender(), pending);
            }
        } else {
            if (!isActiveStaker[_msgSender()]) {
                activeStakers.push(_msgSender());
                isActiveStaker[_msgSender()] = true;
            }
        }

        pxtoken.transferFrom(_msgSender(), address(this), _amount);

        // 🔒 安全优化：使用unchecked进行质押数据更新
        unchecked {
            stakes[_msgSender()].amount += _amount;
            totalStaked += _amount;
        }
        stakes[_msgSender()].stakedAt = block.timestamp;
        stakes[_msgSender()].lastRewardTime = block.timestamp;
        unchecked {
            stakes[_msgSender()].rewardDebt = (stakes[_msgSender()].amount * accRewardPerShare) / PRECISION;
        }

        emit Staked(_msgSender(), _amount);
    }
    
    function unstake(uint256 _amount) external nonReentrant {
        if (stakes[_msgSender()].amount < _amount) revert TokenErrors.InsufficientStakingAmount();
        if (_amount == 0) revert TokenErrors.ZeroAmount();

        updatePool();

        uint256 pending = _calculateReward(_msgSender());

        // 🔒 安全优化：使用unchecked进行取消质押计算
        unchecked {
            stakes[_msgSender()].amount -= _amount;
            stakes[_msgSender()].rewardDebt = (stakes[_msgSender()].amount * accRewardPerShare) / PRECISION;
        }

        uint256 penalty = 0;
        if (block.timestamp < stakes[_msgSender()].stakedAt + minStakeTime) {
            unchecked {
                penalty = (_amount * earlyUnstakePenalty) / 10000;
            }

            pxtoken.burn(penalty);

            pxtoken.transfer(_msgSender(), _amount - penalty);

            unchecked {
                miningStats.totalPenalties += penalty;
            }
        } else {
            pxtoken.transfer(_msgSender(), _amount);
        }

        unchecked {
            totalStaked -= _amount;
        }

        if (stakes[_msgSender()].amount == 0) {
            _removeFromActiveStakers(_msgSender());
        }

        if (pending > 0) {
            _distributeReward(_msgSender(), pending);
        }

        emit Unstaked(_msgSender(), _amount, penalty);
    }

    function _removeFromActiveStakers(address _user) internal {
        if (!isActiveStaker[_user]) {
            return;
        }

        // 🔒 安全优化：移除活跃质押者循环优化
        for (uint256 i = 0; i < activeStakers.length;) {
            if (activeStakers[i] == _user) {
                activeStakers[i] = activeStakers[activeStakers.length - 1];
                activeStakers.pop();
                isActiveStaker[_user] = false;
                break;
            }
            unchecked { i++; }
        }
    }
    
    function _calculateReward(address _user) internal view returns (uint256) {
        StakeInfo storage userStake = stakes[_user];

        (, , StakingPool.StakingLevel userLevel, , , , , , ) = stakingPool.getUserStakingInfo(_user);

        uint256 levelWeight = levelMiningWeights[userLevel];
        if (levelWeight == 0) {
            levelWeight = 100;
        }

        uint256 baseReward = userStake.amount *
            (accRewardPerShare - userStake.rewardDebt)
        / PRECISION;

        uint256 totalReward = baseReward * levelWeight / 100;

        return totalReward;
    }

    function _distributeReward(address _user, uint256 _amount) internal {
        uint256 remainingDailyAllowance = maxDailyReward > dailyRewardCounter ?
                                         maxDailyReward - dailyRewardCounter : 0;

        uint256 rewardToMint = _amount;
        if (rewardToMint > remainingDailyAllowance) {
            rewardToMint = remainingDailyAllowance;
        }

        if (rewardToMint > 0) {
            dailyRewardCounter = dailyRewardCounter + rewardToMint;

            miningStats.totalRewardsDistributed = miningStats.totalRewardsDistributed + rewardToMint;

            patoken.mint(_user, rewardToMint);

            emit RewardClaimed(_user, rewardToMint);
        }
    }

    function emergencyWithdraw() external nonReentrant whenPaused {
        uint256 amount = stakes[_msgSender()].amount;
        if (amount == 0) revert TokenErrors.NoRewardToClaim();

        stakes[_msgSender()].amount = 0;
        stakes[_msgSender()].rewardDebt = 0;

        totalStaked = totalStaked - amount;

        pxtoken.transfer(_msgSender(), amount);

        emit EmergencyWithdraw(_msgSender(), amount);
    }

    function pendingReward(address _user) external view returns (uint256) {
        if (totalStaked == 0 || stakes[_user].amount == 0) {
            return 0;
        }

        (, , StakingPool.StakingLevel userLevel, , , , , , ) = stakingPool.getUserStakingInfo(_user);

        uint256 levelWeight = levelMiningWeights[userLevel];
        if (levelWeight == 0) {
            levelWeight = 100;
        }

        uint256 newAccRewardPerShare = accRewardPerShare;

        if (block.timestamp > lastUpdateTime && totalStaked > 0) {
            uint256 timeElapsed = block.timestamp - lastUpdateTime;
            uint256 currentRewardRate = getCurrentRewardRate();
            uint256 reward = timeElapsed * currentRewardRate;
            newAccRewardPerShare = newAccRewardPerShare +
                reward * PRECISION / totalStaked;
        }

        uint256 baseReward = stakes[_user].amount *
            (newAccRewardPerShare - stakes[_user].rewardDebt)
        / PRECISION;

        uint256 totalReward = baseReward * levelWeight / 100;

        return totalReward;
    }

    function setLevelMiningWeight(StakingPool.StakingLevel _level, uint256 _weight) external onlyRole(ADMIN_ROLE) {
        if (_weight == 0) revert TokenErrors.ZeroAmount();
        levelMiningWeights[_level] = _weight;
        emit LevelWeightUpdated(_level, _weight);
    }

    function setRewardPerSecond(uint256 _rewardPerSecond) external onlyRole(ADMIN_ROLE) {
        updatePool();

        rewardPerSecond = _rewardPerSecond;

        emit PoolUpdated(rewardPerSecond, accRewardPerShare);
    }

    function setTargetStakedAmount(uint256 _targetStakedAmount) external onlyRole(ADMIN_ROLE) {
        if (_targetStakedAmount == 0) revert TokenErrors.ZeroAmount();
        targetStakedAmount = _targetStakedAmount;
        emit SecurityParamUpdated("targetStakedAmount", _targetStakedAmount);
    }

    function setDifficultyAdjustmentPeriod(uint256 _adjustmentPeriod) external onlyRole(ADMIN_ROLE) {
        if (_adjustmentPeriod == 0) revert TokenErrors.InvalidOperation();
        difficultyAdjustmentPeriod = _adjustmentPeriod;
        emit SecurityParamUpdated("difficultyAdjustmentPeriod", _adjustmentPeriod);
    }

    function setMaxDailyReward(uint256 _maxDailyReward) external onlyRole(ADMIN_ROLE) {
        maxDailyReward = _maxDailyReward;
        emit SecurityParamUpdated("maxDailyReward", _maxDailyReward);
    }

    function forceDifficultyAdjustment() external onlyRole(ADMIN_ROLE) {
        updatePool();
        _adjustDifficulty();
    }

    function setPaused(bool _paused) external onlyRole(ADMIN_ROLE) {
        if (_paused) {
            _pause();
        } else {
            _unpause();
        }
    }
    
    function getMiningStatus() external view returns (
        bool isPaused,
        uint256 difficulty,
        uint256 currentRewardRate,
        uint256 totalStakedAmount,
        uint256 activeStakersCount,
        uint256 dailyRewards,
        uint256 averageAPY
    ) {
        return (
            paused(),
            difficultyAdjustmentFactor,
            getCurrentRewardRate(),
            totalStaked,
            activeStakers.length,
            dailyRewardCounter,
            miningStats.averageAPY
        );
    }

    function getUserMiningData(address _user) external view returns (
        uint256 stakedAmount,
        uint256 stakeTime,
        uint256 pendingRewards,
        StakingPool.StakingLevel stakingLevel,
        uint256 userWeight,
        uint256 estimatedDailyReward
    ) {
        StakeInfo memory userStake = stakes[_user];

        (, , StakingPool.StakingLevel userLevel, , , , , , ) = stakingPool.getUserStakingInfo(_user);

        uint256 levelWeight = levelMiningWeights[userLevel];
        if (levelWeight == 0) {
            levelWeight = 100;
        }

        uint256 pending = this.pendingReward(_user);

        uint256 dailyReward = 0;
        if (userStake.amount > 0 && totalStaked > 0) {
            uint256 rewardRate = getCurrentRewardRate();
            uint256 dailyRewards = rewardRate * 24 * 3600;
            uint256 userShare = userStake.amount * PRECISION / totalStaked;
            dailyReward = dailyRewards * userShare / PRECISION * levelWeight / 100;
        }

        return (
            userStake.amount,
            userStake.stakedAt,
            pending,
            userLevel,
            levelWeight,
            dailyReward
        );
    }
    
    function getMiningStatistics() external view returns (
        uint256 totalStakersCount,
        uint256 totalRewardsDistributed,
        uint256 totalPenalties,
        uint256 currentHashrate,
        uint256 averageAPY,
        uint256 dailyDistribution,
        uint256 weeklyDistribution,
        uint256 avgStakeDuration
    ) {
        return (
            miningStats.totalStakersCount,
            miningStats.totalRewardsDistributed,
            miningStats.totalPenalties,
            miningStats.currentHashrate,
            miningStats.averageAPY,
            miningStats.dailyDistribution,
            miningStats.weeklyDistribution,
            miningStats.avgStakeDuration
        );
    }

    function getDailyRewardHistory(uint256 _daysAgo) external view returns (
        uint256 date,
        uint256 reward,
        uint256 difficulty,
        uint256 stakers,
        uint256 stakedAmount
    ) {
        uint256 targetDate = getDateAsUint(block.timestamp - (_daysAgo * 1 days));
        RewardHistory memory history = dailyRewardHistory[targetDate];

        return (
            targetDate,
            history.dailyReward,
            history.activeDifficulty,
            history.activeStakers,
            history.totalStaked
        );
    }

    function claimReward() external nonReentrant {
        if (stakes[_msgSender()].amount == 0) revert TokenErrors.NoRewardToClaim();

        updatePool();

        uint256 pending = _calculateReward(_msgSender());
        
        if (pending == 0) revert TokenErrors.NoRewardToClaim();

        stakes[_msgSender()].rewardDebt = stakes[_msgSender()].amount * accRewardPerShare / PRECISION;
        stakes[_msgSender()].lastRewardTime = block.timestamp;

        _distributeReward(_msgSender(), pending);
    }

    function setMinStakeTime(uint256 _minStakeTime) external onlyRole(ADMIN_ROLE) {
        minStakeTime = _minStakeTime;
        emit SecurityParamUpdated("minStakeTime", _minStakeTime);
    }

    function setEarlyUnstakePenalty(uint256 _penalty) external onlyRole(ADMIN_ROLE) {
        earlyUnstakePenalty = _penalty;
        emit SecurityParamUpdated("earlyUnstakePenalty", _penalty);
    }

    function getActiveStakersPaged(uint256 offset, uint256 limit) external view returns (address[] memory) {
        uint256 total = activeStakers.length;
        if (offset >= total) {
            return new address[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        address[] memory result = new address[](end - offset);
        // 🔒 安全优化：分页获取活跃质押者循环优化
        for (uint256 i = offset; i < end;) {
            result[i - offset] = activeStakers[i];
            unchecked { i++; }
        }
        return result;
    }

    // 🔒 安全优化：添加实用的查询函数

    /**
     * @dev 获取挖矿池的基本信息
     */
    function getPoolInfo() external view returns (
        uint256 _totalStaked,
        uint256 _rewardPerSecond,
        uint256 _accRewardPerShare,
        uint256 _activeStakersCount,
        uint256 _difficultyFactor,
        uint256 _currentRewardRate
    ) {
        return (
            totalStaked,
            rewardPerSecond,
            accRewardPerShare,
            activeStakers.length,
            difficultyAdjustmentFactor,
            getCurrentRewardRate()
        );
    }

    /**
     * @dev 检查用户是否为活跃质押者
     */
    function isUserActiveStaker(address _user) external view returns (bool) {
        return isActiveStaker[_user] && stakes[_user].amount > 0;
    }

    /**
     * @dev 获取用户质押的基本信息
     */
    function getUserStakeInfo(address _user) external view returns (
        uint256 amount,
        uint256 stakedAt,
        uint256 lastRewardTime,
        uint256 rewardDebt,
        bool canUnstakeWithoutPenalty
    ) {
        StakeInfo memory userStake = stakes[_user];
        bool noPenalty = block.timestamp >= userStake.stakedAt + minStakeTime;

        return (
            userStake.amount,
            userStake.stakedAt,
            userStake.lastRewardTime,
            userStake.rewardDebt,
            noPenalty
        );
    }
}