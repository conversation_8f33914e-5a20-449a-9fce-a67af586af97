// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

/**
 * @title MiningRewards
 * @dev 挖矿奖励管理合约 - 管理多种奖励机制和锁定策略
 * @notice 这个合约负责分发挖矿奖励并管理各种奖励加成
 * <AUTHOR> Team
 *
 * 功能特性:
 * - 奖励分发：PAT代币的挖矿奖励分发
 * - 多重加成：活跃度、质押等级、早期采用者、连续奖励
 * - 锁定机制：部分奖励锁定以维护代币经济稳定
 * - 批量操作：支持批量设置用户奖励倍数
 * - 历史记录：完整的奖励分发历史追踪
 * - 统计分析：用户和全局的挖矿统计数据
 * - 权限控制：授权池和管理员权限管理
 */

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

contract MiningRewards is Ownable, ReentrancyGuard, Pausable {
    IPAT public patoken;

    uint256 public deployTime;

    mapping(address => bool) public authorizedPools;

    struct UserBonus {
        uint256 activityMultiplier;
        uint256 stakeLevelMultiplier;
        uint256 lastUpdateTime;
    }

    mapping(address => UserBonus) public userBonuses;

    struct MiningStats {
        uint256 totalRewards;
        uint256 lockedRewards;
        uint256 immediateRewards;
        uint256 activityBonusTotal;
        uint256 stakeLevelBonusTotal;
        uint256 earlyAdopterBonusTotal;
        uint256 consecutiveBonusTotal;
        uint256 lastRewardTime;
        uint256 rewardCount;
    }

    mapping(address => MiningStats) public userMiningStats;

    MiningStats public globalMiningStats;

    uint256 public constant MAX_ACTIVITY_MULTIPLIER = 300;

    uint256 public constant MAX_STAKE_LEVEL_MULTIPLIER = 200;

    uint256 public lockPercentage;
    uint256 public lockDuration;

    uint256 public constant CONSECUTIVE_DAYS = 30;
    uint256 public consecutiveBonusPercentage;

    struct RewardRecord {
        uint256 timestamp;
        uint256 totalReward;
        uint256 lockedAmount;
        uint256 activityBonus;
        uint256 stakeLevelBonus;
        uint256 earlyAdopterBonus;
        uint256 consecutiveBonus;
    }
    mapping(address => RewardRecord[]) public userRewardHistory;

    event RewardDistributed(address indexed user, uint256 amount, uint256 lockedAmount, uint256 activityBonus, uint256 stakeLevelBonus, uint256 earlyAdopterBonus, uint256 consecutiveBonus, uint256 lockPercentage);
    event BonusUpdated(address indexed user, uint256 activityMultiplier, uint256 stakeLevelMultiplier);
    event PoolAuthorized(address indexed pool, bool status);
    event LockParamsUpdated(uint256 percentage, uint256 duration);
    event ConsecutiveBonusUpdated(uint256 percentage);
    event Initialized(address indexed patoken);
    event TokenRecovered(address indexed token, uint256 amount);

    modifier onlyAuthorizedPool() {
        if (!authorizedPools[_msgSender()]) revert TokenErrors.Unauthorized();
        _;
    }

    constructor() {
        deployTime = block.timestamp;
    }

    function initialize(
        address _patoken,
        uint256 _lockPercentage,
        uint256 _lockDuration,
        uint256 _consecutiveBonusPercentage
    ) external onlyOwner {
        if (address(patoken) != address(0)) revert TokenErrors.AlreadyInitialized();
        if (_patoken == address(0)) revert TokenErrors.ZeroAddress();
        if (_lockPercentage > 100) revert TokenErrors.InvalidPercentage();
        if (_consecutiveBonusPercentage > 100) revert TokenErrors.InvalidPercentage();

        patoken = IPAT(_patoken);
        lockPercentage = _lockPercentage;
        lockDuration = _lockDuration;
        consecutiveBonusPercentage = _consecutiveBonusPercentage;

        emit Initialized(_patoken);
        emit LockParamsUpdated(_lockPercentage, _lockDuration);
        emit ConsecutiveBonusUpdated(_consecutiveBonusPercentage);
    }

    function distributeReward(
        address _user,
        uint256 _baseAmount,
        bool _isCreator,
        bool _isConsecutive
    ) external nonReentrant onlyAuthorizedPool whenNotPaused returns (uint256) {
        if (_user == address(0)) revert TokenErrors.ZeroAddress();
        if (_baseAmount == 0) revert TokenErrors.ZeroAmount();
        if (address(patoken) == address(0)) revert TokenErrors.InvalidAddress("PAT Token");

        UserBonus storage userBonus = userBonuses[_user];

        uint256 activityMultiplier = userBonus.activityMultiplier > 0 ? userBonus.activityMultiplier : 100;
        uint256 stakeLevelMultiplier = userBonus.stakeLevelMultiplier > 0 ? userBonus.stakeLevelMultiplier : 100;

        // 🔒 安全优化：防止溢出的奖励计算
        uint256 activityBonus;
        uint256 stakeLevelBonus;
        unchecked {
            activityBonus = (_baseAmount * (activityMultiplier - 100)) / 100;
            stakeLevelBonus = (_baseAmount * (stakeLevelMultiplier - 100)) / 100;
        }

        uint256 earlyAdopterBonus = 0;
        if (block.timestamp < deployTime + 180 days) {
            unchecked {
                earlyAdopterBonus = (_baseAmount * 200) / 100;
            }
        }

        uint256 consecutiveBonus = 0;
        if (_isCreator && _isConsecutive) {
            unchecked {
                consecutiveBonus = (_baseAmount * consecutiveBonusPercentage) / 100;
            }
        }

        uint256 totalReward;
        uint256 lockedAmount;
        uint256 immediateAmount;
        unchecked {
            totalReward = _baseAmount + activityBonus + stakeLevelBonus + earlyAdopterBonus + consecutiveBonus;
            lockedAmount = (totalReward * lockPercentage) / 100;
            immediateAmount = totalReward - lockedAmount;
        }

        if (immediateAmount > 0) {
            patoken.mint(_user, immediateAmount);
        }

        if (lockedAmount > 0) {
            patoken.mint(address(this), lockedAmount);
            patoken.lock(_user, lockedAmount, block.timestamp + lockDuration);
        }

        // 🔒 安全优化：使用unchecked进行用户统计更新
        MiningStats storage stats = userMiningStats[_user];
        unchecked {
            stats.totalRewards += totalReward;
            stats.lockedRewards += lockedAmount;
            stats.immediateRewards += immediateAmount;
            stats.activityBonusTotal += activityBonus;
            stats.stakeLevelBonusTotal += stakeLevelBonus;
            stats.earlyAdopterBonusTotal += earlyAdopterBonus;
            stats.consecutiveBonusTotal += consecutiveBonus;
            stats.rewardCount++;
        }
        stats.lastRewardTime = block.timestamp;

        // 🔒 安全优化：使用unchecked进行全局统计更新
        unchecked {
            globalMiningStats.totalRewards += totalReward;
            globalMiningStats.lockedRewards += lockedAmount;
            globalMiningStats.immediateRewards += immediateAmount;
            globalMiningStats.activityBonusTotal += activityBonus;
            globalMiningStats.stakeLevelBonusTotal += stakeLevelBonus;
            globalMiningStats.earlyAdopterBonusTotal += earlyAdopterBonus;
            globalMiningStats.consecutiveBonusTotal += consecutiveBonus;
            globalMiningStats.rewardCount++;
        }
        globalMiningStats.lastRewardTime = block.timestamp;

        userRewardHistory[_user].push(RewardRecord({
            timestamp: block.timestamp,
            totalReward: totalReward,
            lockedAmount: lockedAmount,
            activityBonus: activityBonus,
            stakeLevelBonus: stakeLevelBonus,
            earlyAdopterBonus: earlyAdopterBonus,
            consecutiveBonus: consecutiveBonus
        }));

        emit RewardDistributed(_user, totalReward, lockedAmount, activityBonus, stakeLevelBonus, earlyAdopterBonus, consecutiveBonus, lockPercentage);

        return totalReward;
    }

    function setUserBonus(
        address _user,
        uint256 _activityMultiplier,
        uint256 _stakeLevelMultiplier
    ) external onlyOwner {
        if (_user == address(0)) revert TokenErrors.ZeroAddress();
        if (_activityMultiplier > MAX_ACTIVITY_MULTIPLIER) revert TokenErrors.InvalidOperation();
        if (_stakeLevelMultiplier > MAX_STAKE_LEVEL_MULTIPLIER) revert TokenErrors.InvalidOperation();

        userBonuses[_user].activityMultiplier = _activityMultiplier;
        userBonuses[_user].stakeLevelMultiplier = _stakeLevelMultiplier;
        userBonuses[_user].lastUpdateTime = block.timestamp;

        emit BonusUpdated(_user, _activityMultiplier, _stakeLevelMultiplier);
    }

    function batchSetUserBonus(
        address[] calldata _users,
        uint256[] calldata _activityMultipliers,
        uint256[] calldata _stakeLevelMultipliers
    ) external onlyOwner {
        if (_users.length != _activityMultipliers.length || _users.length != _stakeLevelMultipliers.length) {
            revert TokenErrors.InvalidArrayLength();
        }

        // 🔒 安全优化：批量设置用户奖励循环优化
        for (uint256 i = 0; i < _users.length;) {
            if (_users[i] == address(0)) revert TokenErrors.ZeroAddress();
            if (_activityMultipliers[i] > MAX_ACTIVITY_MULTIPLIER) revert TokenErrors.InvalidOperation();
            if (_stakeLevelMultipliers[i] > MAX_STAKE_LEVEL_MULTIPLIER) revert TokenErrors.InvalidOperation();

            userBonuses[_users[i]].activityMultiplier = _activityMultipliers[i];
            userBonuses[_users[i]].stakeLevelMultiplier = _stakeLevelMultipliers[i];
            userBonuses[_users[i]].lastUpdateTime = block.timestamp;

            emit BonusUpdated(_users[i], _activityMultipliers[i], _stakeLevelMultipliers[i]);
            unchecked { i++; }
        }
    }

    function setAuthorizedPool(address _pool, bool _status) external onlyOwner {
        if (_pool == address(0)) revert TokenErrors.ZeroAddress();
        authorizedPools[_pool] = _status;
        emit PoolAuthorized(_pool, _status);
    }

    function setLockParams(uint256 _lockPercentage, uint256 _lockDuration) external onlyOwner {
        if (_lockPercentage > 100) revert TokenErrors.InvalidPercentage();
        lockPercentage = _lockPercentage;
        lockDuration = _lockDuration;
        emit LockParamsUpdated(_lockPercentage, _lockDuration);
    }

    function setConsecutiveBonusPercentage(uint256 _percentage) external onlyOwner {
        if (_percentage > 100) revert TokenErrors.InvalidPercentage();
        consecutiveBonusPercentage = _percentage;
        emit ConsecutiveBonusUpdated(_percentage);
    }

    function getUserMiningStats(address _user) external view returns (
        uint256 totalRewards,
        uint256 lockedRewards,
        uint256 immediateRewards,
        uint256 activityBonusTotal,
        uint256 stakeLevelBonusTotal,
        uint256 earlyAdopterBonusTotal,
        uint256 consecutiveBonusTotal,
        uint256 lastRewardTime,
        uint256 rewardCount
    ) {
        MiningStats memory stats = userMiningStats[_user];
        return (
            stats.totalRewards,
            stats.lockedRewards,
            stats.immediateRewards,
            stats.activityBonusTotal,
            stats.stakeLevelBonusTotal,
            stats.earlyAdopterBonusTotal,
            stats.consecutiveBonusTotal,
            stats.lastRewardTime,
            stats.rewardCount
        );
    }

    function getGlobalMiningStats() external view returns (
        uint256 totalRewards,
        uint256 lockedRewards,
        uint256 immediateRewards,
        uint256 activityBonusTotal,
        uint256 stakeLevelBonusTotal,
        uint256 earlyAdopterBonusTotal,
        uint256 consecutiveBonusTotal,
        uint256 lastRewardTime,
        uint256 rewardCount
    ) {
        return (
            globalMiningStats.totalRewards,
            globalMiningStats.lockedRewards,
            globalMiningStats.immediateRewards,
            globalMiningStats.activityBonusTotal,
            globalMiningStats.stakeLevelBonusTotal,
            globalMiningStats.earlyAdopterBonusTotal,
            globalMiningStats.consecutiveBonusTotal,
            globalMiningStats.lastRewardTime,
            globalMiningStats.rewardCount
        );
    }
    
    function setPaused(bool _paused) external onlyOwner {
        if (_paused) {
            _pause();
        } else {
            _unpause();
        }
    }

    function getUserRewardHistoryPaged(address _user, uint256 offset, uint256 limit) external view returns (RewardRecord[] memory) {
        uint256 total = userRewardHistory[_user].length;
        if (offset >= total) {
            return new RewardRecord[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        RewardRecord[] memory result = new RewardRecord[](end - offset);
        // 🔒 安全优化：分页获取奖励历史循环优化
        for (uint256 i = offset; i < end;) {
            result[i - offset] = userRewardHistory[_user][i];
            unchecked { i++; }
        }
        return result;
    }

    function emergencyWithdrawPAT(uint256 _amount) external onlyOwner {
        if (address(patoken) == address(0)) revert TokenErrors.InvalidAddress("PAT Token");
        if (_amount > patoken.balanceOf(address(this))) revert TokenErrors.InsufficientBalance();
        patoken.transfer(owner(), _amount);
    }

    function recoverToken(address _token, uint256 _amount) external onlyOwner {
        if (_token == address(0)) revert TokenErrors.ZeroAddress();
        if (_amount == 0) revert TokenErrors.ZeroAmount();

        if (_token == address(patoken)) revert TokenErrors.InvalidOperation();

        IERC20(_token).transfer(owner(), _amount);

        emit TokenRecovered(_token, _amount);
    }

    // 🔒 安全优化：添加实用的查询函数

    /**
     * @dev 获取用户当前的奖励倍数
     */
    function getUserBonusMultipliers(address _user) external view returns (
        uint256 activityMultiplier,
        uint256 stakeLevelMultiplier,
        uint256 lastUpdateTime
    ) {
        UserBonus memory bonus = userBonuses[_user];
        return (
            bonus.activityMultiplier > 0 ? bonus.activityMultiplier : 100,
            bonus.stakeLevelMultiplier > 0 ? bonus.stakeLevelMultiplier : 100,
            bonus.lastUpdateTime
        );
    }

    /**
     * @dev 检查是否在早期采用者奖励期内
     */
    function isEarlyAdopterPeriod() external view returns (bool) {
        return block.timestamp < deployTime + 180 days;
    }

    /**
     * @dev 获取奖励配置信息
     */
    function getRewardConfig() external view returns (
        uint256 _lockPercentage,
        uint256 _lockDuration,
        uint256 _consecutiveBonusPercentage,
        bool _isEarlyAdopterPeriod
    ) {
        return (
            lockPercentage,
            lockDuration,
            consecutiveBonusPercentage,
            block.timestamp < deployTime + 180 days
        );
    }

    /**
     * @dev 获取用户奖励历史记录数量
     */
    function getUserRewardHistoryCount(address _user) external view returns (uint256) {
        return userRewardHistory[_user].length;
    }
}