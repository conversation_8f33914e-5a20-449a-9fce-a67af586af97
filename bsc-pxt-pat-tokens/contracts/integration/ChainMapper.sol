// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

/**
 * @title ChainMapper
 * @dev 跨链映射管理合约 - 管理多链部署的合约地址映射
 * @notice 这个合约负责维护不同区块链上合约地址的映射关系
 * <AUTHOR> Team
 *
 * 功能特性:
 * - 链管理：添加和管理支持的区块链网络
 * - 合约映射：维护不同链上相同功能合约的地址映射
 * - 批量操作：支持批量设置和查询合约地址
 * - 日志记录：完整的映射操作历史记录
 * - 权限控制：管理员权限和操作审计
 * - 类型管理：支持多种合约类型的分类管理
 */

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "../interfaces/TokenErrors.sol";

contract ChainMapper is Ownable, ReentrancyGuard {
    struct ChainConfig {
        string name;
        uint256 chainId;
        bool isActive;
        mapping(string => address) contracts;
        uint256 lastUpdated;
    }

    uint256[] public supportedChainIds;

    mapping(uint256 => ChainConfig) public chainConfigs;

    string[] public contractTypes;

    struct MappingLog {
        uint256 timestamp;
        uint256 sourceChainId;
        uint256 targetChainId;
        string contractType;
        address sourceAddress;
        address targetAddress;
        bytes32 mappingId;
    }

    MappingLog[] private mappingLogs;

    mapping(address => bool) public administrators;

    uint256 public constant MAX_LOGS = 1000;

    event ChainAdded(uint256 indexed chainId, string name);
    event ChainStatusUpdated(uint256 indexed chainId, bool isActive);
    event ContractTypeAdded(string contractType);
    event ContractMapped(
        bytes32 indexed mappingId,
        uint256 indexed sourceChainId,
        uint256 indexed targetChainId,
        string contractType,
        address sourceAddress,
        address targetAddress
    );
    event ContractAddressUpdated(
        uint256 indexed chainId,
        string indexed contractType,
        address oldAddress,
        address newAddress
    );
    event AdministratorUpdated(address indexed admin, bool status);

    modifier onlyAdmin() {
        if (!administrators[_msgSender()] && _msgSender() != owner()) revert TokenErrors.Unauthorized();
        _;
    }

    constructor() {
        administrators[_msgSender()] = true;
        emit AdministratorUpdated(_msgSender(), true);
    }

    function setAdministrator(address _admin, bool _status) external onlyOwner {
        if (_admin == address(0)) revert TokenErrors.ZeroAddress();
        administrators[_admin] = _status;
        emit AdministratorUpdated(_admin, _status);
    }

    function addChain(uint256 _chainId, string memory _name) external onlyAdmin {
        if (_chainId == 0) revert TokenErrors.InvalidOperation();
        if (isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();
        if (bytes(_name).length == 0) revert TokenErrors.EmptyName();

        ChainConfig storage config = chainConfigs[_chainId];
        config.name = _name;
        config.chainId = _chainId;
        config.isActive = true;
        config.lastUpdated = block.timestamp;

        supportedChainIds.push(_chainId);

        emit ChainAdded(_chainId, _name);
    }

    function updateChainStatus(uint256 _chainId, bool _isActive) external onlyAdmin {
        if (!isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();

        chainConfigs[_chainId].isActive = _isActive;
        chainConfigs[_chainId].lastUpdated = block.timestamp;

        emit ChainStatusUpdated(_chainId, _isActive);
    }

    function addContractType(string memory _contractType) external onlyAdmin {
        if (bytes(_contractType).length == 0) revert TokenErrors.EmptyName();

        // 🔒 安全优化：合约类型检查循环优化
        for (uint256 i = 0; i < contractTypes.length;) {
            if (_compareStringsIgnoreCase(contractTypes[i], _contractType)) {
                revert TokenErrors.InvalidOperation();
            }
            unchecked { i++; }
        }

        contractTypes.push(_contractType);

        emit ContractTypeAdded(_contractType);
    }

    function batchAddContractTypes(string[] memory _contractTypes) external onlyAdmin {
        if (_contractTypes.length == 0) revert TokenErrors.InvalidOperation();

        // 🔒 安全优化：批量添加合约类型循环优化
        for (uint256 i = 0; i < _contractTypes.length;) {
            if (bytes(_contractTypes[i]).length == 0) revert TokenErrors.EmptyName();

            bool exists = false;
            for (uint256 j = 0; j < contractTypes.length;) {
                if (_compareStringsIgnoreCase(contractTypes[j], _contractTypes[i])) {
                    exists = true;
                    break;
                }
                unchecked { j++; }
            }

            if (!exists) {
                contractTypes.push(_contractTypes[i]);
                emit ContractTypeAdded(_contractTypes[i]);
            }
            unchecked { i++; }
        }
    }

    function setContractAddress(
        uint256 _chainId,
        string memory _contractType,
        address _contractAddress
    ) external onlyAdmin {
        if (!isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();
        if (!contractTypeExists(_contractType)) revert TokenErrors.InvalidOperation();
        if (_contractAddress == address(0)) revert TokenErrors.ZeroAddress();

        address oldAddress = chainConfigs[_chainId].contracts[_contractType];
        chainConfigs[_chainId].contracts[_contractType] = _contractAddress;
        chainConfigs[_chainId].lastUpdated = block.timestamp;

        emit ContractAddressUpdated(_chainId, _contractType, oldAddress, _contractAddress);
    }

    function batchSetContractAddresses(
        uint256[] memory _chainIds,
        string[] memory _contractTypes,
        address[] memory _contractAddresses
    ) external onlyAdmin {
        if (_chainIds.length != _contractTypes.length || _contractTypes.length != _contractAddresses.length) {
            revert TokenErrors.InvalidArrayLength();
        }

        // 🔒 安全优化：批量设置合约地址循环优化
        for (uint256 i = 0; i < _chainIds.length;) {
            if (!isChainSupported(_chainIds[i])) {
                unchecked { i++; }
                continue;
            }
            if (!contractTypeExists(_contractTypes[i])) {
                unchecked { i++; }
                continue;
            }
            if (_contractAddresses[i] == address(0)) {
                unchecked { i++; }
                continue;
            }

            address oldAddress = chainConfigs[_chainIds[i]].contracts[_contractTypes[i]];
            chainConfigs[_chainIds[i]].contracts[_contractTypes[i]] = _contractAddresses[i];
            chainConfigs[_chainIds[i]].lastUpdated = block.timestamp;

            emit ContractAddressUpdated(_chainIds[i], _contractTypes[i], oldAddress, _contractAddresses[i]);
            unchecked { i++; }
        }
    }

    function mapContract(
        uint256 _sourceChainId,
        uint256 _targetChainId,
        string memory _contractType,
        address _sourceAddress,
        address _targetAddress
    ) public onlyAdmin returns (bytes32 mappingId) {
        if (!isChainSupported(_sourceChainId)) revert TokenErrors.InvalidOperation();
        if (!isChainSupported(_targetChainId)) revert TokenErrors.InvalidOperation();
        if (!contractTypeExists(_contractType)) revert TokenErrors.InvalidOperation();
        if (_sourceAddress == address(0)) revert TokenErrors.ZeroAddress();
        if (_targetAddress == address(0)) revert TokenErrors.ZeroAddress();

        chainConfigs[_sourceChainId].contracts[_contractType] = _sourceAddress;
        chainConfigs[_targetChainId].contracts[_contractType] = _targetAddress;

        chainConfigs[_sourceChainId].lastUpdated = block.timestamp;
        chainConfigs[_targetChainId].lastUpdated = block.timestamp;

        mappingId = keccak256(abi.encodePacked(
            _sourceChainId,
            _targetChainId,
            _contractType,
            _sourceAddress,
            _targetAddress,
            block.timestamp
        ));

        if (mappingLogs.length >= MAX_LOGS) {
            // 🔒 安全优化：日志轮转循环优化
            for (uint256 i = 0; i < mappingLogs.length - 1;) {
                mappingLogs[i] = mappingLogs[i + 1];
                unchecked { i++; }
            }
            mappingLogs.pop();
        }

        mappingLogs.push(MappingLog({
            timestamp: block.timestamp,
            sourceChainId: _sourceChainId,
            targetChainId: _targetChainId,
            contractType: _contractType,
            sourceAddress: _sourceAddress,
            targetAddress: _targetAddress,
            mappingId: mappingId
        }));

        emit ContractMapped(
            mappingId,
            _sourceChainId,
            _targetChainId,
            _contractType,
            _sourceAddress,
            _targetAddress
        );

        return mappingId;
    }

    function batchMapContracts(
        uint256[] memory _sourceChainIds,
        uint256[] memory _targetChainIds,
        string[] memory _contractTypes,
        address[] memory _sourceAddresses,
        address[] memory _targetAddresses
    ) external onlyAdmin returns (bytes32[] memory mappingIds) {
        uint256 length = _sourceChainIds.length;
        if (
            length != _targetChainIds.length ||
            length != _contractTypes.length ||
            length != _sourceAddresses.length ||
            length != _targetAddresses.length
        ) {
            revert TokenErrors.InvalidArrayLength();
        }

        mappingIds = new bytes32[](length);

        // 🔒 安全优化：批量映射合约循环优化
        for (uint256 i = 0; i < length;) {
            if (
                !isChainSupported(_sourceChainIds[i]) ||
                !isChainSupported(_targetChainIds[i]) ||
                !contractTypeExists(_contractTypes[i]) ||
                _sourceAddresses[i] == address(0) ||
                _targetAddresses[i] == address(0)
            ) {
                unchecked { i++; }
                continue;
            }

            mappingIds[i] = mapContract(
                _sourceChainIds[i],
                _targetChainIds[i],
                _contractTypes[i],
                _sourceAddresses[i],
                _targetAddresses[i]
            );
            unchecked { i++; }
        }

        return mappingIds;
    }

    function getContractAddress(
        uint256 _chainId,
        string memory _contractType
    ) external view returns (address) {
        if (!isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();
        if (!contractTypeExists(_contractType)) revert TokenErrors.InvalidOperation();

        return chainConfigs[_chainId].contracts[_contractType];
    }

    function batchGetContractAddresses(
        uint256[] memory _chainIds,
        string[] memory _contractTypes
    ) external view returns (address[] memory addresses) {
        if (_chainIds.length != _contractTypes.length) {
            revert TokenErrors.InvalidArrayLength();
        }

        addresses = new address[](_chainIds.length);

        // 🔒 安全优化：批量获取合约地址循环优化
        for (uint256 i = 0; i < _chainIds.length;) {
            if (isChainSupported(_chainIds[i]) && contractTypeExists(_contractTypes[i])) {
                addresses[i] = chainConfigs[_chainIds[i]].contracts[_contractTypes[i]];
            } else {
                addresses[i] = address(0);
            }
            unchecked { i++; }
        }

        return addresses;
    }

    function getChainCount() external view returns (uint256) {
        return supportedChainIds.length;
    }

    function getChainName(uint256 _chainId) external view returns (string memory) {
        if (!isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();

        return chainConfigs[_chainId].name;
    }

    function getAllChainIds() external view returns (uint256[] memory) {
        return supportedChainIds;
    }

    function getAllContractTypes() external view returns (string[] memory) {
        return contractTypes;
    }

    function getChainConfig(uint256 _chainId) external view returns (
        string memory name,
        uint256 chainId,
        bool isActive,
        uint256 lastUpdated
    ) {
        if (!isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();

        ChainConfig storage config = chainConfigs[_chainId];

        return (
            config.name,
            config.chainId,
            config.isActive,
            config.lastUpdated
        );
    }

    function getMappingLogsCount() external view returns (uint256) {
        return mappingLogs.length;
    }

    function isChainSupported(uint256 _chainId) public view returns (bool) {
        // 🔒 安全优化：链支持检查循环优化
        for (uint256 i = 0; i < supportedChainIds.length;) {
            if (supportedChainIds[i] == _chainId) {
                return true;
            }
            unchecked { i++; }
        }
        return false;
    }

    function contractTypeExists(string memory _contractType) public view returns (bool) {
        // 🔒 安全优化：合约类型存在检查循环优化
        for (uint256 i = 0; i < contractTypes.length;) {
            if (_compareStringsIgnoreCase(contractTypes[i], _contractType)) {
                return true;
            }
            unchecked { i++; }
        }
        return false;
    }

    function getAllContractAddresses(uint256 _chainId) external view returns (string[] memory, address[] memory) {
        if (!isChainSupported(_chainId)) revert TokenErrors.InvalidOperation();
        address[] memory addresses = new address[](contractTypes.length);
        // 🔒 安全优化：获取所有合约地址循环优化
        for (uint256 i = 0; i < contractTypes.length;) {
            addresses[i] = chainConfigs[_chainId].contracts[contractTypes[i]];
            unchecked { i++; }
        }
        return (contractTypes, addresses);
    }

    function getMappingLogsPaged(uint256 offset, uint256 limit) external view returns (MappingLog[] memory) {
        uint256 total = mappingLogs.length;
        if (offset >= total) {
            return new MappingLog[](0);
        }
        uint256 end = offset + limit > total ? total : offset + limit;
        MappingLog[] memory result = new MappingLog[](end - offset);
        // 🔒 安全优化：分页获取映射日志循环优化
        for (uint256 i = offset; i < end;) {
            result[i - offset] = mappingLogs[i];
            unchecked { i++; }
        }
        return result;
    }

    function _compareStringsIgnoreCase(string memory a, string memory b) internal pure returns (bool) {
        return keccak256(bytes(_toLower(a))) == keccak256(bytes(_toLower(b)));
    }

    function _toLower(string memory str) internal pure returns (string memory) {
        bytes memory bStr = bytes(str);
        bytes memory bLower = new bytes(bStr.length);

        // 🔒 安全优化：字符串转小写循环优化
        for (uint256 i = 0; i < bStr.length;) {
            if (uint8(bStr[i]) >= 65 && uint8(bStr[i]) <= 90) {
                unchecked {
                    bLower[i] = bytes1(uint8(bStr[i]) + 32);
                }
            } else {
                bLower[i] = bStr[i];
            }
            unchecked { i++; }
        }

        return string(bLower);
    }

    // 🔒 安全优化：添加实用的查询函数

    /**
     * @dev 检查合约地址是否已设置
     */
    function isContractAddressSet(uint256 _chainId, string memory _contractType) external view returns (bool) {
        if (!isChainSupported(_chainId) || !contractTypeExists(_contractType)) {
            return false;
        }
        return chainConfigs[_chainId].contracts[_contractType] != address(0);
    }

    /**
     * @dev 获取活跃链的数量
     */
    function getActiveChainCount() external view returns (uint256) {
        uint256 count = 0;
        for (uint256 i = 0; i < supportedChainIds.length;) {
            if (chainConfigs[supportedChainIds[i]].isActive) {
                unchecked { count++; }
            }
            unchecked { i++; }
        }
        return count;
    }

    /**
     * @dev 获取所有活跃链的ID
     */
    function getActiveChainIds() external view returns (uint256[] memory) {
        uint256 activeCount = this.getActiveChainCount();
        uint256[] memory activeChains = new uint256[](activeCount);
        uint256 index = 0;

        for (uint256 i = 0; i < supportedChainIds.length;) {
            if (chainConfigs[supportedChainIds[i]].isActive) {
                activeChains[index] = supportedChainIds[i];
                unchecked { index++; }
            }
            unchecked { i++; }
        }

        return activeChains;
    }
}