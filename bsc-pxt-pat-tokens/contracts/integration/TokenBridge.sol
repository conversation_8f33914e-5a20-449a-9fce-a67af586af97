// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.24;

/**
 * @title TokenBridge
 * @dev 代币跨链桥接合约 - 支持PXT/PAT代币的跨链转移
 * @notice 这个合约负责代币的锁定、释放和跨链验证
 * <AUTHOR> Team
 *
 * 功能特性:
 * - 代币锁定：在源链锁定代币用于跨链转移
 * - 代币释放：在目标链释放对应数量的代币
 * - 多签验证：基于验证者多签的安全机制
 * - 费用管理：灵活的跨链费用配置
 * - 请求管理：完整的跨链请求生命周期管理
 * - 双向跨链：支持代币的双向跨链转移
 * - 安全控制：完善的权限控制和暂停机制
 */

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import "../interfaces/IPXT.sol";
import "../interfaces/IPAT.sol";
import "../interfaces/TokenErrors.sol";

contract TokenBridge is Ownable, ReentrancyGuard, Pausable {
    using ECDSA for bytes32;

    IPXT public pxtoken;
    IPAT public patoken;

    mapping(uint256 => bool) public supportedChainIds;

    mapping(uint256 => address[]) public chainValidators;

    mapping(uint256 => uint256) public requiredConfirmations;

    struct FeeConfig {
        uint256 baseFee;
        uint256 percentFee;
        uint256 minFee;
        uint256 maxFee;
        uint256 lastUpdated;
    }

    mapping(uint256 => FeeConfig) public chainFees;

    struct BridgeRequest {
        uint256 id;
        address sender;
        address receiver;
        uint256 amount;
        uint256 fee;
        uint256 sourceChainId;
        uint256 targetChainId;
        address tokenAddress;
        uint256 timestamp;
        uint256 expiry;
        uint256 nonce;
        bool isProcessed;
        bytes[] signatures;
    }

    mapping(bytes32 => BridgeRequest) public bridgeRequests;

    uint256 public requestCount;

    mapping(bytes32 => uint256) public requestHashToId;

    mapping(address => uint256) public userNonces;

    mapping(uint256 => mapping(uint256 => mapping(uint256 => bool))) public usedNonces;

    uint256 public lockedPXT;
    uint256 public lockedPAT;

    address public feeReceiver;

    mapping(address => bool) public administrators;

    mapping(address => bool) public bridgeValidators;

    uint256 public requestExpiry = 3 days;

    mapping(uint256 => string) public chainNames;

    // ============ 双向跨链新增状态变量 ============
    mapping(bytes32 => bool) public processedUnlockRequests;  // 已处理的解锁请求
    mapping(address => uint256) public lockedTokens;          // 锁定的代币数量
    uint256 public unlockRequestCounter;                      // 解锁请求计数器

    event TokensLocked(
        bytes32 indexed requestHash,
        uint256 indexed requestId,
        address indexed sender,
        address receiver,
        uint256 amount,
        uint256 fee,
        uint256 sourceChainId,
        uint256 targetChainId,
        address tokenAddress,
        uint256 nonce,
        uint256 expiry
    );

    event TokensReleased(
        bytes32 indexed requestHash,
        uint256 indexed requestId,
        address indexed receiver,
        uint256 amount,
        uint256 sourceChainId,
        uint256 targetChainId,
        address tokenAddress
    );

    event BridgeRequestConfirmed(
        bytes32 indexed requestHash,
        address indexed validator,
        uint256 confirmations,
        uint256 required
    );

    event BridgeRequestProcessed(
        bytes32 indexed requestHash,
        uint256 indexed requestId
    );

    event ChainSupportAdded(
        uint256 indexed chainId,
        address[] validators,
        uint256 requiredConfirmations
    );

    event ChainSupportRemoved(uint256 indexed chainId);

    event FeeConfigUpdated(
        uint256 indexed chainId,
        uint256 baseFee,
        uint256 percentFee,
        uint256 minFee,
        uint256 maxFee
    );

    // ============ 双向跨链新增事件 ============
    event TokensUnlocked(
        bytes32 indexed unlockRequestId,
        address indexed recipient,
        uint256 amount,
        address tokenAddress,
        bytes32 sourceBurnTxHash
    );

    event UnlockRequestCreated(
        bytes32 indexed unlockRequestId,
        address indexed recipient,
        uint256 amount,
        address tokenAddress,
        bytes32 sourceBurnTxHash,
        uint256 sourceChainId
    );

    event ValidatorUpdated(address indexed validator, bool status);
    event AdministratorUpdated(address indexed admin, bool status);
    event EmergencyWithdraw(address indexed token, uint256 amount, address indexed recipient);
    event RequestExpiryUpdated(uint256 oldExpiry, uint256 newExpiry);

    modifier onlyAdmin() {
        if (!administrators[_msgSender()] && _msgSender() != owner()) revert TokenErrors.Unauthorized();
        _;
    }

    modifier onlyValidator() {
        if (!bridgeValidators[_msgSender()] && !administrators[_msgSender()] && _msgSender() != owner()) {
            revert TokenErrors.Unauthorized();
        }
        _;
    }

    constructor(
        address _pxtoken,
        address _patoken,
        address _feeReceiver
    ) {
        if (_pxtoken == address(0)) revert TokenErrors.ZeroAddress();
        if (_patoken == address(0)) revert TokenErrors.ZeroAddress();
        if (_feeReceiver == address(0)) revert TokenErrors.ZeroAddress();

        pxtoken = IPXT(_pxtoken);
        patoken = IPAT(_patoken);
        feeReceiver = _feeReceiver;

        administrators[_msgSender()] = true;
        bridgeValidators[_msgSender()] = true;

        emit AdministratorUpdated(_msgSender(), true);
        emit ValidatorUpdated(_msgSender(), true);

        chainNames[1] = "Ethereum";
        chainNames[56] = "BSC";
        chainNames[137] = "Polygon";
        chainNames[42161] = "Arbitrum";
    }

    function setAdministrator(address _admin, bool _status) external onlyOwner {
        if (_admin == address(0)) revert TokenErrors.ZeroAddress();
        administrators[_admin] = _status;
        emit AdministratorUpdated(_admin, _status);
    }

    function setValidator(address _validator, bool _status) external onlyAdmin {
        if (_validator == address(0)) revert TokenErrors.ZeroAddress();
        bridgeValidators[_validator] = _status;
        emit ValidatorUpdated(_validator, _status);
    }

    function setRequestExpiry(uint256 _expiryTime) external onlyAdmin {
        if (_expiryTime < 1 hours || _expiryTime > 7 days) revert TokenErrors.InvalidOperation();
        uint256 oldExpiry = requestExpiry;
        requestExpiry = _expiryTime;
        emit RequestExpiryUpdated(oldExpiry, _expiryTime);
    }

    function setChainName(uint256 _chainId, string memory _name) external onlyAdmin {
        if (_chainId == 0) revert TokenErrors.InvalidOperation();
        if (bytes(_name).length == 0) revert TokenErrors.EmptyName();
        chainNames[_chainId] = _name;
    }

    function lockTokens(
        address _receiver,
        uint256 _amount,
        uint256 _targetChainId,
        address _tokenAddress
    ) external payable whenNotPaused nonReentrant returns (bytes32 requestHash) {
        if (_receiver == address(0)) revert TokenErrors.ZeroAddress();
        if (_amount == 0) revert TokenErrors.ZeroAmount();
        if (!supportedChainIds[_targetChainId]) revert TokenErrors.InvalidOperation();
        if (_tokenAddress != address(pxtoken) && _tokenAddress != address(patoken)) {
            revert TokenErrors.InvalidOperation();
        }

        uint256 sourceChainId = block.chainid;

        uint256 fee = calculateFee(_amount, _targetChainId);
        if (msg.value < fee) revert TokenErrors.InsufficientBalance();

        (bool success, ) = feeReceiver.call{value: fee}("");
        if (!success) revert TokenErrors.TransferFailed();

        if (msg.value > fee) {
            (success, ) = _msgSender().call{value: msg.value - fee}("");
            if (!success) revert TokenErrors.TransferFailed();
        }

        uint256 nonce = userNonces[_msgSender()]++;

        uint256 expiry = block.timestamp + requestExpiry;

        if (_tokenAddress == address(pxtoken)) {
            pxtoken.transferFrom(_msgSender(), address(this), _amount);
            lockedPXT += _amount;
            lockedTokens[_tokenAddress] += _amount;  // 记录锁定数量
        } else {
            patoken.transferFrom(_msgSender(), address(this), _amount);
            lockedPAT += _amount;
            lockedTokens[_tokenAddress] += _amount;  // 记录锁定数量
        }

        uint256 requestId = ++requestCount;

        requestHash = keccak256(
            abi.encodePacked(
                "BRIDGE",
                requestId,
                _msgSender(),
                _receiver,
                _amount,
                fee,
                sourceChainId,
                _targetChainId,
                _tokenAddress,
                nonce,
                expiry
            )
        );

        bridgeRequests[requestHash] = BridgeRequest({
            id: requestId,
            sender: _msgSender(),
            receiver: _receiver,
            amount: _amount,
            fee: fee,
            sourceChainId: sourceChainId,
            targetChainId: _targetChainId,
            tokenAddress: _tokenAddress,
            timestamp: block.timestamp,
            expiry: expiry,
            nonce: nonce,
            isProcessed: false,
            signatures: new bytes[](0)
        });

        requestHashToId[requestHash] = requestId;

        emit TokensLocked(
            requestHash,
            requestId,
            _msgSender(),
            _receiver,
            _amount,
            fee,
            sourceChainId,
            _targetChainId,
            _tokenAddress,
            nonce,
            expiry
        );

        return requestHash;
    }

    function confirmRequest(bytes32 _requestHash, bytes calldata _signature) external onlyValidator whenNotPaused nonReentrant returns (bool) {
        BridgeRequest storage request = bridgeRequests[_requestHash];

        if (request.id == 0) revert TokenErrors.InvalidOperation();
        if (request.isProcessed) revert TokenErrors.InvalidOperation();
        if (block.timestamp > request.expiry) revert TokenErrors.RequestExpired();

        if (usedNonces[request.sourceChainId][request.targetChainId][request.nonce]) {
            revert TokenErrors.NonceAlreadyUsed();
        }

        bytes32 messageHash = getRequestMessageHash(_requestHash, request);
        address signer = messageHash.recover(_signature);

        bool isValidValidator = false;
        // 🔒 安全优化：验证者检查循环优化
        for (uint256 i = 0; i < chainValidators[request.targetChainId].length;) {
            if (chainValidators[request.targetChainId][i] == signer) {
                isValidValidator = true;
                break;
            }
            unchecked { i++; }
        }

        if (!isValidValidator) revert TokenErrors.InvalidSignature();

        // 🔒 安全优化：重复签名检查循环优化
        for (uint256 i = 0; i < request.signatures.length;) {
            address existingSigner = messageHash.recover(request.signatures[i]);
            if (existingSigner == signer) revert TokenErrors.DuplicateSignature();
            unchecked { i++; }
        }

        request.signatures.push(_signature);

        uint256 required = requiredConfirmations[request.targetChainId];

        emit BridgeRequestConfirmed(
            _requestHash,
            signer,
            request.signatures.length,
            required
        );

        if (request.signatures.length >= required) {
            _processRequest(_requestHash, request);
            return true;
        }

        return false;
    }

    function _processRequest(bytes32 _requestHash, BridgeRequest storage _request) internal {
        usedNonces[_request.sourceChainId][_request.targetChainId][_request.nonce] = true;

        _request.isProcessed = true;

        if (_request.targetChainId == block.chainid) {
            if (_request.tokenAddress == address(pxtoken)) {
                pxtoken.transfer(_request.receiver, _request.amount);
            } else {
                patoken.transfer(_request.receiver, _request.amount);
            }

            emit TokensReleased(
                _requestHash,
                _request.id,
                _request.receiver,
                _request.amount,
                _request.sourceChainId,
                _request.targetChainId,
                _request.tokenAddress
            );
        }

        emit BridgeRequestProcessed(_requestHash, _request.id);
    }

    function getRequestMessageHash(bytes32 _requestHash, BridgeRequest memory _request) public view returns (bytes32) {
        string memory sourceChainName = chainNames[_request.sourceChainId];
        string memory targetChainName = chainNames[_request.targetChainId];

        return keccak256(
            abi.encodePacked(
                "Bridge request from ",
                sourceChainName,
                " to ",
                targetChainName,
                " with hash: ",
                _requestHash,
                " at timestamp: ",
                _request.timestamp,
                " with nonce: ",
                _request.nonce
            )
        );
    }

    function emergencyProcessRequest(bytes32 _requestHash) external onlyAdmin whenPaused {
        BridgeRequest storage request = bridgeRequests[_requestHash];

        if (request.id == 0) revert TokenErrors.InvalidOperation();
        if (request.isProcessed) revert TokenErrors.InvalidOperation();

        _processRequest(_requestHash, request);
    }

    function calculateFee(
        uint256 _amount,
        uint256 _targetChainId
    ) public view returns (uint256) {
        FeeConfig memory config = chainFees[_targetChainId];

        uint256 percentFeeAmount = (_amount * config.percentFee) / 10000;
        uint256 totalFee = config.baseFee + percentFeeAmount;

        if (totalFee < config.minFee) {
            return config.minFee;
        } else if (totalFee > config.maxFee) {
            return config.maxFee;
        } else {
            return totalFee;
        }
    }

    function addSupportedChain(
        uint256 _chainId,
        address[] memory _validators,
        uint256 _requiredConfirmations,
        uint256 _baseFee,
        uint256 _percentFee,
        uint256 _minFee,
        uint256 _maxFee
    ) external onlyAdmin {
        if (_chainId == 0) revert TokenErrors.InvalidOperation();
        if (_validators.length == 0) revert TokenErrors.InvalidOperation();
        if (_requiredConfirmations == 0 || _requiredConfirmations > _validators.length) {
            revert TokenErrors.InvalidOperation();
        }

        supportedChainIds[_chainId] = true;

        delete chainValidators[_chainId];
        // 🔒 安全优化：添加验证者循环优化
        for (uint256 i = 0; i < _validators.length;) {
            if (_validators[i] == address(0)) revert TokenErrors.ZeroAddress();
            chainValidators[_chainId].push(_validators[i]);
            unchecked { i++; }
        }

        requiredConfirmations[_chainId] = _requiredConfirmations;

        chainFees[_chainId] = FeeConfig({
            baseFee: _baseFee,
            percentFee: _percentFee,
            minFee: _minFee,
            maxFee: _maxFee,
            lastUpdated: block.timestamp
        });

        emit ChainSupportAdded(_chainId, _validators, _requiredConfirmations);

        emit FeeConfigUpdated(
            _chainId,
            _baseFee,
            _percentFee,
            _minFee,
            _maxFee
        );
    }

    function updateChainValidators(
        uint256 _chainId,
        address[] memory _validators,
        uint256 _requiredConfirmations
    ) external onlyAdmin {
        if (!supportedChainIds[_chainId]) revert TokenErrors.InvalidOperation();
        if (_validators.length == 0) revert TokenErrors.InvalidOperation();
        if (_requiredConfirmations == 0 || _requiredConfirmations > _validators.length) {
            revert TokenErrors.InvalidOperation();
        }

        delete chainValidators[_chainId];
        // 🔒 安全优化：更新验证者循环优化
        for (uint256 i = 0; i < _validators.length;) {
            if (_validators[i] == address(0)) revert TokenErrors.ZeroAddress();
            chainValidators[_chainId].push(_validators[i]);
            unchecked { i++; }
        }

        requiredConfirmations[_chainId] = _requiredConfirmations;

        emit ChainSupportAdded(_chainId, _validators, _requiredConfirmations);
    }

    function removeSupportedChain(uint256 _chainId) external onlyAdmin {
        if (!supportedChainIds[_chainId]) revert TokenErrors.InvalidOperation();

        supportedChainIds[_chainId] = false;
        delete chainValidators[_chainId];
        delete requiredConfirmations[_chainId];

        emit ChainSupportRemoved(_chainId);
    }

    function updateFeeConfig(
        uint256 _chainId,
        uint256 _baseFee,
        uint256 _percentFee,
        uint256 _minFee,
        uint256 _maxFee
    ) external onlyAdmin {
        if (!supportedChainIds[_chainId]) revert TokenErrors.InvalidOperation();
        if (_minFee > _maxFee) revert TokenErrors.InvalidOperation();

        chainFees[_chainId] = FeeConfig({
            baseFee: _baseFee,
            percentFee: _percentFee,
            minFee: _minFee,
            maxFee: _maxFee,
            lastUpdated: block.timestamp
        });

        emit FeeConfigUpdated(
            _chainId,
            _baseFee,
            _percentFee,
            _minFee,
            _maxFee
        );
    }

    function updateFeeReceiver(address _feeReceiver) external onlyAdmin {
        if (_feeReceiver == address(0)) revert TokenErrors.ZeroAddress();
        feeReceiver = _feeReceiver;
    }

    function pause() external onlyAdmin {
        _pause();
    }

    function unpause() external onlyAdmin {
        _unpause();
    }

    function emergencyWithdraw(
        address _token,
        uint256 _amount,
        address _recipient
    ) external onlyAdmin {
        if (_recipient == address(0)) revert TokenErrors.ZeroAddress();

        if (_token == address(0)) {
            if (_amount > address(this).balance) revert TokenErrors.InsufficientBalance();
            (bool success, ) = _recipient.call{value: _amount}("");
            if (!success) revert TokenErrors.TransferFailed();
        } else {
            IERC20 token = IERC20(_token);
            if (_amount > token.balanceOf(address(this))) revert TokenErrors.InsufficientBalance();
            bool success = token.transfer(_recipient, _amount);
            if (!success) revert TokenErrors.TransferFailed();
            if (_token == address(pxtoken)) {
                lockedPXT = _amount >= lockedPXT ? 0 : lockedPXT - _amount;
            } else if (_token == address(patoken)) {
                lockedPAT = _amount >= lockedPAT ? 0 : lockedPAT - _amount;
            }
        }
        emit EmergencyWithdraw(_token, _amount, _recipient);
    }

    function getChainValidators(uint256 _chainId) external view returns (address[] memory) {
        return chainValidators[_chainId];
    }

    function getRequestInfo(bytes32 _requestHash) external view returns (
        uint256 id,
        address sender,
        address receiver,
        uint256 amount,
        uint256 fee,
        uint256 sourceChainId,
        uint256 targetChainId,
        address tokenAddress,
        uint256 timestamp,
        uint256 expiry,
        uint256 nonce,
        bool isProcessed,
        uint256 confirmations
    ) {
        BridgeRequest memory request = bridgeRequests[_requestHash];

        return (
            request.id,
            request.sender,
            request.receiver,
            request.amount,
            request.fee,
            request.sourceChainId,
            request.targetChainId,
            request.tokenAddress,
            request.timestamp,
            request.expiry,
            request.nonce,
            request.isProcessed,
            request.signatures.length
        );
    }

    function verifyRequest(bytes32 _requestHash) external view returns (bool isValid, uint8 reason) {
        BridgeRequest memory request = bridgeRequests[_requestHash];

        if (request.id == 0) {
            return (false, 1);
        }

        if (request.isProcessed) {
            return (false, 2);
        }

        if (!supportedChainIds[request.targetChainId]) {
            return (false, 3);
        }

        if (block.timestamp > request.expiry) {
            return (false, 4);
        }

        if (usedNonces[request.sourceChainId][request.targetChainId][request.nonce]) {
            return (false, 5);
        }

        return (true, 0);
    }

    function getSupportedChains() external view returns (
        uint256[] memory chainIds,
        uint256[] memory confirmations
    ) {
        uint256 count = 0;

        // 🔒 安全优化：支持链计数循环优化
        for (uint256 i = 1; i <= 1000;) {
            if (supportedChainIds[i]) {
                unchecked { count++; }
            }
            unchecked { i++; }
        }

        chainIds = new uint256[](count);
        confirmations = new uint256[](count);

        uint256 index = 0;
        // 🔒 安全优化：支持链数据收集循环优化
        for (uint256 i = 1; i <= 1000;) {
            if (supportedChainIds[i]) {
                chainIds[index] = i;
                confirmations[index] = requiredConfirmations[i];
                unchecked { index++; }
            }
            unchecked { i++; }
        }

        return (chainIds, confirmations);
    }

    receive() external payable {}

    function getUserRequestsPaged(address _user, uint256 _page, uint256 _limit) external view returns (
        bytes32[] memory hashes,
        uint256[] memory ids,
        bool[] memory statuses
    ) {
        if (_page == 0 || _limit == 0 || _limit > 100) revert TokenErrors.InvalidOperation();

        bytes32[] memory userHashes = new bytes32[](requestCount);
        uint256 count = 0;

        // 🔒 安全优化：用户请求查找循环优化
        for (uint256 i = 1; i <= requestCount;) {
            bytes32 hash = bytes32(0);

            for (uint256 j = 0; j < 2**16;) {
                bytes32 potentialHash = keccak256(abi.encodePacked(i, j));
                if (requestHashToId[potentialHash] == i) {
                    hash = potentialHash;
                    break;
                }
                unchecked { j++; }
            }

            if (hash != bytes32(0)) {
                BridgeRequest memory request = bridgeRequests[hash];
                if (request.sender == _user) {
                    userHashes[count] = hash;
                    unchecked { count++; }
                }
            }
            unchecked { i++; }
        }

        uint256 startIndex = (_page - 1) * _limit;
        uint256 endIndex = startIndex + _limit > count ? count : startIndex + _limit;

        if (startIndex >= count) {
            return (new bytes32[](0), new uint256[](0), new bool[](0));
        }

        hashes = new bytes32[](endIndex - startIndex);
        ids = new uint256[](endIndex - startIndex);
        statuses = new bool[](endIndex - startIndex);

        // 🔒 安全优化：分页数据组装循环优化
        for (uint256 i = startIndex; i < endIndex;) {
            hashes[i - startIndex] = userHashes[i];
            BridgeRequest memory request = bridgeRequests[userHashes[i]];
            ids[i - startIndex] = request.id;
            statuses[i - startIndex] = request.isProcessed;
            unchecked { i++; }
        }

        return (hashes, ids, statuses);
    }

    function getRequestSignatures(bytes32 _requestHash) external view returns (bytes[] memory) {
        return bridgeRequests[_requestHash].signatures;
    }

    function getFeeConfig(uint256 _chainId) external view returns (
        uint256 baseFee,
        uint256 percentFee,
        uint256 minFee,
        uint256 maxFee,
        uint256 lastUpdated
    ) {
        FeeConfig memory config = chainFees[_chainId];
        return (
            config.baseFee,
            config.percentFee,
            config.minFee,
            config.maxFee,
            config.lastUpdated
        );
    }

    // ============ 双向跨链新增功能 ============

    /**
     * @dev 创建解锁请求（验证者调用）
     * @param tokenAddress 代币地址
     * @param recipient 接收者地址
     * @param amount 解锁数量
     * @param sourceChainId 源链ID
     * @param sourceBurnTxHash 源链销毁交易哈希
     */
    function createUnlockRequest(
        address tokenAddress,
        address recipient,
        uint256 amount,
        uint256 sourceChainId,
        bytes32 sourceBurnTxHash
    ) external onlyValidator whenNotPaused returns (bytes32 unlockRequestId) {
        if (tokenAddress == address(0)) revert TokenErrors.ZeroAddress();
        if (recipient == address(0)) revert TokenErrors.ZeroAddress();
        if (amount == 0) revert TokenErrors.ZeroAmount();
        if (sourceBurnTxHash == bytes32(0)) revert TokenErrors.InvalidOperation();
        if (lockedTokens[tokenAddress] < amount) revert TokenErrors.InsufficientBalance();

        // 生成解锁请求ID
        unlockRequestCounter++;
        unlockRequestId = keccak256(abi.encodePacked(
            "UNLOCK",
            unlockRequestCounter,
            tokenAddress,
            recipient,
            amount,
            sourceChainId,
            sourceBurnTxHash,
            block.timestamp
        ));

        if (processedUnlockRequests[unlockRequestId]) revert TokenErrors.InvalidOperation();

        // 标记为已处理
        processedUnlockRequests[unlockRequestId] = true;

        // 减少锁定数量
        lockedTokens[tokenAddress] -= amount;
        if (tokenAddress == address(pxtoken)) {
            lockedPXT -= amount;
        } else if (tokenAddress == address(patoken)) {
            lockedPAT -= amount;
        }

        // 转移代币给接收者
        IERC20(tokenAddress).transfer(recipient, amount);

        emit UnlockRequestCreated(unlockRequestId, recipient, amount, tokenAddress, sourceBurnTxHash, sourceChainId);
        emit TokensUnlocked(unlockRequestId, recipient, amount, tokenAddress, sourceBurnTxHash);

        return unlockRequestId;
    }

    /**
     * @dev 检查解锁请求是否已处理
     */
    function isUnlockRequestProcessed(bytes32 unlockRequestId) external view returns (bool) {
        return processedUnlockRequests[unlockRequestId];
    }

    /**
     * @dev 获取代币锁定数量
     */
    function getLockedTokenAmount(address tokenAddress) external view returns (uint256) {
        return lockedTokens[tokenAddress];
    }

    /**
     * @dev 添加验证者（仅管理员）
     */
    function addValidator(address validator) external onlyOwner {
        if (validator == address(0)) revert TokenErrors.ZeroAddress();
        bridgeValidators[validator] = true;
    }

    /**
     * @dev 移除验证者（仅管理员）
     */
    function removeValidator(address validator) external onlyOwner {
        bridgeValidators[validator] = false;
    }

    /**
     * @dev 检查是否为验证者 (已在上方定义，此处删除重复定义)
     */
}