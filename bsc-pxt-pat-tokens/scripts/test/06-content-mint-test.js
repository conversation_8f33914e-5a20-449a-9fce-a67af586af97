const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🎨 BSC链内容铸造NFT测试（90/10分成）");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("时间:", new Date().toISOString());
    
    // 获取账户
    const [deployer, user1, user2] = await ethers.getSigners();
    console.log("测试账户:");
    console.log("- 部署者:", deployer.address);
    console.log("- 用户1:", user1.address);
    console.log("- 用户2:", user2.address);
    
    // 加载部署信息
    let coreDeployment, contentDeployment;
    try {
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const contentFile = path.join(__dirname, "../../deployments", network.name, "content-deployment.json");
        
        coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        contentDeployment = JSON.parse(fs.readFileSync(contentFile, 'utf8'));
        console.log("✅ 已加载部署信息");
    } catch (error) {
        console.error("❌ 未找到部署文件");
        process.exit(1);
    }
    
    // 获取合约实例
    const PAToken = await ethers.getContractFactory("PAToken");
    const patToken = PAToken.attach(coreDeployment.contracts.PAToken.address);
    
    const ContentRegistry = await ethers.getContractFactory("ContentRegistry");
    const contentRegistry = ContentRegistry.attach(contentDeployment.contracts.ContentRegistry.address);
    
    const ContentCharacter = await ethers.getContractFactory("ContentCharacter");
    const contentCharacter = ContentCharacter.attach(contentDeployment.contracts.ContentCharacter.address);
    
    const ContentMint = await ethers.getContractFactory("ContentMint");
    const contentMint = ContentMint.attach(contentDeployment.contracts.ContentMint.address);
    
    console.log("\n=== 1. 检查合约状态 ===");
    console.log("📊 合约地址:");
    console.log("- PAT代币:", patToken.target);
    console.log("- 内容注册:", contentRegistry.target);
    console.log("- 内容角色:", contentCharacter.target);
    console.log("- 内容铸造:", contentMint.target);
    
    console.log("\n=== 2. 准备测试环境 ===");
    
    // 检查用户PAT余额
    const user1PatBalance = await patToken.balanceOf(user1.address);
    const user2PatBalance = await patToken.balanceOf(user2.address);
    console.log("💰 用户PAT余额:");
    console.log(`- 用户1: ${ethers.formatEther(user1PatBalance)} PAT`);
    console.log(`- 用户2: ${ethers.formatEther(user2PatBalance)} PAT`);
    
    // 如果用户PAT不足，部署者先铸造PAT再转账
    const requiredPat = ethers.parseEther("10"); // 每个用户需要10 PAT
    if (user1PatBalance < requiredPat) {
        console.log("💸 给用户1铸造PAT...");
        const mintTx1 = await patToken.connect(deployer).mint(user1.address, requiredPat);
        await mintTx1.wait();
        console.log("✅ 用户1 PAT铸造完成");
    }

    if (user2PatBalance < requiredPat) {
        console.log("💸 给用户2铸造PAT...");
        const mintTx2 = await patToken.connect(deployer).mint(user2.address, requiredPat);
        await mintTx2.wait();
        console.log("✅ 用户2 PAT铸造完成");
    }
    
    // 检查用户BNB余额
    const user1BnbBalance = await ethers.provider.getBalance(user1.address);
    const user2BnbBalance = await ethers.provider.getBalance(user2.address);
    console.log("💰 用户BNB余额:");
    console.log(`- 用户1: ${ethers.formatEther(user1BnbBalance)} BNB`);
    console.log(`- 用户2: ${ethers.formatEther(user2BnbBalance)} BNB`);
    
    // 如果用户BNB不足，从部署者转账
    const requiredBnb = ethers.parseEther("5"); // 每个用户需要5 BNB
    if (user1BnbBalance < requiredBnb) {
        console.log("💸 给用户1转账BNB...");
        const transferTx1 = await deployer.sendTransaction({
            to: user1.address,
            value: requiredBnb
        });
        await transferTx1.wait();
        console.log("✅ 用户1 BNB转账完成");
    }
    
    if (user2BnbBalance < requiredBnb) {
        console.log("💸 给用户2转账BNB...");
        const transferTx2 = await deployer.sendTransaction({
            to: user2.address,
            value: requiredBnb
        });
        await transferTx2.wait();
        console.log("✅ 用户2 BNB转账完成");
    }
    
    console.log("\n=== 3. 创建内容创作者角色 ===");
    
    // 检查用户1是否已有角色
    const user1CharacterId = await contentCharacter.ownerToCharacter(user1.address);
    if (user1CharacterId === 0n) {
        console.log("为用户1创建内容创作者角色...");
        const createCharacterTx = await contentCharacter.connect(user1).createCharacter(
            "BSCVideoEducator", // handle
            "专注于BSC链技术教育和视频内容创作", // bio
            "", // avatar (empty)
            "" // website (empty)
        );
        await createCharacterTx.wait();
        console.log("✅ 用户1角色创建完成");
    } else {
        console.log("✅ 用户1已有角色，ID:", user1CharacterId.toString());
    }
    
    console.log("\n=== 4. 授权PAT代币 ===");
    
    // 用户1授权PAT给内容注册合约
    const allowance1 = await patToken.allowance(user1.address, contentRegistry.target);
    if (allowance1 < ethers.parseEther("5")) {
        console.log("用户1授权PAT给内容注册合约...");
        const approveTx1 = await patToken.connect(user1).approve(contentRegistry.target, ethers.MaxUint256);
        await approveTx1.wait();
        console.log("✅ 用户1 PAT授权完成");
    }
    
    console.log("\n=== 5. 发布测试内容 ===");
    
    // 发布一个视频内容
    const videoMetadata = {
        name: `BSC链内容铸造教程 - ${Date.now()}`,
        description: "详细介绍BSC链内容铸造NFT功能的使用方法，包括90/10分成机制",
        ipfsHash: `QmBSCContentMintTutorialHash${Date.now()}`
    };
    
    console.log("📝 发布视频内容...");
    const registerTx = await contentRegistry.connect(user1).registerContent(
        "video", // contentType
        videoMetadata.name, // title
        videoMetadata.ipfsHash, // ipfsHash
        videoMetadata.ipfsHash, // metadataURI
        [deployer.address] // reviewers
    );
    const registerReceipt = await registerTx.wait();
    
    // 获取内容ID
    const registerEvent = registerReceipt.logs.find(log => {
        try {
            const parsed = contentRegistry.interface.parseLog(log);
            return parsed.name === "ContentRegistered";
        } catch {
            return false;
        }
    });
    
    const parsedEvent = contentRegistry.interface.parseLog(registerEvent);
    const contentId = parsedEvent.args.contentId;
    
    console.log("✅ 内容发布成功，ID:", contentId.toString());
    console.log("- 标题:", videoMetadata.name);
    console.log("- IPFS哈希:", videoMetadata.ipfsHash);
    
    console.log("\n=== 6. 测试内容铸造NFT（90/10分成） ===");
    
    // 获取铸造价格
    const mintPrice = await contentMint.getMintPrice(contentId);
    console.log(`📊 铸造价格: ${ethers.formatEther(mintPrice)} BNB`);
    
    // 记录铸造前的余额
    const beforeUser2BnbBalance = await ethers.provider.getBalance(user2.address);
    const beforeCreatorPatBalance = await patToken.balanceOf(user1.address);
    const beforeTreasuryBnbBalance = await ethers.provider.getBalance(coreDeployment.treasury);
    
    console.log("💰 铸造前余额:");
    console.log(`- 用户2 BNB: ${ethers.formatEther(beforeUser2BnbBalance)} BNB`);
    console.log(`- 创作者 PAT: ${ethers.formatEther(beforeCreatorPatBalance)} PAT`);
    console.log(`- 国库 BNB: ${ethers.formatEther(beforeTreasuryBnbBalance)} BNB`);
    
    // 用户2铸造NFT
    console.log("📝 用户2铸造内容NFT...");
    const mintTx = await contentMint.connect(user2).mintContent(contentId, {
        value: mintPrice // 支付BNB作为铸造费用
    });
    const mintReceipt = await mintTx.wait();
    
    // 获取NFT ID
    const mintEvent = mintReceipt.logs.find(log => {
        try {
            const parsed = contentMint.interface.parseLog(log);
            return parsed.name === "Transfer" && parsed.args.from === ethers.ZeroAddress;
        } catch {
            return false;
        }
    });
    
    let nftId;
    if (mintEvent) {
        const parsedMintEvent = contentMint.interface.parseLog(mintEvent);
        nftId = parsedMintEvent.args.tokenId;
        console.log("🎉 内容NFT铸造成功！");
        console.log("- NFT ID:", nftId.toString());
        console.log("- 所有者:", user2.address);
        console.log("- Gas使用:", mintReceipt.gasUsed.toString());
    }
    
    console.log("\n=== 7. 验证90/10分成机制 ===");
    
    // 记录铸造后的余额
    const afterUser2BnbBalance = await ethers.provider.getBalance(user2.address);
    const afterCreatorPatBalance = await patToken.balanceOf(user1.address);
    const afterTreasuryBnbBalance = await ethers.provider.getBalance(coreDeployment.treasury);
    
    console.log("💰 铸造后余额:");
    console.log(`- 用户2 BNB: ${ethers.formatEther(afterUser2BnbBalance)} BNB`);
    console.log(`- 创作者 PAT: ${ethers.formatEther(afterCreatorPatBalance)} PAT`);
    console.log(`- 国库 BNB: ${ethers.formatEther(afterTreasuryBnbBalance)} BNB`);
    
    // 计算收益分配
    const user2BnbUsed = beforeUser2BnbBalance - afterUser2BnbBalance;
    const creatorPatEarned = afterCreatorPatBalance - beforeCreatorPatBalance;
    const treasuryBnbEarned = afterTreasuryBnbBalance - beforeTreasuryBnbBalance;
    
    console.log("💸 收益分配验证:");
    console.log(`- 用户2支付: ${ethers.formatEther(user2BnbUsed)} BNB`);
    console.log(`- 创作者收益: ${ethers.formatEther(creatorPatEarned)} PAT`);
    console.log(`- 国库收益: ${ethers.formatEther(treasuryBnbEarned)} BNB`);
    
    // 验证分成比例（注意：这里的分成可能不是直接的90/10，因为涉及不同代币）
    const expectedCreatorShare = mintPrice * 90n / 100n; // 90%
    const expectedTreasuryShare = mintPrice * 10n / 100n; // 10%
    
    console.log("📊 预期分成:");
    console.log(`- 创作者应得: ${ethers.formatEther(expectedCreatorShare)} BNB (90%)`);
    console.log(`- 国库应得: ${ethers.formatEther(expectedTreasuryShare)} BNB (10%)`);
    
    console.log("\n=== 8. 验证NFT所有权和元数据 ===");
    
    if (nftId) {
        // 检查NFT所有权
        const nftOwner = await contentMint.ownerOf(nftId);
        console.log("🎨 NFT信息:");
        console.log("- NFT ID:", nftId.toString());
        console.log("- 所有者:", nftOwner);
        console.log("- 所有权正确:", nftOwner === user2.address ? "✅" : "❌");
        
        // 检查NFT元数据URI
        try {
            const tokenURI = await contentMint.tokenURI(nftId);
            console.log("- 元数据URI:", tokenURI);
        } catch (error) {
            console.log("- 元数据URI: 获取失败");
        }
    }
    
    console.log("\n=== 9. 测试结果汇总 ===");
    
    console.log("🎊 BSC链内容铸造NFT测试成功！");
    console.log("================================================");
    console.log("✅ 内容发布: 成功");
    console.log("✅ NFT铸造: 成功");
    console.log("✅ 收益分配: 已执行");
    console.log("✅ 所有权验证: 正确");
    
    console.log("\n📊 测试统计:");
    console.log("- 内容ID:", contentId.toString());
    console.log("- NFT ID:", nftId ? nftId.toString() : "N/A");
    console.log("- 铸造费用:", ethers.formatEther(mintPrice), "BNB");
    console.log("- Gas费用:", ethers.formatEther(mintReceipt.gasUsed * mintReceipt.gasPrice || 0n), "BNB");
    
    console.log("\n🔗 后续操作:");
    console.log("1. 验证PAT分配:");
    console.log("   npx hardhat run scripts/utils/verify-pat-allocation.js --network localhost");
    console.log("2. 查看内容统计:");
    console.log("   npx hardhat run scripts/query/content-stats.js --network localhost");
    
    // 保存测试报告
    const reportDir = path.join(__dirname, "../../test-reports");
    if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const testReport = {
        network: network.name,
        timestamp: new Date().toISOString(),
        contentId: contentId.toString(),
        nftId: nftId ? nftId.toString() : null,
        mintPrice: ethers.formatEther(mintPrice),
        gasUsed: mintReceipt.gasUsed.toString(),
        success: true
    };
    
    const reportFile = path.join(reportDir, `content-mint-test-${network.name}-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(testReport, null, 2));
    console.log(`📊 测试报告已保存: ${reportFile}`);
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("内容铸造测试失败:", error);
            process.exit(1);
        });
}

module.exports = main;
