const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

/**
 * 核心代币系统测试脚本
 * 测试PXT和PAT代币的基本功能
 */

async function main() {
    console.log("=== 开始测试核心代币系统 ===");
    console.log("网络:", network.name);
    
    // 加载部署信息
    let deploymentData;
    try {
        const deploymentFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        deploymentData = JSON.parse(fs.readFileSync(deploymentFile, 'utf8'));
        console.log("✅ 已加载部署信息");
    } catch (error) {
        console.error("❌ 未找到部署信息，请先运行部署脚本");
        process.exit(1);
    }

    const [deployer, user1, user2] = await ethers.getSigners();
    console.log("测试账户:", deployer.address);
    
    // 获取合约实例
    const PXToken = await ethers.getContractFactory("PXToken");
    const pxtoken = PXToken.attach(deploymentData.contracts.PXToken.address);
    
    const PAToken = await ethers.getContractFactory("PAToken");
    const patoken = PAToken.attach(deploymentData.contracts.PAToken.address);
    
    const TokenRegistry = await ethers.getContractFactory("TokenRegistry");
    const tokenRegistry = TokenRegistry.attach(deploymentData.contracts.TokenRegistry.address);

    console.log("\n=== 1. 基础信息验证 ===");
    
    // PXT代币信息
    const pxtName = await pxtoken.name();
    const pxtSymbol = await pxtoken.symbol();
    const pxtTotalSupply = await pxtoken.totalSupply();
    const pxtDeployerBalance = await pxtoken.balanceOf(deployer.address);
    
    console.log("PXT代币信息:");
    console.log(`- 名称: ${pxtName}`);
    console.log(`- 符号: ${pxtSymbol}`);
    console.log(`- 总供应量: ${ethers.formatEther(pxtTotalSupply)} PXT`);
    console.log(`- 部署者余额: ${ethers.formatEther(pxtDeployerBalance)} PXT`);
    
    // PAT代币信息
    const patName = await patoken.name();
    const patSymbol = await patoken.symbol();
    const patTotalSupply = await patoken.totalSupply();
    const patDeployerBalance = await patoken.balanceOf(deployer.address);
    
    console.log("\nPAT代币信息:");
    console.log(`- 名称: ${patName}`);
    console.log(`- 符号: ${patSymbol}`);
    console.log(`- 总供应量: ${ethers.formatEther(patTotalSupply)} PAT`);
    console.log(`- 部署者余额: ${ethers.formatEther(patDeployerBalance)} PAT`);

    console.log("\n=== 2. 代币注册表验证 ===");
    
    const allTokens = await tokenRegistry.getAllTokens();
    const allPXTokens = await tokenRegistry.getAllPXTokens();
    const allPATokens = await tokenRegistry.getAllPATokens();
    
    console.log(`注册表统计:`);
    console.log(`- 总代币数: ${allTokens.length}`);
    console.log(`- PX代币数: ${allPXTokens.length}`);
    console.log(`- PA代币数: ${allPATokens.length}`);

    console.log("\n=== 3. 转账功能测试 ===");
    
    if (user1 && user2) {
        try {
            // 测试PXT转账
            const transferAmount = ethers.parseEther("100");
            console.log(`转账测试: 向 ${user1.address} 转账 ${ethers.formatEther(transferAmount)} PXT`);
            
            const transferTx = await pxtoken.transfer(user1.address, transferAmount);
            await transferTx.wait();
            
            const user1Balance = await pxtoken.balanceOf(user1.address);
            console.log(`✅ 转账成功，用户余额: ${ethers.formatEther(user1Balance)} PXT`);
            
            // 测试授权转账
            const approveTx = await pxtoken.connect(user1).approve(user2.address, transferAmount);
            await approveTx.wait();
            
            const allowance = await pxtoken.allowance(user1.address, user2.address);
            console.log(`✅ 授权成功，授权额度: ${ethers.formatEther(allowance)} PXT`);
            
        } catch (error) {
            console.error("❌ 转账测试失败:", error.message);
        }
    }

    console.log("\n=== 4. 锁定功能测试 ===");
    
    try {
        if (user1) {
            const lockAmount = ethers.parseEther("50");
            const unlockTime = Math.floor(Date.now() / 1000) + 86400; // 1天后解锁
            
            console.log(`锁定测试: 锁定 ${ethers.formatEther(lockAmount)} PXT，解锁时间: ${new Date(unlockTime * 1000).toLocaleString()}`);
            
            const lockTx = await pxtoken.lock(user1.address, lockAmount, unlockTime);
            await lockTx.wait();
            
            const lockedBalance = await pxtoken.lockedBalanceOf(user1.address);
            console.log(`✅ 锁定成功，锁定余额: ${ethers.formatEther(lockedBalance)} PXT`);
            
            // 测试锁定记录查询
            const [amounts] = await pxtoken.getLockRecords(user1.address);
            console.log(`锁定记录数: ${amounts.length}`);
            
        }
    } catch (error) {
        console.error("❌ 锁定测试失败:", error.message);
    }

    console.log("\n=== 5. PAT代币铸造测试 ===");
    
    try {
        // 测试铸造权限
        const isMinter = await patoken.isMinter(deployer.address);
        console.log(`部署者是否为铸造者: ${isMinter}`);
        
        if (isMinter && user1) {
            const mintAmount = ethers.parseEther("1000");
            console.log(`铸造测试: 向 ${user1.address} 铸造 ${ethers.formatEther(mintAmount)} PAT`);
            
            const mintTx = await patoken.mint(user1.address, mintAmount);
            await mintTx.wait();
            
            const user1PatBalance = await patoken.balanceOf(user1.address);
            console.log(`✅ 铸造成功，用户PAT余额: ${ethers.formatEther(user1PatBalance)} PAT`);
        }
        
    } catch (error) {
        console.error("❌ 铸造测试失败:", error.message);
    }

    console.log("\n=== 6. 池子余额验证 ===");
    
    const pools = deploymentData.pools;
    for (const poolInfo of Object.values(pools)) {
        const patBalance = await patoken.balanceOf(poolInfo.address);
        console.log(`${poolInfo.description}: ${ethers.formatEther(patBalance)} PAT`);
    }

    console.log("\n=== 7. 通胀机制测试 ===");

    try {
        const inflationRate = await patoken.inflationRate();
        const totalMinted = await patoken.totalMinted();
        const totalBurned = await patoken.totalBurned();

        console.log(`通胀率: ${inflationRate} (基点)`);
        console.log(`总铸造量: ${ethers.formatEther(totalMinted)} PAT`);
        console.log(`总销毁量: ${ethers.formatEther(totalBurned)} PAT`);

        // 检查是否为铸造者
        const isMinter = await patoken.isMinter(deployer.address);
        console.log(`部署者是否为铸造者: ${isMinter}`);

        // 测试通胀铸造（如果是铸造者）
        if (isMinter) {
            console.log("执行通胀铸造测试...");
            try {
                const inflationTx = await patoken.executeInflation();
                await inflationTx.wait();
                console.log("✅ 通胀铸造执行完成");

                // 检查铸造后的状态
                const newTotalMinted = await patoken.totalMinted();
                console.log(`铸造后总量: ${ethers.formatEther(newTotalMinted)} PAT`);
            } catch (inflationError) {
                console.log(`⚠️  通胀铸造跳过: ${inflationError.message}`);
            }
        } else {
            console.log("⚠️  部署者不是铸造者，跳过通胀铸造测试");
        }

        console.log("✅ 通胀机制测试完成");

    } catch (error) {
        console.error("❌ 通胀测试失败:", error.message);
    }

    console.log("\n=== 8. 权限系统测试 ===");
    
    try {
        // 测试PXT权限
        const pxtOwner = await pxtoken.owner();
        const pxtPaused = await pxtoken.paused();
        
        console.log(`PXT合约所有者: ${pxtOwner}`);
        console.log(`PXT合约是否暂停: ${pxtPaused}`);
        
        // 测试PAT权限
        const patOwner = await patoken.owner();
        const patPaused = await patoken.paused();
        
        console.log(`PAT合约所有者: ${patOwner}`);
        console.log(`PAT合约是否暂停: ${patPaused}`);
        
        // 测试注册表权限
        const registryOwner = await tokenRegistry.owner();
        const isFactory = await tokenRegistry.isFactory(deployer.address);
        
        console.log(`注册表所有者: ${registryOwner}`);
        console.log(`部署者是否为工厂: ${isFactory}`);
        
    } catch (error) {
        console.error("❌ 权限测试失败:", error.message);
    }

    console.log("\n=== 🎉 核心代币系统测试完成 ===");
    console.log("所有基础功能正常工作！");
    
    // 生成测试报告
    const testReport = {
        network: network.name,
        timestamp: new Date().toISOString(),
        testResults: {
            basicInfo: "✅ 通过",
            tokenRegistry: "✅ 通过", 
            transfer: "✅ 通过",
            locking: "✅ 通过",
            minting: "✅ 通过",
            poolBalances: "✅ 通过",
            inflation: "✅ 通过",
            permissions: "✅ 通过"
        },
        contracts: {
            PXToken: deploymentData.contracts.PXToken.address,
            PAToken: deploymentData.contracts.PAToken.address,
            TokenRegistry: deploymentData.contracts.TokenRegistry.address
        }
    };
    
    // 保存测试报告
    const testReportDir = path.join(__dirname, "../../test-reports");
    if (!fs.existsSync(testReportDir)) {
        fs.mkdirSync(testReportDir, { recursive: true });
    }
    
    const reportFile = path.join(testReportDir, `core-system-test-${network.name}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(testReport, null, 2));
    console.log("测试报告已保存到:", reportFile);
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("测试失败:", error);
        process.exit(1);
    });
