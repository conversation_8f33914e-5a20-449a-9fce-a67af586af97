const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🌉 BSC跨链桥基础功能测试");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("时间:", new Date().toISOString());
    
    const [deployer] = await ethers.getSigners();
    console.log("测试账户:", deployer.address);
    
    // 加载部署信息
    let coreDeployment, bridgeDeployment;
    try {
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const bridgeFile = path.join(__dirname, "../../deployments", network.name, "bridge-deployment.json");
        
        coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        bridgeDeployment = JSON.parse(fs.readFileSync(bridgeFile, 'utf8'));
        console.log("✅ 已加载部署信息");
    } catch (error) {
        console.error("❌ 未找到部署文件，请先部署跨链桥:");
        console.error("npx hardhat run scripts/deploy/05-deploy-token-bridge.js --network localhost");
        process.exit(1);
    }
    
    // 获取合约实例
    const PAToken = await ethers.getContractFactory("PAToken");
    const patToken = PAToken.attach(coreDeployment.contracts.PAToken.address);
    
    const TokenBridge = await ethers.getContractFactory("TokenBridge");
    const tokenBridge = TokenBridge.attach(bridgeDeployment.contracts.TokenBridge.address);
    
    console.log("\n=== 1. 跨链桥基础信息验证 ===");

    // 检查跨链桥配置
    const bridgeOwner = await tokenBridge.owner();
    const isOwner = bridgeOwner === deployer.address;
    console.log("📊 跨链桥状态:");
    console.log(`- 合约地址: ${bridgeDeployment.contracts.TokenBridge.address}`);
    console.log(`- 合约所有者: ${bridgeOwner}`);
    console.log(`- 部署者是所有者: ${isOwner}`);

    // 检查支持的链
    try {
        const supportedChainsInfo = await tokenBridge.getSupportedChains();
        console.log(`- 支持的链数量: ${supportedChainsInfo.chainIds.length}`);
        console.log(`- 支持的链ID: [${supportedChainsInfo.chainIds.map(id => id.toString()).join(', ')}]`);
        console.log(`- 链名称: [${supportedChainsInfo.chainNames ? supportedChainsInfo.chainNames.join(', ') : '未获取到'}]`);

        const pxaChainSupported = supportedChainsInfo.chainIds.includes(327n);
        console.log(`- PXA链(327)支持: ${pxaChainSupported}`);
    } catch (error) {
        console.log("⚠️  无法获取支持的链信息:", error.message);
    }
    
    console.log("\n=== 2. PAT代币跨链支持验证 ===");

    // 检查PAT代币配置
    const patTokenAddress = await tokenBridge.patoken();
    const pxtTokenAddress = await tokenBridge.pxtoken();
    const patTokenMatch = patTokenAddress === coreDeployment.contracts.PAToken.address;
    const pxtTokenMatch = pxtTokenAddress === coreDeployment.contracts.PXToken.address;

    console.log("📊 代币配置:");
    console.log(`- 桥接合约中PAT地址: ${patTokenAddress}`);
    console.log(`- 实际PAT地址: ${coreDeployment.contracts.PAToken.address}`);
    console.log(`- PAT地址匹配: ${patTokenMatch}`);
    console.log(`- 桥接合约中PXT地址: ${pxtTokenAddress}`);
    console.log(`- 实际PXT地址: ${coreDeployment.contracts.PXToken.address}`);
    console.log(`- PXT地址匹配: ${pxtTokenMatch}`);
    
    console.log("\n=== 3. 跨链费用配置验证 ===");

    // 测试跨链费用计算
    const testAmount = ethers.parseEther("1000"); // 1000 PAT
    try {
        const feeConfig = await tokenBridge.getFeeConfig(327);
        console.log("📊 PXA链费用配置:");
        console.log(`- 基础费用: ${ethers.formatEther(feeConfig.baseFee)} BNB`);
        console.log(`- 百分比费用: ${Number(feeConfig.feePercentage) / 100}%`);
        console.log(`- 最小费用: ${ethers.formatEther(feeConfig.minFee)} BNB`);
        console.log(`- 最大费用: ${ethers.formatEther(feeConfig.maxFee)} BNB`);

        // 计算实际费用
        const baseFee = feeConfig.baseFee;
        const percentageFee = (testAmount * BigInt(feeConfig.feePercentage)) / 10000n; // 百分比费用
        let totalFee = baseFee + percentageFee;

        // 应用最小和最大费用限制
        if (totalFee < feeConfig.minFee) totalFee = feeConfig.minFee;
        if (totalFee > feeConfig.maxFee) totalFee = feeConfig.maxFee;

        console.log("📊 跨链费用测试:");
        console.log(`- 测试金额: ${ethers.formatEther(testAmount)} PAT`);
        console.log(`- 计算费用: ${ethers.formatEther(totalFee)} BNB`);

    } catch (error) {
        console.error("❌ 费用配置获取失败:", error.message);
    }
    
    console.log("\n=== 4. 跨链池状态检查 ===");
    
    // 检查跨链池
    const crossChainPoolAddress = coreDeployment.pools.crossChainPool.address;
    const crossChainPoolBalance = await patToken.balanceOf(crossChainPoolAddress);
    const crossChainPoolBnbBalance = await ethers.provider.getBalance(crossChainPoolAddress);
    
    console.log("📊 跨链池状态:");
    console.log(`- 池子地址: ${crossChainPoolAddress}`);
    console.log(`- PAT余额: ${ethers.formatEther(crossChainPoolBalance)} PAT`);
    console.log(`- BNB余额: ${ethers.formatEther(crossChainPoolBnbBalance)} BNB`);
    
    console.log("\n=== 5. 验证者配置检查 ===");

    try {
        // 获取PXA链验证者列表
        const pxaValidators = await tokenBridge.getChainValidators(327);
        console.log("📊 PXA链验证者:");
        console.log(`- 验证者总数: ${pxaValidators.length}`);

        for (let i = 0; i < Math.min(pxaValidators.length, 5); i++) {
            console.log(`- 验证者${i + 1}: ${pxaValidators[i]}`);
        }

        if (pxaValidators.length > 5) {
            console.log(`- ... 还有 ${pxaValidators.length - 5} 个验证者`);
        }
    } catch (error) {
        console.log("⚠️  无法获取验证者信息:", error.message);
    }
    
    console.log("\n=== 6. 跨链桥权限检查 ===");

    // 检查权限状态（isOwner已在前面定义）

    // 检查是否为管理员
    let isAdmin = false;
    try {
        isAdmin = await tokenBridge.administrators(deployer.address);
    } catch (error) {
        console.log("⚠️  无法检查管理员状态:", error.message);
    }

    // 检查是否为验证者
    let isValidator = false;
    try {
        isValidator = await tokenBridge.bridgeValidators(deployer.address);
    } catch (error) {
        console.log("⚠️  无法检查验证者状态:", error.message);
    }

    console.log("📊 权限状态:");
    console.log(`- 合约所有者: ${isOwner}`);
    console.log(`- 管理员权限: ${isAdmin}`);
    console.log(`- 验证者权限: ${isValidator}`);
    
    console.log("\n=== 7. 跨链桥暂停状态检查 ===");

    let isPaused = false;
    try {
        isPaused = await tokenBridge.paused();
        console.log("📊 合约状态:");
        console.log(`- 是否暂停: ${isPaused}`);

        if (isPaused) {
            console.log("⚠️  跨链桥当前处于暂停状态");
        } else {
            console.log("✅ 跨链桥正常运行中");
        }
    } catch (error) {
        console.log("⚠️  无法检查暂停状态:", error.message);
    }
    
    console.log("\n=== 8. 测试结果汇总 ===");
    
    const testResults = {
        network: network.name,
        timestamp: new Date().toISOString(),
        bridgeContract: bridgeDeployment.contracts.TokenBridge.address,
        patToken: coreDeployment.contracts.PAToken.address,
        crossChainPool: crossChainPoolAddress,
        tests: {
            bridgeDeployed: !!bridgeDeployment.contracts.TokenBridge.address,
            patTokenConfigured: patTokenMatch,
            pxtTokenConfigured: pxtTokenMatch,
            ownerPermissions: isOwner,
            poolFunded: crossChainPoolBalance > 0,
            bridgeActive: !isPaused
        },
        balances: {
            crossChainPoolPAT: ethers.formatEther(crossChainPoolBalance),
            crossChainPoolBNB: ethers.formatEther(crossChainPoolBnbBalance)
        }
    };
    
    console.log("🎉 跨链桥基础功能测试完成！");
    console.log("================================================");
    
    const allTestsPassed = Object.values(testResults.tests).every(test => test === true);
    
    if (allTestsPassed) {
        console.log("✅ 所有基础功能测试通过");
        console.log("🚀 跨链桥已准备就绪，可以执行跨链操作");
    } else {
        console.log("⚠️  部分测试未通过，请检查配置");
        console.log("❌ 失败的测试:");
        Object.entries(testResults.tests).forEach(([test, passed]) => {
            if (!passed) {
                console.log(`   - ${test}: ${passed}`);
            }
        });
    }
    
    console.log("\n🔗 下一步操作:");
    console.log("1. 检查跨链池状态:");
    console.log("   npx hardhat run scripts/debug/check-cross-chain-pool-balance.js --network localhost");
    console.log("2. 执行跨链操作:");
    console.log("   npx hardhat run scripts/bridge/bridge-from-cross-chain-pool.js --network localhost");
    
    // 保存测试报告
    const reportDir = path.join(__dirname, "../../test-reports");
    if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const reportFile = path.join(reportDir, `bridge-test-${network.name}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(testResults, null, 2));
    console.log(`📊 测试报告已保存: ${reportFile}`);
    
    return testResults;
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("测试失败:", error);
            process.exit(1);
        });
}

module.exports = main;
