const { ethers, network } = require("hardhat");
const hre = require("hardhat");
const fs = require("fs");
const path = require("path");
require('dotenv').config();

/**
 * BSC链IPFS内容上链测试
 * 使用PAT代币支付，BNB作为Gas费用
 */

async function main() {
    console.log("🌐 开始BSC链IPFS内容上链测试");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("时间:", new Date().toISOString());
    
    const signers = await ethers.getSigners();
    const deployer = signers[0];
    const user1 = signers[1] || deployer; // 如果没有第二个账户，使用部署者

    console.log("测试账户:");
    console.log("- 部署者:", deployer.address);
    console.log("- 用户1:", user1.address);
    
    // 加载部署信息
    let deploymentInfo = { contracts: {} };
    try {
        const deploymentDir = path.join(__dirname, "../../deployments", network.name);
        
        // 读取核心系统部署信息
        const coreFile = path.join(deploymentDir, "core-deployment.json");
        const coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        
        // 读取内容系统部署信息
        const contentFile = path.join(deploymentDir, "content-deployment.json");
        const contentDeployment = JSON.parse(fs.readFileSync(contentFile, 'utf8'));
        
        deploymentInfo.contracts.PAToken = coreDeployment.contracts.PAToken.address;
        deploymentInfo.contracts.ContentRegistry = contentDeployment.contracts.ContentRegistry.address;
        deploymentInfo.contracts.ContentCharacter = contentDeployment.contracts.ContentCharacter.address;
        deploymentInfo.contracts.ContentMint = contentDeployment.contracts.ContentMint.address;
        
        console.log("✅ 已加载部署信息");
        console.log("- PAT代币:", deploymentInfo.contracts.PAToken);
        console.log("- 内容注册器:", deploymentInfo.contracts.ContentRegistry);
        console.log("- 内容角色:", deploymentInfo.contracts.ContentCharacter);
        console.log("- 内容铸造:", deploymentInfo.contracts.ContentMint);
    } catch (error) {
        console.error("❌ 未找到部署信息，请先部署内容系统:");
        console.error("npx hardhat run scripts/deploy/04-deploy-content-system.js --network localhost");
        process.exit(1);
    }
    
    // 获取合约实例
    const PAToken = await ethers.getContractFactory("PAToken");
    const patToken = PAToken.attach(deploymentInfo.contracts.PAToken);
    
    const ContentRegistry = await ethers.getContractFactory("ContentRegistry");
    const contentRegistry = ContentRegistry.attach(deploymentInfo.contracts.ContentRegistry);
    
    const ContentCharacter = await ethers.getContractFactory("ContentCharacter");
    const contentCharacter = ContentCharacter.attach(deploymentInfo.contracts.ContentCharacter);
    
    const ContentMint = await ethers.getContractFactory("ContentMint");
    const contentMint = ContentMint.attach(deploymentInfo.contracts.ContentMint);
    
    console.log("\n=== 1. 准备测试环境 ===");
    
    // 检查用户余额
    const user1PatBalance = await patToken.balanceOf(user1.address);
    console.log("用户1 PAT余额:", ethers.formatEther(user1PatBalance), "PAT");

    // 确保用户有足够的PAT
    if (user1PatBalance < ethers.parseEther("5")) {
        console.log("给用户1铸造PAT代币...");

        // 检查部署者是否为铸造者
        const isMinter = await patToken.isMinter(deployer.address);
        if (isMinter) {
            const mintAmount = ethers.parseEther("100");
            const mintTx = await patToken.connect(deployer).mint(user1.address, mintAmount);
            await mintTx.wait();
            console.log("✅ PAT铸造完成");
        } else {
            console.error("❌ 部署者不是PAT铸造者");
            console.log("💡 解决方案: 请使用有铸造权限的账户");
            process.exit(1);
        }
    }
    
    // 无上限授权
    console.log("执行无上限PAT授权...");
    const approveTx = await patToken.connect(user1).approve(contentRegistry.getAddress(), ethers.MaxUint256);
    await approveTx.wait();
    console.log("✅ 无上限授权完成");
    
    // 检查Character
    const existingCharacterId = await contentCharacter.ownerToCharacter(user1.address);
    if (existingCharacterId === 0n) {
        console.log("创建内容创作者角色...");
        const createCharacterTx = await contentCharacter.connect(user1).createCharacter(
            "bsc_educator", // handle - 用户句柄
            "专注于BSC链技术教育和PXPAC生态内容创作", // bio - 个人简介
            "https://gateway.pinata.cloud/ipfs/QmPXPACEducatorAvatar", // avatar - 头像URL
            "https://pxpac.education" // website - 网站URL
        );
        await createCharacterTx.wait();
        console.log("✅ 内容创作者角色创建完成");
    } else {
        console.log("✅ 用户已有内容创作者角色，ID:", existingCharacterId.toString());
    }
    
    console.log("\n=== 2. 准备IPFS内容 ===");
    
    // 创建视频教程元数据（BSC链专用）
    const timestamp = Date.now();
    const videoMetadata = {
        name: `PXPAC生态BSC智能合约开发教程 - ${timestamp}`,
        description: `全面介绍PXPAC生态在BSC链上的智能合约开发，包括双代币系统、质押治理、内容上链等核心功能。创建时间：${new Date().toISOString()}`,
        image: "https://gateway.pinata.cloud/ipfs/QmPXPACBSCLogo123456789",
        animation_url: "", // 将在上传后填入
        timestamp: timestamp, // 添加时间戳确保内容唯一性
        
        // 扩展属性
        attributes: [
            { trait_type: "内容类型", value: "教育视频" },
            { trait_type: "难度等级", value: "高级" },
            { trait_type: "时长", value: "60分钟" },
            { trait_type: "语言", value: "中文" },
            { trait_type: "区块链", value: "BSC" },
            { trait_type: "生态系统", value: "PXPAC" }
        ],
        
        // 教程信息
        category: "区块链教育",
        tags: ["BSC", "PXPAC", "智能合约", "DeFi", "治理", "质押"],
        duration: "60:00",
        level: "advanced",
        
        // 章节信息
        chapters: [
            "第1章: PXPAC生态介绍",
            "第2章: 双代币经济模型(PXT+PAT)", 
            "第3章: 质押治理系统",
            "第4章: 内容上链机制",
            "第5章: 跨链桥接技术",
            "第6章: DApp前端集成"
        ],
        
        // 版权信息
        license: "Creative Commons BY-SA",
        copyright: "© 2024 PXPAC教育工作室"
    };
    
    try {
        // 真实IPFS上传到Pinata
        console.log("📤 开始真实IPFS上传到Pinata...");

        // 使用真实Pinata配置
        const pinataJWT = process.env.PINATA_JWT || process.env.IPFS_JWT_TOKEN;
        const ipfsApiKey = process.env.IPFS_API_KEY;
        const ipfsApiSecret = process.env.IPFS_API_SECRET;
        const ipfsGatewayUrl = process.env.IPFS_GATEWAY_URL || "https://gateway.pinata.cloud";

        if (!pinataJWT && (!ipfsApiKey || !ipfsApiSecret)) {
            throw new Error("❌ 未找到Pinata凭据！请设置 PINATA_JWT 或 IPFS_API_KEY + IPFS_API_SECRET 环境变量");
        }

        console.log("✅ Pinata配置检查通过");
        console.log(`- 使用网关: ${ipfsGatewayUrl}`);
        console.log(`- 认证方式: ${pinataJWT ? 'JWT Token' : 'API Key + Secret'}`);

        // 上传元数据到Pinata
        console.log("📤 上传元数据到Pinata IPFS...");

        let uploadResponse;

        if (pinataJWT) {
            // 使用JWT Token方式
            uploadResponse = await fetch('https://api.pinata.cloud/pinning/pinJSONToIPFS', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${pinataJWT}`
                },
                body: JSON.stringify({
                    pinataContent: videoMetadata,
                    pinataMetadata: {
                        name: `PXPAC-BSC-Tutorial-${Date.now()}`,
                        keyvalues: {
                            chain: "BSC",
                            ecosystem: "PXPAC",
                            type: "video-tutorial",
                            timestamp: new Date().toISOString(),
                            creator: user1.address
                        }
                    },
                    pinataOptions: {
                        cidVersion: 1,
                        wrapWithDirectory: false
                    }
                })
            });
        } else {
            // 使用API Key + Secret方式
            uploadResponse = await fetch('https://api.pinata.cloud/pinning/pinJSONToIPFS', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'pinata_api_key': ipfsApiKey,
                    'pinata_secret_api_key': ipfsApiSecret
                },
                body: JSON.stringify({
                    pinataContent: videoMetadata,
                    pinataMetadata: {
                        name: `PXPAC-BSC-Tutorial-${Date.now()}`,
                        keyvalues: {
                            chain: "BSC",
                            ecosystem: "PXPAC",
                            type: "video-tutorial",
                            timestamp: new Date().toISOString(),
                            creator: user1.address
                        }
                    },
                    pinataOptions: {
                        cidVersion: 1,
                        wrapWithDirectory: false
                    }
                })
            });
        }

        if (!uploadResponse.ok) {
            const errorText = await uploadResponse.text();
            throw new Error(`Pinata上传失败: ${uploadResponse.status} - ${errorText}`);
        }

        const uploadResult = await uploadResponse.json();
        const videoMetadataHash = uploadResult.IpfsHash;

        console.log("🎉 真实IPFS上传成功!");
        console.log("- IPFS哈希:", videoMetadataHash);
        console.log("- Pinata URL:", `${ipfsGatewayUrl}/ipfs/${videoMetadataHash}`);
        console.log("- 文件大小:", uploadResult.PinSize ? `${uploadResult.PinSize} bytes` : "未知");
        console.log("- 上传时间:", uploadResult.Timestamp || new Date().toISOString());

        // 更新元数据中的animation_url
        videoMetadata.animation_url = `https://gateway.pinata.cloud/ipfs/${videoMetadataHash}`;
        
        console.log("\n=== 3. 注册内容到BSC链 ===");

        // 检查video类型是否激活
        const activeTypes = await contentRegistry.getActiveContentTypes();
        console.log("激活的内容类型:", activeTypes);

        if (!activeTypes.includes("video")) {
            console.log("❌ video类型未激活！");
            throw new Error("video类型未激活");
        }

        // 检查video类型费用
        const videoFee = await contentRegistry.getContentFee("video");
        console.log("video内容费用:", ethers.formatEther(videoFee), "PAT");
        
        // 检查用户PAT余额和授权
        console.log("🔍 检查注册前状态...");
        const userPatBalance = await patToken.balanceOf(user1.address);
        const allowance = await patToken.allowance(user1.address, await contentRegistry.getAddress());
        console.log(`- 用户PAT余额: ${ethers.formatEther(userPatBalance)} PAT`);
        console.log(`- 授权额度: ${ethers.formatEther(allowance)} PAT`);
        console.log(`- 需要费用: ${ethers.formatEther(videoFee)} PAT`);

        if (userPatBalance < videoFee) {
            throw new Error("用户PAT余额不足");
        }

        if (allowance < videoFee) {
            throw new Error("授权额度不足");
        }

        // 注册内容
        console.log("📝 注册内容到BSC链...");
        let registerTx;
        try {
            registerTx = await contentRegistry.connect(user1).registerContent(
                "video", // contentType
                videoMetadata.name, // title
                videoMetadataHash, // ipfsHash
                videoMetadataHash, // metadataURI
                [deployer.address] // reviewers - 需要至少一个审核者地址
            );
        } catch (error) {
            console.error("❌ 注册内容失败，详细错误:", error);

            // 尝试调用静态方法来获取更详细的错误信息
            try {
                await contentRegistry.connect(user1).registerContent.staticCall(
                    "video",
                    videoMetadata.name,
                    videoMetadataHash,
                    videoMetadataHash,
                    [deployer.address]
                );
            } catch (staticError) {
                console.error("❌ 静态调用错误:", staticError.message);
            }

            throw error;
        }
        
        console.log("⏳ 等待交易确认...");
        const receipt = await registerTx.wait();
        
        // 获取内容ID
        const event = receipt.logs.find(log => {
            try {
                const parsed = contentRegistry.interface.parseLog(log);
                return parsed.name === "ContentRegistered";
            } catch {
                return false;
            }
        });
        
        if (!event) {
            throw new Error("未找到ContentRegistered事件");
        }
        
        const parsedEvent = contentRegistry.interface.parseLog(event);
        const contentId = parsedEvent.args.contentId;
        
        console.log("🎉 内容注册成功！");
        console.log("- 交易哈希:", registerTx.hash);
        console.log("- 内容ID:", contentId.toString());
        console.log("- Gas使用:", receipt.gasUsed.toString());
        console.log("- Gas费用:", ethers.formatEther(receipt.gasUsed * receipt.gasPrice || 0n), "BNB");
        
        console.log("\n=== 4. 验证链上内容 ===");
        
        // 获取链上内容信息
        const onChainContent = await contentRegistry.getContent(contentId);
        console.log("✅ 链上内容验证:");
        console.log("- 标题:", onChainContent.title);
        console.log("- 类型:", onChainContent.contentType);
        console.log("- 创建者:", onChainContent.creator);
        console.log("- IPFS哈希:", onChainContent.ipfsHash);
        console.log("- 元数据URI:", onChainContent.metadataURI);
        console.log("- 创建时间:", new Date(Number(onChainContent.timestamp) * 1000).toLocaleString());

        console.log("\n=== 5. 检查用户余额变化 ===");

        const finalPatBalance = await patToken.balanceOf(user1.address);
        const patUsed = user1PatBalance - finalPatBalance;
        console.log("用户1最终PAT余额:", ethers.formatEther(finalPatBalance), "PAT");
        console.log("本次消耗PAT:", ethers.formatEther(patUsed), "PAT");
        
        console.log("\n=== 6. 测试内容铸造NFT ===");

        // 获取铸造价格
        const mintPrice = await contentMint.getMintPrice(contentId);
        console.log(`📊 铸造价格: ${ethers.formatEther(mintPrice)} BNB`);

        // 检查用户BNB余额
        const user1BnbBalance = await ethers.provider.getBalance(user1.address);
        console.log(`💰 用户1 BNB余额: ${ethers.formatEther(user1BnbBalance)} BNB`);

        // 如果用户BNB不足，部署者转账5 BNB
        const requiredBnb = mintPrice + ethers.parseEther("0.01"); // 铸造费用 + 预留Gas费用
        if (user1BnbBalance < requiredBnb) {
            console.log("💸 用户BNB余额不足，部署者转账5 BNB...");

            const deployerBnbBalance = await ethers.provider.getBalance(deployer.address);
            console.log(`💰 部署者 BNB余额: ${ethers.formatEther(deployerBnbBalance)} BNB`);

            if (deployerBnbBalance >= ethers.parseEther("5.1")) { // 5 BNB + Gas费用
                const transferAmount = ethers.parseEther("5");
                const transferTx = await deployer.sendTransaction({
                    to: user1.address,
                    value: transferAmount
                });
                await transferTx.wait();

                const newUser1Balance = await ethers.provider.getBalance(user1.address);
                console.log("✅ BNB转账完成");
                console.log(`💰 用户1新余额: ${ethers.formatEther(newUser1Balance)} BNB`);
            } else {
                console.error("❌ 部署者BNB余额不足，无法转账");
                throw new Error("部署者BNB余额不足");
            }
        }

        // 检查用户是否已经铸造过
        const hasUserMintedBefore = await contentMint.hasUserMinted(user1.address, contentId);
        console.log(`📊 用户是否已铸造: ${hasUserMintedBefore}`);

        if (!hasUserMintedBefore) {
            console.log("📝 铸造内容NFT...");
            const mintTx = await contentMint.connect(user1).mintContent(contentId, {
                value: mintPrice // 支付BNB作为铸造费用
            });
            const mintReceipt = await mintTx.wait();

            // 获取NFT ID
            const mintEvent = mintReceipt.logs.find(log => {
                try {
                    const parsed = contentMint.interface.parseLog(log);
                    return parsed.name === "Transfer" && parsed.args.from === ethers.ZeroAddress;
                } catch {
                    return false;
                }
            });

            if (mintEvent) {
                const parsedMintEvent = contentMint.interface.parseLog(mintEvent);
                const nftId = parsedMintEvent.args.tokenId;
                console.log("🎉 内容NFT铸造成功！");
                console.log("- NFT ID:", nftId.toString());
                console.log("- 所有者:", user1.address);
                console.log("- 铸造费用:", ethers.formatEther(mintPrice), "BNB");
                console.log("- Gas使用:", mintReceipt.gasUsed.toString());

                // 检查最终余额
                const finalUser1BnbBalance = await ethers.provider.getBalance(user1.address);
                console.log(`💰 用户1最终BNB余额: ${ethers.formatEther(finalUser1BnbBalance)} BNB`);
            }
        } else {
            console.log("⚠️  用户已经铸造过此内容的NFT");
        }
        
        console.log("\n=== 7. 测试结果汇总 ===");

        const testResults = {
            success: true,
            network: network.name,
            contentId: contentId.toString(),
            title: videoMetadata.name,
            ipfsHash: videoMetadataHash,
            patFee: ethers.formatEther(videoFee),
            gasUsed: receipt.gasUsed.toString(),
            transactionHash: registerTx.hash,
            creator: user1.address,
            nftMinted: !hasUserMintedBefore // 如果之前没有铸造过，说明这次铸造了
        };
        
        console.log("🎊 BSC链IPFS上链测试成功！");
        console.log("================================================");
        console.log("✅ 内容已成功注册到BSC链");
        console.log("✅ 使用PAT代币支付费用");
        console.log("✅ 使用BNB支付Gas费用");
        console.log("✅ 内容NFT铸造成功");
        console.log("✅ PXPAC生态完整功能验证");
        
        console.log("\n🚀 下一步操作:");
        console.log("1. 查看内容统计:");
        console.log(`   npx hardhat run scripts/query/content-stats.js --network ${hre.network.name}`);
        console.log("2. 测试治理投票:");
        console.log(`   npx hardhat run scripts/test/governance-system-test.js --network ${hre.network.name}`);
        console.log("3. 测试质押奖励:");
        console.log(`   npx hardhat run scripts/test/staking-system-test.js --network ${hre.network.name}`);

        console.log("\n📊 生态系统对比:");
        console.log("- BSC链: PAT代币 + BNB Gas费用（成熟生态）");
        console.log("- PXA链: PXA-T代币 + PXA Gas费用（原生生态）");
        console.log("- 跨链桥: 支持资产在两链间自由流动");

        console.log("\n🔗 内容访问:");
        if (pinataJWT) {
            console.log(`- IPFS: https://gateway.pinata.cloud/ipfs/${videoMetadataHash}`);
        } else {
            console.log(`- 模拟IPFS: ${videoMetadataHash}`);
        }
        console.log(`- 内容ID: ${contentId.toString()}`);
        console.log(`- BSC浏览器: https://bscscan.com/tx/${registerTx.hash}`);
        
        return testResults;
        
    } catch (error) {
        console.error("❌ BSC链IPFS上链测试失败:", error.message);
        throw error;
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("测试失败:", error);
            process.exit(1);
        });
}

module.exports = main;
