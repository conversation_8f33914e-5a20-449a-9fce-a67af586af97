const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("=== 开始部署核心代币系统 ===");
    console.log("网络:", network.name);
    console.log("链ID:", network.config.chainId);

    // 网络验证
    const supportedNetworks = ['localhost', 'hardhat', 'bsc', 'bscTestnet', 'polygon', 'mumbai'];
    if (!supportedNetworks.includes(network.name)) {
        console.warn("⚠️  警告：当前网络可能不受支持:", network.name);
    }

    const signers = await ethers.getSigners();
    const deployer = signers[0];
    const treasury = signers[1] || deployer;
    const operator = signers[2] || deployer;

    console.log("部署账户:", deployer.address);
    console.log("国库账户:", treasury.address);
    console.log("操作员账户:", operator.address);

    // 余额检查
    const deployerBalance = await deployer.provider.getBalance(deployer.address);
    console.log("部署账户余额:", ethers.formatEther(deployerBalance), "ETH");

    const minRequiredBalance = ethers.parseEther("0.1"); // 最少需要0.1 ETH
    if (deployerBalance < minRequiredBalance) {
        throw new Error(`部署账户余额不足，至少需要 ${ethers.formatEther(minRequiredBalance)} ETH`);
    }

    // 部署配置参数
    const INITIAL_PXT_SUPPLY = ethers.parseEther("100000000"); // 1亿 PXT

    // 🔒 安全优化：使用确定性地址生成池子账户（基于部署者地址）
    const generatePoolAddress = (deployer, salt) => {
        const hash = ethers.keccak256(ethers.solidityPacked(['address', 'string'], [deployer, salt]));
        return ethers.getAddress('0x' + hash.slice(26)); // 取后20字节作为地址
    };

    // 生成池子地址（大部分池子只接收代币，不需要私钥）
    const chinaMainlandPoolAddress = generatePoolAddress(deployer.address, "china_mainland_pool");
    const globalPoolAddress = generatePoolAddress(deployer.address, "global_pool");
    const stakingPoolAddress = generatePoolAddress(deployer.address, "staking_pool");

    // 🔑 跨链池需要私钥来执行跨链操作，所以生成真实的钱包
    const crossChainPoolWallet = ethers.Wallet.createRandom();
    const crossChainPoolAddress = crossChainPoolWallet.address;
    const crossChainPoolPrivateKey = crossChainPoolWallet.privateKey;

    console.log("\n=== 生成池子地址 ===");
    console.log("中国大陆池子地址:", chinaMainlandPoolAddress);
    console.log("国际池子地址:", globalPoolAddress);
    console.log("质押池地址:", stakingPoolAddress);
    console.log("跨链池地址:", crossChainPoolAddress);
    console.log("🔑 跨链池私钥:", crossChainPoolPrivateKey);
    console.log("   [警告] 跨链池私钥用于自动跨链操作，请妥善保管!");

    // 存储部署结果
    const deployedContracts = {};

    console.log("\n=== 1. 部署 PXT 治理代币 ===");
    try {
        const PXToken = await ethers.getContractFactory("PXToken");

        // Gas估算
        const pxtDeployTx = await PXToken.getDeployTransaction(
            "Paper x Token",
            "PXT",
            INITIAL_PXT_SUPPLY,
            deployer.address,      // 社区挖矿与激励
            operator.address,      // 团队持有
            treasury.address,      // 平台生态建设
            deployer.address,      // 私募投资者
            deployer.address,      // 战略合作伙伴
            deployer.address,      // 市场营销
            deployer.address,      // 安全储备
            chinaMainlandPoolAddress  // 中国大陆池子
        );
        const estimatedGas = await deployer.estimateGas(pxtDeployTx);
        console.log("预估Gas消耗:", estimatedGas.toString());

        const pxtoken = await PXToken.deploy(
            "Paper x Token",
            "PXT",
            INITIAL_PXT_SUPPLY,
            deployer.address,      // 社区挖矿与激励
            operator.address,      // 团队持有
            treasury.address,      // 平台生态建设
            deployer.address,      // 私募投资者
            deployer.address,      // 战略合作伙伴
            deployer.address,      // 市场营销
            deployer.address,      // 安全储备
            chinaMainlandPoolAddress  // 中国大陆池子
        );
        await pxtoken.waitForDeployment();
        deployedContracts.PXToken = await pxtoken.getAddress();
        console.log("✅ PXT代币部署成功:", deployedContracts.PXToken);

        // 验证部署
        const pxtName = await pxtoken.name();
        const pxtSymbol = await pxtoken.symbol();
        const pxtTotalSupply = await pxtoken.totalSupply();
        console.log(`- 名称: ${pxtName}, 符号: ${pxtSymbol}, 总供应量: ${ethers.formatEther(pxtTotalSupply)}`);

    } catch (error) {
        console.error("❌ PXT代币部署失败:", error.message);
        throw error;
    }

    console.log("\n=== 2. 部署 PAT 功能代币 ===");
    try {
        const PAToken = await ethers.getContractFactory("PAToken");

        const patoken = await PAToken.deploy(
            chinaMainlandPoolAddress,  // 中国大陆池：1亿PAT
            globalPoolAddress,         // 国际池：1亿PAT
            stakingPoolAddress,        // 质押池：0PAT（通过PXT质押增发）
            crossChainPoolAddress,     // 跨链池：1亿PAT
            deployer.address           // 铸币者
        );
        await patoken.waitForDeployment();
        deployedContracts.PAToken = await patoken.getAddress();
        console.log("✅ PAT代币部署成功:", deployedContracts.PAToken);

        // 验证部署
        const patName = await patoken.name();
        const patSymbol = await patoken.symbol();
        const patTotalSupply = await patoken.totalSupply();
        console.log(`- 名称: ${patName}, 符号: ${patSymbol}, 总供应量: ${ethers.formatEther(patTotalSupply)}`);

    } catch (error) {
        console.error("❌ PAT代币部署失败:", error.message);
        throw error;
    }

    console.log("\n=== 3. 部署代币注册表 ===");
    try {
        const TokenRegistry = await ethers.getContractFactory("TokenRegistry");
        const tokenRegistry = await TokenRegistry.deploy();
        await tokenRegistry.waitForDeployment();
        deployedContracts.TokenRegistry = await tokenRegistry.getAddress();
        console.log("✅ 代币注册表部署成功:", deployedContracts.TokenRegistry);

        // 验证合约owner
        const registryOwner = await tokenRegistry.owner();
        console.log("注册表合约owner:", registryOwner);
        console.log("是否为部署者:", registryOwner.toLowerCase() === deployer.address.toLowerCase());

    } catch (error) {
        console.error("❌ 代币注册表部署失败:", error.message);
        throw error;
    }

    console.log("\n=== 4. 配置代币注册表 ===");
    try {
        const TokenRegistry = await ethers.getContractFactory("TokenRegistry");
        const tokenRegistry = TokenRegistry.attach(deployedContracts.TokenRegistry);

        // 将部署者添加为工厂，以便注册代币
        console.log("正在添加工厂权限...");
        const addFactoryTx = await tokenRegistry.addFactory(deployer.address);
        await addFactoryTx.wait();
        console.log("✅ 部署者已添加为工厂");

        // 验证工厂权限
        const isFactory = await tokenRegistry.isFactory(deployer.address);
        console.log("验证工厂权限:", isFactory);

        if (!isFactory) {
            throw new Error("添加工厂权限失败");
        }

    } catch (error) {
        console.error("❌ 配置代币注册表失败:", error.message);
        throw error;
    }

    console.log("\n=== 5. 注册代币到注册表 ===");
    try {
        const TokenRegistry = await ethers.getContractFactory("TokenRegistry");
        const tokenRegistry = TokenRegistry.attach(deployedContracts.TokenRegistry);
        const PXToken = await ethers.getContractFactory("PXToken");
        const pxtoken = PXToken.attach(deployedContracts.PXToken);
        const PAToken = await ethers.getContractFactory("PAToken");
        const patoken = PAToken.attach(deployedContracts.PAToken);

        // 注册PXT代币
        const pxtName = await pxtoken.name();
        const pxtSymbol = await pxtoken.symbol();
        const pxtDecimals = await pxtoken.decimals();
        const pxtTotalSupply = await pxtoken.totalSupply();

        console.log("注册PXT代币:");
        console.log(`- 地址: ${deployedContracts.PXToken}`);
        console.log(`- 信息: ${pxtName} (${pxtSymbol}), ${ethers.formatEther(pxtTotalSupply)} 总量`);

        const registerPxtTx = await tokenRegistry.registerToken(
            deployedContracts.PXToken,
            pxtName,
            pxtSymbol,
            pxtDecimals,
            pxtTotalSupply,
            deployer.address,
            true  // 是PXT代币
        );
        await registerPxtTx.wait();
        console.log("✅ PXT代币已注册");

        // 注册PAT代币
        const patName = await patoken.name();
        const patSymbol = await patoken.symbol();
        const patDecimals = await patoken.decimals();
        const patTotalSupply = await patoken.totalSupply();

        console.log("注册PAT代币:");
        console.log(`- 地址: ${deployedContracts.PAToken}`);
        console.log(`- 信息: ${patName} (${patSymbol}), ${ethers.formatEther(patTotalSupply)} 总量`);

        const registerPatTx = await tokenRegistry.registerToken(
            deployedContracts.PAToken,
            patName,
            patSymbol,
            patDecimals,
            patTotalSupply,
            deployer.address,
            false  // 不是PXT代币
        );
        await registerPatTx.wait();
        console.log("✅ PAT代币已注册");

    } catch (error) {
        console.error("❌ 代币注册失败:", error.message);
        throw error;
    }

    // 验证部署
    console.log("\n=== 6. 验证部署结果 ===");
    try {
        const PXToken = await ethers.getContractFactory("PXToken");
        const pxtoken = PXToken.attach(deployedContracts.PXToken);
        const PAToken = await ethers.getContractFactory("PAToken");
        const patoken = PAToken.attach(deployedContracts.PAToken);
        const TokenRegistry = await ethers.getContractFactory("TokenRegistry");
        const tokenRegistry = TokenRegistry.attach(deployedContracts.TokenRegistry);

        const pxtBalance = await pxtoken.balanceOf(deployer.address);
        const patBalance = await patoken.balanceOf(deployer.address);
        const pxtTotalSupply = await pxtoken.totalSupply();
        const patTotalSupply = await patoken.totalSupply();

        console.log("📊 代币供应量验证:");
        console.log(`- PXT总供应量: ${ethers.formatEther(pxtTotalSupply)} PXT`);
        console.log(`- PAT总供应量: ${ethers.formatEther(patTotalSupply)} PAT`);
        console.log(`- 部署者PXT余额: ${ethers.formatEther(pxtBalance)} PXT`);
        console.log(`- 部署者PAT余额: ${ethers.formatEther(patBalance)} PAT`);

        // 验证注册表
        const allTokens = await tokenRegistry.getAllTokens();
        const allPXTokens = await tokenRegistry.getAllPXTokens();
        const allPATokens = await tokenRegistry.getAllPATokens();

        console.log("📋 注册表验证:");
        console.log(`- 总代币数: ${allTokens.length}`);
        console.log(`- PX代币数: ${allPXTokens.length}`);
        console.log(`- PA代币数: ${allPATokens.length}`);

        // 验证池子余额
        console.log("💰 池子余额验证:");
        const chinaBalance = await patoken.balanceOf(chinaMainlandPoolAddress);
        const globalBalance = await patoken.balanceOf(globalPoolAddress);
        const crossChainBalance = await patoken.balanceOf(crossChainPoolAddress);

        console.log(`- 中国大陆池: ${ethers.formatEther(chinaBalance)} PAT`);
        console.log(`- 国际池: ${ethers.formatEther(globalBalance)} PAT`);
        console.log(`- 跨链池: ${ethers.formatEther(crossChainBalance)} PAT`);

    } catch (error) {
        console.error("❌ 验证失败:", error.message);
        throw error;
    }

    // 保存部署信息
    console.log("\n=== 7. 保存部署信息 ===");
    const deploymentInfo = {
        network: network.name,
        chainId: network.config.chainId,
        timestamp: new Date().toISOString(),
        deployer: deployer.address,
        treasury: treasury.address,
        operator: operator.address,
        gasUsed: {
            // 这里可以记录实际使用的Gas
            estimated: "待实现Gas跟踪"
        },
        contracts: {
            PXToken: {
                address: deployedContracts.PXToken,
                name: "Paper x Token",
                symbol: "PXT",
                description: "PXT治理代币"
            },
            PAToken: {
                address: deployedContracts.PAToken,
                name: "Paper Author Token",
                symbol: "PAT",
                description: "PAT功能代币"
            },
            TokenRegistry: {
                address: deployedContracts.TokenRegistry,
                description: "代币注册表"
            }
        },
        pools: {
            chinaMainlandPool: {
                address: chinaMainlandPoolAddress,
                description: "中国大陆池",
                allocation: "100,000,000 PAT",
                note: "确定性生成的地址，无私钥"
            },
            globalPool: {
                address: globalPoolAddress,
                description: "国际池",
                allocation: "100,000,000 PAT",
                note: "确定性生成的地址，无私钥"
            },
            stakingPool: {
                address: stakingPoolAddress,
                description: "质押池",
                allocation: "0 PAT (通过质押增发)",
                note: "确定性生成的地址，无私钥"
            },
            crossChainPool: {
                address: crossChainPoolAddress,
                privateKey: crossChainPoolPrivateKey,
                description: "跨链池（到PXPAC）",
                allocation: "100,000,000 PAT",
                note: "真实钱包地址，有私钥用于跨链操作"
            }
        },
        security: {
            poolAddressGeneration: "大部分池子基于确定性生成，跨链池使用真实钱包",
            privateKeyStorage: "跨链池有私钥用于自动跨链，其他池子无私钥",
            accessControl: "基于OpenZeppelin AccessControl"
        }
    };

    // 确保部署目录存在
    const deploymentDir = path.join(__dirname, "../../deployments", network.name);
    if (!fs.existsSync(deploymentDir)) {
        fs.mkdirSync(deploymentDir, { recursive: true });
    }

    // 保存部署文件
    const deploymentFile = path.join(deploymentDir, "core-deployment.json");
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));

    console.log("\n=== 🎉 核心代币系统部署成功 ===");
    console.log("部署信息已保存到:", deploymentFile);

    console.log("\n📋 部署摘要:");
    console.log("✅ PXT治理代币:", deployedContracts.PXToken);
    console.log("✅ PAT功能代币:", deployedContracts.PAToken);
    console.log("✅ 代币注册表:", deployedContracts.TokenRegistry);
    console.log("✅ 4个资金池地址已生成");

    console.log("\n🔒 安全特性:");
    console.log("- 池子地址采用确定性生成，无私钥泄露风险");
    console.log("- 团队代币锁定40个季度");
    console.log("- 基于OpenZeppelin的访问控制");

    console.log("\n🚀 下一步操作:");
    console.log("1. 部署质押系统: npx hardhat run scripts/deploy/02-deploy-staking-system.js --network", network.name);
    console.log("2. 部署治理系统: npx hardhat run scripts/deploy/03-deploy-governance.js --network", network.name);
    console.log("3. 验证合约: npx hardhat verify --network", network.name);

    return {
        pxtoken: deployedContracts.PXToken,
        patoken: deployedContracts.PAToken,
        tokenRegistry: deployedContracts.TokenRegistry,
        chinaMainlandPool: chinaMainlandPoolAddress,
        globalPool: globalPoolAddress,
        stakingPool: stakingPoolAddress,
        crossChainPool: crossChainPoolAddress
    };
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("部署失败:", error);
            process.exit(1);
        });
}

module.exports = main;
