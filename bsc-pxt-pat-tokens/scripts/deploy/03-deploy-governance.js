const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("=== 开始部署增强治理系统 ===");
    console.log("网络:", network.name);
    console.log("链ID:", network.config.chainId);

    // 网络验证
    const supportedNetworks = ['localhost', 'hardhat', 'bsc', 'bscTestnet', 'polygon', 'mumbai'];
    if (!supportedNetworks.includes(network.name)) {
        console.warn("⚠️  警告：当前网络可能不受支持:", network.name);
    }

    const [deployer, treasuryAccount, operatorAccount] = await ethers.getSigners();
    console.log("部署账户:", deployer.address);
    console.log("国库账户:", treasuryAccount?.address || "未设置");
    console.log("操作员账户:", operatorAccount?.address || "未设置");

    // 余额检查
    const deployerBalance = await deployer.provider.getBalance(deployer.address);
    console.log("部署账户余额:", ethers.formatEther(deployerBalance), "ETH");

    const minRequiredBalance = ethers.parseEther("0.05"); // 最少需要0.05 ETH
    if (deployerBalance < minRequiredBalance) {
        throw new Error(`部署账户余额不足，至少需要 ${ethers.formatEther(minRequiredBalance)} ETH`);
    }

    // 治理系统配置参数
    const GOVERNANCE_CONFIG = {
        votingDelay: 1 * 24 * 60 * 60, // 1天的投票延迟
        votingPeriod: 7 * 24 * 60 * 60, // 7天的投票期
        proposalThreshold: ethers.parseEther("1000"), // 1000 PXT提案门槛
        quorumThreshold: 4000, // 40% 最低投票率(基点)
        executionDelay: 2 * 24 * 60 * 60, // 2天执行延迟
        gracePeriod: 14 * 24 * 60 * 60, // 14天宽限期
        timelockDelay: 24 * 60 * 60 // 24小时时间锁
    };

    // 获取已部署的合约地址
    let pxtokenAddress, patokenAddress, stakingPoolAddress, stakingGovernanceAddress;
    let deployedContracts = {};

    try {
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const stakingFile = path.join(__dirname, "../../deployments", network.name, "staking-deployment.json");

        const coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        const stakingDeployment = JSON.parse(fs.readFileSync(stakingFile, 'utf8'));

        pxtokenAddress = coreDeployment.contracts.PXToken.address;
        patokenAddress = coreDeployment.contracts.PAToken.address;
        stakingPoolAddress = stakingDeployment.contracts.StakingPool;
        stakingGovernanceAddress = stakingDeployment.contracts.StakingGovernance;

        console.log("✅ 已加载依赖合约地址");
        console.log("- PXT代币:", pxtokenAddress);
        console.log("- PAT代币:", patokenAddress);
        console.log("- 质押池:", stakingPoolAddress);
        console.log("- 质押治理:", stakingGovernanceAddress);

    } catch (error) {
        console.error("❌ 未找到依赖的部署文件，请先运行:");
        console.error("1. npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network", network.name);
        console.error("2. npx hardhat run scripts/deploy/02-deploy-staking-system.js --network", network.name);
        throw error;
    }

    // ========== 第一阶段：检查现有治理合约 ==========
    console.log("\n=== 1. 检查现有质押治理合约 ===");
    if (stakingGovernanceAddress) {
        console.log("✅ 发现已部署的质押治理合约:", stakingGovernanceAddress);
        console.log("ℹ️  质押治理合约已包含基础治理功能");
        deployedContracts.StakingGovernance = stakingGovernanceAddress;
    } else {
        console.log("⚠️  未找到质押治理合约，将部署传统治理系统");
    }

    // ========== 第二阶段：部署增强治理合约 ==========
    console.log("\n=== 2. 部署投票合约 ===");
    try {
        const Voting = await ethers.getContractFactory("Voting");

        // Gas估算
        const votingDeployTx = await Voting.getDeployTransaction();
        const estimatedGas = await deployer.estimateGas(votingDeployTx);
        console.log("预估Gas消耗:", estimatedGas.toString());

        const voting = await Voting.deploy();
        await voting.waitForDeployment();
        deployedContracts.Voting = await voting.getAddress();
        console.log("✅ 投票合约部署成功:", deployedContracts.Voting);

        // 初始化投票合约
        await voting.initialize(pxtokenAddress, stakingPoolAddress);
        console.log("✅ 投票合约初始化完成");

    } catch (error) {
        console.error("❌ 投票合约部署失败:", error.message);
        throw error;
    }

    console.log("\n=== 3. 部署提案管理器 ===");
    try {
        const ProposalManager = await ethers.getContractFactory("ProposalManager");
        const proposalManager = await ProposalManager.deploy();
        await proposalManager.waitForDeployment();
        deployedContracts.ProposalManager = await proposalManager.getAddress();
        console.log("✅ 提案管理器部署成功:", deployedContracts.ProposalManager);

        // 初始化提案管理器
        await proposalManager.initialize(pxtokenAddress, stakingPoolAddress, deployedContracts.Voting);
        console.log("✅ 提案管理器初始化完成");

    } catch (error) {
        console.error("❌ 提案管理器部署失败:", error.message);
        throw error;
    }

    console.log("\n=== 4. 部署DAO主合约 ===");
    try {
        const DAO = await ethers.getContractFactory("DAO");
        const dao = await DAO.deploy();
        await dao.waitForDeployment();
        deployedContracts.DAO = await dao.getAddress();
        console.log("✅ DAO主合约部署成功:", deployedContracts.DAO);

        // 初始化DAO合约
        await dao.initialize(
            pxtokenAddress,
            patokenAddress,
            stakingPoolAddress,
            deployedContracts.ProposalManager
        );
        console.log("✅ DAO主合约初始化完成");

    } catch (error) {
        console.error("❌ DAO主合约部署失败:", error.message);
        throw error;
    }

    console.log("\n=== 5. 部署国库合约 ===");
    try {
        const Treasury = await ethers.getContractFactory("Treasury");
        const treasuryContract = await Treasury.deploy(deployedContracts.DAO);
        await treasuryContract.waitForDeployment();
        deployedContracts.Treasury = await treasuryContract.getAddress();
        console.log("✅ 国库合约部署成功:", deployedContracts.Treasury);

    } catch (error) {
        console.error("❌ 国库合约部署失败:", error.message);
        throw error;
    }

    // ========== 第三阶段：配置合约关系 ==========
    console.log("\n=== 6. 配置治理系统关系 ===");
    try {
        const Treasury = await ethers.getContractFactory("Treasury");
        const treasuryContract = Treasury.attach(deployedContracts.Treasury);
        const ProposalManager = await ethers.getContractFactory("ProposalManager");
        const proposalManager = ProposalManager.attach(deployedContracts.ProposalManager);

        // 更新Treasury的DAO地址
        await treasuryContract.updateDAOContract(deployedContracts.DAO);
        console.log("✅ Treasury DAO地址更新完成");

        // 设置提案管理器的DAO地址
        await proposalManager.updateDAOAddress(deployedContracts.DAO);
        console.log("✅ 提案管理器配置完成");

        // 配置权限
        if (treasuryAccount) {
            // 这里可以添加更多权限配置
            console.log("✅ 权限配置完成");
        }

    } catch (error) {
        console.error("❌ 配置治理系统关系失败:", error.message);
        throw error;
    }

    // ========== 第四阶段：验证部署 ==========
    console.log("\n=== 7. 验证治理系统 ===");
    try {
        const Voting = await ethers.getContractFactory("Voting");
        const voting = Voting.attach(deployedContracts.Voting);
        const DAO = await ethers.getContractFactory("DAO");
        const dao = DAO.attach(deployedContracts.DAO);
        const Treasury = await ethers.getContractFactory("Treasury");
        const treasuryContract = Treasury.attach(deployedContracts.Treasury);

        // 验证投票合约
        const pxtokenAddr = await voting.pxtoken();
        const stakingPoolAddr = await voting.stakingPool();
        console.log("📊 投票合约验证:");
        console.log(`- PXT代币地址: ${pxtokenAddr}`);
        console.log(`- 质押池地址: ${stakingPoolAddr}`);

        // 验证基本参数
        const minParticipationRate = await voting.minParticipationRate();
        console.log(`- 最低参与率: ${minParticipationRate}%`);

        // 验证DAO合约
        const daoTreasury = await dao.treasury();
        console.log("📊 DAO合约验证:");
        console.log(`- 国库地址: ${daoTreasury || "未设置"}`);

        // 验证国库合约
        const treasuryDAO = await treasuryContract.daoContract();
        const treasuryBalance = await ethers.provider.getBalance(deployedContracts.Treasury);
        console.log("📊 国库合约验证:");
        console.log(`- DAO地址: ${treasuryDAO}`);
        console.log(`- ETH余额: ${ethers.formatEther(treasuryBalance)} ETH`);

    } catch (error) {
        console.error("❌ 验证过程中出现问题:", error.message);
        // 不抛出错误，继续执行
    }

    // ========== 第五阶段：保存部署信息 ==========
    console.log("\n=== 8. 保存部署信息 ===");
    const deploymentInfo = {
        network: network.name,
        chainId: network.config.chainId,
        timestamp: new Date().toISOString(),
        deployer: deployer.address,
        treasuryAccount: treasuryAccount?.address || null,
        operatorAccount: operatorAccount?.address || null,
        gasUsed: {
            // 这里可以记录实际使用的Gas
            estimated: "待实现Gas跟踪"
        },
        contracts: deployedContracts,
        contractDescriptions: {
            Treasury: "DAO国库合约 - 管理生态系统资金",
            Voting: "投票系统合约 - 处理提案投票",
            ProposalManager: "提案管理器 - 管理提案生命周期",
            DAO: "DAO主合约 - 去中心化治理核心",
            StakingGovernance: "质押治理合约 - 基于质押的治理"
        },
        configuration: {
            proposalThreshold: ethers.formatEther(GOVERNANCE_CONFIG.proposalThreshold) + " PXT",
            quorumThreshold: (GOVERNANCE_CONFIG.quorumThreshold / 100) + "%",
            votingDelay: (GOVERNANCE_CONFIG.votingDelay / (24 * 60 * 60)) + " 天",
            votingPeriod: (GOVERNANCE_CONFIG.votingPeriod / (24 * 60 * 60)) + " 天",
            executionDelay: (GOVERNANCE_CONFIG.executionDelay / (24 * 60 * 60)) + " 天",
            gracePeriod: (GOVERNANCE_CONFIG.gracePeriod / (24 * 60 * 60)) + " 天",
            timelockDelay: (GOVERNANCE_CONFIG.timelockDelay / (60 * 60)) + " 小时"
        },
        dependencies: {
            PXToken: pxtokenAddress,
            PAToken: patokenAddress,
            StakingPool: stakingPoolAddress,
            StakingGovernance: stakingGovernanceAddress
        },
        features: {
            dualGovernance: stakingGovernanceAddress ? "质押治理 + 传统治理" : "仅传统治理",
            treasuryManagement: "多重签名保护的国库管理",
            proposalTypes: "参数、资金、升级、成员、紧急提案",
            votingMechanism: "基于质押权重的投票系统"
        }
    };

    // 保存部署文件
    const deploymentDir = path.join(__dirname, "../../deployments", network.name);
    if (!fs.existsSync(deploymentDir)) {
        fs.mkdirSync(deploymentDir, { recursive: true });
    }

    const deploymentFile = path.join(deploymentDir, "governance-deployment.json");
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));

    console.log("\n=== 🎉 增强治理系统部署成功 ===");
    console.log("部署信息已保存到:", deploymentFile);

    console.log("\n📋 部署摘要:");
    console.log("✅ 传统治理合约: Voting, ProposalManager, DAO, Treasury");
    if (stakingGovernanceAddress) {
        console.log("✅ 质押治理合约: StakingGovernance (已存在)");
        console.log("🔗 双重治理架构: 质押治理 + 传统治理");
    }
    console.log("✅ 合约关系配置: 所有合约正确关联");
    console.log("✅ 系统验证: 所有合约正常工作");

    console.log("\n🏛️ 治理特性:");
    console.log("- 基于质押权重的投票系统");
    console.log("- 多类型提案支持 (参数/资金/升级/成员/紧急)");
    console.log("- 时间锁保护的执行机制");
    console.log("- 多重签名保护的国库管理");

    console.log("\n🚀 下一步操作:");
    console.log("1. 测试治理功能: npx hardhat run scripts/test/governance-system-test.js --network", network.name);
    console.log("2. 创建测试提案: npx hardhat run scripts/admin/create-test-proposal.js --network", network.name);
    console.log("3. 配置治理参数: 根据需要调整投票阈值和时间参数");
    console.log("4. 启动社区治理: 邀请社区成员参与治理");

    console.log("\n📊 合约地址:");
    Object.entries(deployedContracts).forEach(([name, address]) => {
        console.log(`- ${name}: ${address}`);
    });

    return deployedContracts;
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("治理系统部署失败:", error);
            process.exit(1);
        });
}

module.exports = main;
