const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("=== 开始部署完整质押系统 ===");
    console.log("网络:", network.name);
    console.log("链ID:", network.config.chainId);

    const [deployer, treasury, operator, guardian1, guardian2] = await ethers.getSigners();
    console.log("部署账户:", deployer.address);
    console.log("国库账户:", treasury.address);
    console.log("操作员账户:", operator.address);

    // 质押系统配置参数
    const BASE_APR = 500; // 5% 基础年化收益率(基点)
    const MIN_UNLOCK_PERIOD = 27 * 24 * 60 * 60; // 27天
    const MAX_UNLOCK_PERIOD = 1095 * 24 * 60 * 60; // 3年

    // 质押等级配置 (基点，10000 = 100%)
    const STAKING_LEVELS = [
        { name: "丁级", minAmount: ethers.parseEther("100"), multiplier: 12000, randomMin: 12000, randomMax: 13000 },
        { name: "丙级", minAmount: ethers.parseEther("1000"), multiplier: 13000, randomMin: 13000, randomMax: 14000 },
        { name: "乙级", minAmount: ethers.parseEther("5000"), multiplier: 15000, randomMin: 15000, randomMax: 16000 },
        { name: "甲级", minAmount: ethers.parseEther("20000"), multiplier: 19000, randomMin: 19000, randomMax: 20000 },
        { name: "十绝", minAmount: ethers.parseEther("100000"), multiplier: 22500, randomMin: 20000, randomMax: 25000 },
        { name: "双十绝", minAmount: ethers.parseEther("250000"), multiplier: 30000, randomMin: 30000, randomMax: 40000 },
        { name: "至尊", minAmount: ethers.parseEther("500000"), multiplier: 45000, randomMin: 40000, randomMax: 60000 }
    ];

    // 治理配置
    const GOVERNANCE_CONFIG = {
        votingDelay: 1 * 24 * 60 * 60, // 1天
        votingPeriod: 7 * 24 * 60 * 60, // 7天
        proposalThreshold: ethers.parseEther("1000"), // 1000 PXT
        quorumThreshold: 4000, // 40%
        executionDelay: 2 * 24 * 60 * 60, // 2天
        gracePeriod: 14 * 24 * 60 * 60 // 14天
    };

    // 保险库配置
    const VAULT_CONFIG = {
        requiredSignatures: 3,
        maxSignatures: 5,
        timelock: 24 * 60 * 60, // 24小时
        emergencyTimelock: 1 * 60 * 60, // 1小时
        maxSingleTransaction: ethers.parseEther("1000000") // 1M PXT
    };

    // 获取核心合约地址
    let pxtokenAddress, patokenAddress, tokenRegistryAddress;

    try {
        const deploymentFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const deploymentData = JSON.parse(fs.readFileSync(deploymentFile, 'utf8'));
        pxtokenAddress = deploymentData.contracts.PXToken.address;
        patokenAddress = deploymentData.contracts.PAToken.address;
        tokenRegistryAddress = deploymentData.contracts.TokenRegistry.address;

        console.log("✅ 已加载核心合约地址");
        console.log("- PXT代币:", pxtokenAddress);
        console.log("- PAT代币:", patokenAddress);
        console.log("- 代币注册表:", tokenRegistryAddress);
    } catch (error) {
        console.error("❌ 未找到核心合约部署信息，请先运行:");
        console.error("npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network", network.name);
        process.exit(1);
    }

    // 部署结果存储
    const deployedContracts = {};

    // ========== 第一阶段：部署核心合约 ==========
    console.log("\n=== 1. 部署质押保险库 ===");
    const StakingVault = await ethers.getContractFactory("StakingVault");
    const stakingVault = await StakingVault.deploy();
    await stakingVault.waitForDeployment();
    deployedContracts.StakingVault = await stakingVault.getAddress();
    console.log("✅ 质押保险库部署成功:", deployedContracts.StakingVault);

    console.log("\n=== 2. 部署质押池 ===");
    const StakingPool = await ethers.getContractFactory("StakingPool");
    const stakingPool = await StakingPool.deploy();
    await stakingPool.waitForDeployment();
    deployedContracts.StakingPool = await stakingPool.getAddress();
    console.log("✅ 质押池部署成功:", deployedContracts.StakingPool);

    console.log("\n=== 3. 部署奖励分配器 ===");
    const RewardDistributor = await ethers.getContractFactory("RewardDistributor");
    const rewardDistributor = await RewardDistributor.deploy(patokenAddress);
    await rewardDistributor.waitForDeployment();
    deployedContracts.RewardDistributor = await rewardDistributor.getAddress();
    console.log("✅ 奖励分配器部署成功:", deployedContracts.RewardDistributor);

    console.log("\n=== 4. 部署质押工厂 ===");
    const StakingFactory = await ethers.getContractFactory("StakingFactory");
    const stakingFactory = await StakingFactory.deploy(pxtokenAddress, patokenAddress);
    await stakingFactory.waitForDeployment();
    deployedContracts.StakingFactory = await stakingFactory.getAddress();
    console.log("✅ 质押工厂部署成功:", deployedContracts.StakingFactory);

    // ========== 第二阶段：部署增强合约 ==========
    console.log("\n=== 5. 部署质押奖励合约 ===");
    const StakingRewards = await ethers.getContractFactory("StakingRewards");
    const stakingRewards = await StakingRewards.deploy(deployedContracts.StakingPool);
    await stakingRewards.waitForDeployment();
    deployedContracts.StakingRewards = await stakingRewards.getAddress();
    console.log("✅ 质押奖励合约部署成功:", deployedContracts.StakingRewards);

    console.log("\n=== 6. 部署质押治理合约 ===");
    const StakingGovernance = await ethers.getContractFactory("StakingGovernance");
    const stakingGovernance = await StakingGovernance.deploy(deployedContracts.StakingPool);
    await stakingGovernance.waitForDeployment();
    deployedContracts.StakingGovernance = await stakingGovernance.getAddress();
    console.log("✅ 质押治理合约部署成功:", deployedContracts.StakingGovernance);

    console.log("\n=== 7. 部署质押分析合约 ===");
    const StakingAnalytics = await ethers.getContractFactory("StakingAnalytics");
    const stakingAnalytics = await StakingAnalytics.deploy(deployedContracts.StakingPool);
    await stakingAnalytics.waitForDeployment();
    deployedContracts.StakingAnalytics = await stakingAnalytics.getAddress();
    console.log("✅ 质押分析合约部署成功:", deployedContracts.StakingAnalytics);

    // ========== 第三阶段：初始化和配置 ==========
    console.log("\n=== 8. 初始化质押池 ===");
    await stakingPool.initialize(pxtokenAddress, patokenAddress, BASE_APR);
    console.log("✅ 质押池初始化完成");

    console.log("\n=== 9. 配置保险库 ===");
    // 创建不同类型的保险库
    await stakingVault.createVault(0, pxtokenAddress, 2000); // Staking vault, 20% reserve
    await stakingVault.createVault(1, patokenAddress, 1000); // Rewards vault, 10% reserve
    await stakingVault.createVault(2, patokenAddress, 5000); // Insurance vault, 50% reserve
    await stakingVault.createVault(3, pxtokenAddress, 10000); // Emergency vault, 100% reserve
    console.log("✅ 保险库配置完成");

    console.log("\n=== 10. 配置奖励系统 ===");
    // 将质押池添加到奖励分配器
    await rewardDistributor.addRewardPool(deployedContracts.StakingPool, 1000);
    console.log("✅ 质押池已添加到奖励分配器");

    // 配置奖励池
    await stakingRewards.addRewardPool(patokenAddress, ethers.parseEther("1")); // 每秒1个PAT
    console.log("✅ 奖励池配置完成");

    console.log("\n=== 11. 配置工厂合约 ===");
    await stakingFactory.updateRewardDistributor(deployedContracts.RewardDistributor);
    await stakingFactory.updateStandardImplementation(deployedContracts.StakingPool);
    console.log("✅ 工厂合约配置完成");

    console.log("\n=== 12. 配置权限系统 ===");
    // 为保险库设置守护者
    if (guardian1 && guardian2) {
        await stakingVault.grantRole(await stakingVault.GUARDIAN_ROLE(), guardian1.address);
        await stakingVault.grantRole(await stakingVault.GUARDIAN_ROLE(), guardian2.address);
        console.log("✅ 保险库守护者配置完成");
    }

    // 为治理合约设置提案者
    await stakingGovernance.grantRole(await stakingGovernance.PROPOSER_ROLE(), treasury.address);
    await stakingGovernance.grantRole(await stakingGovernance.EXECUTOR_ROLE(), operator.address);
    console.log("✅ 治理权限配置完成");

    // 为分析合约设置数据提供者
    await stakingAnalytics.grantRole(await stakingAnalytics.DATA_PROVIDER_ROLE(), operator.address);
    await stakingAnalytics.grantRole(await stakingAnalytics.ANALYST_ROLE(), treasury.address);
    console.log("✅ 分析权限配置完成");

    // ========== 第四阶段：验证和测试 ==========
    console.log("\n=== 13. 验证质押系统 ===");
    const minStake = await stakingPool.minStakeAmount();
    const baseAPR = await stakingPool.baseAPR();
    const totalStaked = await stakingPool.getTotalStaked();

    console.log("最小质押金额:", ethers.formatEther(minStake), "PXT");
    console.log("基础年化收益率:", baseAPR.toString(), "基点");
    console.log("当前总质押量:", ethers.formatEther(totalStaked), "PXT");

    // 验证保险库
    const stakingVaultBalance = await stakingVault.getTotalBalance();
    console.log("保险库总余额:", ethers.formatEther(stakingVaultBalance), "代币");

    // 验证治理系统
    const proposalCount = await stakingGovernance.proposalCount();
    console.log("治理提案数量:", proposalCount.toString());

    console.log("\n=== 14. 质押等级配置 ===");
    STAKING_LEVELS.forEach((level) => {
        console.log(`- ${level.name}: ${ethers.formatEther(level.minAmount)} PXT (倍数: ${level.multiplier/100}%)`);
    });

    // 保存部署信息 - 处理 BigInt 序列化问题
    const serializableStakingLevels = STAKING_LEVELS.map(level => ({
        ...level,
        minAmount: level.minAmount.toString()
    }));

    const serializableGovernanceConfig = {
        ...GOVERNANCE_CONFIG,
        proposalThreshold: GOVERNANCE_CONFIG.proposalThreshold.toString()
    };

    const serializableVaultConfig = {
        ...VAULT_CONFIG,
        maxSingleTransaction: VAULT_CONFIG.maxSingleTransaction.toString()
    };

    const deploymentInfo = {
        network: network.name,
        chainId: network.config.chainId,
        timestamp: new Date().toISOString(),
        deployer: deployer.address,
        treasury: treasury.address,
        operator: operator.address,
        guardians: guardian1 && guardian2 ? [guardian1.address, guardian2.address] : [],
        contracts: deployedContracts,
        configuration: {
            minStakeAmount: minStake.toString(),
            baseAPR: baseAPR.toString(),
            minUnlockPeriod: MIN_UNLOCK_PERIOD,
            maxUnlockPeriod: MAX_UNLOCK_PERIOD,
            stakingLevels: serializableStakingLevels,
            governanceConfig: serializableGovernanceConfig,
            vaultConfig: serializableVaultConfig
        },
        verification: {
            totalStaked: totalStaked.toString(),
            vaultBalance: stakingVaultBalance.toString(),
            proposalCount: proposalCount.toString()
        }
    };

    // 保存部署文件
    const deploymentDir = path.join(__dirname, "../../deployments", network.name);
    if (!fs.existsSync(deploymentDir)) {
        fs.mkdirSync(deploymentDir, { recursive: true });
    }

    const deploymentFile = path.join(deploymentDir, "staking-deployment.json");
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));

    console.log("\n=== 🎉 完整质押系统部署成功 ===");
    console.log("部署信息已保存到:", deploymentFile);

    console.log("\n📋 部署摘要:");
    console.log("✅ 核心合约: StakingPool, RewardDistributor, StakingFactory");
    console.log("✅ 增强合约: StakingRewards, StakingGovernance, StakingAnalytics");
    console.log("✅ 安全合约: StakingVault");
    console.log("✅ 权限配置: 多重签名、治理权限、数据权限");
    console.log("✅ 系统验证: 所有合约正常工作");

    console.log("\n🚀 下一步操作:");
    console.log("1. 向奖励池充值: 转入PAT代币到奖励分配器");
    console.log("2. 测试质押功能: npx hardhat run scripts/test/staking-system-test.js --network", network.name);
    console.log("3. 启动治理提案: 通过治理合约调整参数");
    console.log("4. 监控系统状态: 使用分析合约查看数据");

    console.log("\n📊 合约地址:");
    Object.entries(deployedContracts).forEach(([name, address]) => {
        console.log(`- ${name}: ${address}`);
    });

    return deploymentInfo;
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("部署失败:", error);
            process.exit(1);
        });
}

module.exports = main;
