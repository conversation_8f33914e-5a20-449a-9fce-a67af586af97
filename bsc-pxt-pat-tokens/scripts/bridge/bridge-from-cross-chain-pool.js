const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🌉 从跨链池执行跨链操作");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("时间:", new Date().toISOString());
    
    // 跨链参数配置
    const BRIDGE_AMOUNT = ethers.parseEther("1000"); // 默认1000 PAT
    const TARGET_CHAIN_ID = 327; // PXA链ID
    const TARGET_ADDRESS = "******************************************"; // 默认接收地址
    
    console.log("🎯 跨链参数:");
    console.log(`- 跨链金额: ${ethers.formatEther(BRIDGE_AMOUNT)} PAT`);
    console.log(`- 目标链ID: ${TARGET_CHAIN_ID} (PXA链)`);
    console.log(`- 接收地址: ${TARGET_ADDRESS}`);
    
    // 加载部署信息
    let coreDeployment, bridgeDeployment;
    try {
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const bridgeFile = path.join(__dirname, "../../deployments", network.name, "bridge-deployment.json");
        
        coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        bridgeDeployment = JSON.parse(fs.readFileSync(bridgeFile, 'utf8'));
        console.log("✅ 已加载部署信息");
    } catch (error) {
        console.error("❌ 未找到部署文件");
        process.exit(1);
    }
    
    // 获取跨链池信息
    const crossChainPoolAddress = coreDeployment.pools.crossChainPool.address;
    const crossChainPoolPrivateKey = coreDeployment.pools.crossChainPool.privateKey;
    
    if (!crossChainPoolPrivateKey) {
        console.error("❌ 跨链池私钥未配置");
        process.exit(1);
    }
    
    // 创建跨链池钱包
    const crossChainPoolWallet = new ethers.Wallet(crossChainPoolPrivateKey, ethers.provider);
    console.log("🔑 跨链池钱包:");
    console.log(`- 地址: ${crossChainPoolWallet.address}`);
    console.log(`- 地址验证: ${crossChainPoolWallet.address === crossChainPoolAddress ? '✅ 匹配' : '❌ 不匹配'}`);
    
    // 获取合约实例
    const PAToken = await ethers.getContractFactory("PAToken");
    const patToken = PAToken.attach(coreDeployment.contracts.PAToken.address);
    
    const TokenBridge = await ethers.getContractFactory("TokenBridge");
    const tokenBridge = TokenBridge.attach(bridgeDeployment.contracts.TokenBridge.address);
    
    console.log("\n=== 1. 跨链前状态检查 ===");
    
    // 检查跨链池余额
    const patBalance = await patToken.balanceOf(crossChainPoolAddress);
    const bnbBalance = await ethers.provider.getBalance(crossChainPoolAddress);
    
    console.log("💰 跨链池当前余额:");
    console.log(`- PAT余额: ${ethers.formatEther(patBalance)} PAT`);
    console.log(`- BNB余额: ${ethers.formatEther(bnbBalance)} BNB`);
    
    // 检查余额是否充足
    if (patBalance < BRIDGE_AMOUNT) {
        console.error(`❌ PAT余额不足! 需要: ${ethers.formatEther(BRIDGE_AMOUNT)} PAT, 当前: ${ethers.formatEther(patBalance)} PAT`);
        process.exit(1);
    }
    
    // 计算跨链费用
    let bridgeFee;
    try {
        bridgeFee = await tokenBridge.calculateBridgeFee(TARGET_CHAIN_ID, BRIDGE_AMOUNT);
        console.log(`💸 预估跨链费用: ${ethers.formatEther(bridgeFee)} BNB`);
        
        if (bnbBalance < bridgeFee) {
            console.error(`❌ BNB余额不足支付跨链费用! 需要: ${ethers.formatEther(bridgeFee)} BNB, 当前: ${ethers.formatEther(bnbBalance)} BNB`);
            process.exit(1);
        }
    } catch (error) {
        console.error("❌ 无法计算跨链费用:", error.message);
        process.exit(1);
    }
    
    // 检查授权
    const allowance = await patToken.allowance(crossChainPoolAddress, bridgeDeployment.contracts.TokenBridge.address);
    console.log(`🔐 当前授权额度: ${ethers.formatEther(allowance)} PAT`);
    
    if (allowance < BRIDGE_AMOUNT) {
        console.log("📝 授权额度不足，执行授权...");
        const approveTx = await patToken.connect(crossChainPoolWallet).approve(
            bridgeDeployment.contracts.TokenBridge.address,
            ethers.MaxUint256
        );
        await approveTx.wait();
        console.log("✅ 无上限授权完成");
    }
    
    console.log("\n=== 2. 执行跨链操作 ===");
    
    // 记录跨链前状态
    const beforePatBalance = await patToken.balanceOf(crossChainPoolAddress);
    const beforeBnbBalance = await ethers.provider.getBalance(crossChainPoolAddress);
    
    console.log("🚀 开始执行跨链操作...");
    
    try {
        // 执行跨链操作
        const bridgeTx = await tokenBridge.connect(crossChainPoolWallet).bridgeTokens(
            TARGET_CHAIN_ID,
            TARGET_ADDRESS,
            BRIDGE_AMOUNT,
            {
                value: bridgeFee
            }
        );
        
        console.log("⏳ 等待交易确认...");
        console.log(`- 交易哈希: ${bridgeTx.hash}`);
        
        const receipt = await bridgeTx.wait();
        console.log("✅ 跨链交易已确认!");
        console.log(`- 区块号: ${receipt.blockNumber}`);
        console.log(`- Gas使用: ${receipt.gasUsed.toString()}`);
        console.log(`- Gas费用: ${ethers.formatEther(receipt.gasUsed * receipt.gasPrice || 0n)} BNB`);
        
        // 解析事件
        const bridgeEvents = receipt.logs.filter(log => {
            try {
                const parsed = tokenBridge.interface.parseLog(log);
                return parsed.name === "TokensBridged";
            } catch {
                return false;
            }
        });
        
        if (bridgeEvents.length > 0) {
            const bridgeEvent = tokenBridge.interface.parseLog(bridgeEvents[0]);
            console.log("\n📋 跨链事件详情:");
            console.log(`- 事件ID: ${bridgeEvent.args.bridgeId}`);
            console.log(`- 发送者: ${bridgeEvent.args.sender}`);
            console.log(`- 目标链: ${bridgeEvent.args.targetChain}`);
            console.log(`- 接收者: ${bridgeEvent.args.recipient}`);
            console.log(`- 金额: ${ethers.formatEther(bridgeEvent.args.amount)} PAT`);
            console.log(`- 费用: ${ethers.formatEther(bridgeEvent.args.fee)} BNB`);
        }
        
    } catch (error) {
        console.error("❌ 跨链操作失败:", error.message);
        
        // 尝试获取更详细的错误信息
        if (error.data) {
            try {
                const decodedError = tokenBridge.interface.parseError(error.data);
                console.error("❌ 合约错误:", decodedError.name, decodedError.args);
            } catch (decodeError) {
                console.error("❌ 无法解析错误数据");
            }
        }
        
        process.exit(1);
    }
    
    console.log("\n=== 3. 跨链后状态验证 ===");
    
    // 检查跨链后余额
    const afterPatBalance = await patToken.balanceOf(crossChainPoolAddress);
    const afterBnbBalance = await ethers.provider.getBalance(crossChainPoolAddress);
    
    const patUsed = beforePatBalance - afterPatBalance;
    const bnbUsed = beforeBnbBalance - afterBnbBalance;
    
    console.log("💰 余额变化:");
    console.log(`- PAT余额变化: -${ethers.formatEther(patUsed)} PAT`);
    console.log(`- BNB余额变化: -${ethers.formatEther(bnbUsed)} BNB`);
    console.log(`- PAT剩余余额: ${ethers.formatEther(afterPatBalance)} PAT`);
    console.log(`- BNB剩余余额: ${ethers.formatEther(afterBnbBalance)} BNB`);
    
    // 验证金额是否正确
    const patAmountCorrect = patUsed === BRIDGE_AMOUNT;
    const bnbFeeReasonable = bnbUsed >= bridgeFee && bnbUsed <= bridgeFee + ethers.parseEther("0.01"); // 允许一些Gas误差
    
    console.log("✅ 金额验证:");
    console.log(`- PAT扣除正确: ${patAmountCorrect ? '✅' : '❌'}`);
    console.log(`- BNB费用合理: ${bnbFeeReasonable ? '✅' : '❌'}`);
    
    console.log("\n=== 4. 跨链操作总结 ===");
    
    const operationSummary = {
        network: network.name,
        timestamp: new Date().toISOString(),
        operation: {
            amount: ethers.formatEther(BRIDGE_AMOUNT),
            targetChain: TARGET_CHAIN_ID,
            recipient: TARGET_ADDRESS,
            fee: ethers.formatEther(bridgeFee),
            success: true
        },
        transaction: {
            hash: bridgeTx.hash,
            blockNumber: receipt.blockNumber,
            gasUsed: receipt.gasUsed.toString()
        },
        balanceChanges: {
            patUsed: ethers.formatEther(patUsed),
            bnbUsed: ethers.formatEther(bnbUsed),
            remainingPat: ethers.formatEther(afterPatBalance),
            remainingBnb: ethers.formatEther(afterBnbBalance)
        }
    };
    
    console.log("🎉 跨链操作成功完成!");
    console.log("================================================");
    console.log(`✅ 已将 ${ethers.formatEther(BRIDGE_AMOUNT)} PAT 跨链到 PXA链`);
    console.log(`✅ 接收地址: ${TARGET_ADDRESS}`);
    console.log(`✅ 支付费用: ${ethers.formatEther(bridgeFee)} BNB`);
    console.log(`✅ 交易哈希: ${bridgeTx.hash}`);
    
    console.log("\n🔗 后续操作:");
    console.log("1. 在PXA链上验证代币到账:");
    console.log("   - 检查接收地址的PAT-T余额");
    console.log("2. 监控跨链状态:");
    console.log("   - 等待验证者确认");
    console.log("   - 检查跨链完成事件");
    console.log("3. 查看跨链池剩余状态:");
    console.log("   npx hardhat run scripts/debug/check-cross-chain-pool-balance.js --network localhost");
    
    // 保存操作报告
    const reportDir = path.join(__dirname, "../../test-reports");
    if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const reportFile = path.join(reportDir, `bridge-operation-${network.name}-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(operationSummary, null, 2));
    console.log(`📊 操作报告已保存: ${reportFile}`);
    
    return operationSummary;
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("跨链操作失败:", error);
            process.exit(1);
        });
}

module.exports = main;
