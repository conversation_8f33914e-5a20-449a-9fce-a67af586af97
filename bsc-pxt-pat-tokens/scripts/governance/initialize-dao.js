const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("=== 初始化DAO合约 ===");
    console.log("网络:", network.name);
    
    const [deployer] = await ethers.getSigners();
    console.log("操作账户:", deployer.address);
    
    // 加载部署信息
    const governanceFile = path.join(__dirname, "../../deployments", network.name, "governance-deployment.json");
    
    if (!fs.existsSync(governanceFile)) {
        throw new Error("治理系统部署文件不存在");
    }
    
    const governanceData = JSON.parse(fs.readFileSync(governanceFile, 'utf8'));
    console.log("✅ 已加载治理系统部署信息");
    
    // 获取DAO合约实例
    const DAO = await ethers.getContractFactory("DAO");
    const dao = DAO.attach(governanceData.contracts.DAO);
    
    console.log("\n=== 检查当前DAO状态 ===");

    // 检查公共变量（这些可能为零地址）
    const currentTreasury = await dao.treasury();
    const currentProposalManager = await dao.proposalManager();
    const currentPXToken = await dao.pxtoken();

    console.log(`当前国库地址: ${currentTreasury}`);
    console.log(`当前提案管理器: ${currentProposalManager}`);
    console.log(`当前PXT代币: ${currentPXToken}`);

    // 检查contracts mapping（这是实际的初始化状态）
    let contractsPXTAddress;
    try {
        contractsPXTAddress = await dao.contracts("PXT");
        console.log(`contracts["PXT"]: ${contractsPXTAddress}`);
    } catch (e) {
        console.log("无法查询contracts mapping");
        contractsPXTAddress = "******************************************";
    }

    const isZeroAddress = (addr) => addr === "******************************************";

    if (!isZeroAddress(contractsPXTAddress)) {
        console.log("✅ DAO合约已经通过contracts mapping初始化");
        console.log("ℹ️  注意：公共变量可能仍为零地址，这是合约设计问题");
        return;
    }
    
    console.log("\n=== 执行DAO初始化 ===");
    console.log("初始化参数:");
    console.log(`- PXT代币: ${governanceData.dependencies.PXToken}`);
    console.log(`- PAT代币: ${governanceData.dependencies.PAToken}`);
    console.log(`- 质押池: ${governanceData.dependencies.StakingPool}`);
    console.log(`- 提案管理器: ${governanceData.contracts.ProposalManager}`);
    
    try {
        const initTx = await dao.initialize(
            governanceData.dependencies.PXToken,
            governanceData.dependencies.PAToken,
            governanceData.dependencies.StakingPool,
            governanceData.contracts.ProposalManager
        );
        
        console.log("等待交易确认...");
        await initTx.wait();
        console.log("✅ DAO初始化交易成功");
        
        // 验证初始化结果
        console.log("\n=== 验证初始化结果 ===");
        const newTreasury = await dao.treasury();
        const newProposalManager = await dao.proposalManager();
        const newPXToken = await dao.pxtoken();
        const newPAToken = await dao.patoken();
        const newStakingPool = await dao.stakingPool();
        
        console.log(`新国库地址: ${newTreasury}`);
        console.log(`新提案管理器: ${newProposalManager}`);
        console.log(`新PXT代币: ${newPXToken}`);
        console.log(`新PAT代币: ${newPAToken}`);
        console.log(`新质押池: ${newStakingPool}`);
        
        // 验证地址是否正确
        const addressesCorrect = 
            newPXToken === governanceData.dependencies.PXToken &&
            newPAToken === governanceData.dependencies.PAToken &&
            newStakingPool === governanceData.dependencies.StakingPool &&
            newProposalManager === governanceData.contracts.ProposalManager;
            
        if (addressesCorrect) {
            console.log("✅ 所有地址配置正确");
        } else {
            console.log("⚠️  部分地址配置可能有问题");
        }
        
        console.log("\n=== 🎉 DAO初始化完成 ===");
        console.log("现在可以重新运行治理系统测试:");
        console.log("npx hardhat run scripts/test/governance-system-test.js --network localhost");
        
    } catch (error) {
        console.error("❌ DAO初始化失败:", error.message);
        
        // 提供详细的错误诊断
        if (error.message.includes("Already initialized")) {
            console.log("ℹ️  DAO合约可能已经初始化，请检查合约状态");
        } else if (error.message.includes("Ownable")) {
            console.log("ℹ️  权限不足，请确保使用合约所有者账户");
        } else {
            console.log("ℹ️  请检查所有依赖合约是否正确部署");
        }
        
        throw error;
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("初始化失败:", error);
        process.exit(1);
    });
