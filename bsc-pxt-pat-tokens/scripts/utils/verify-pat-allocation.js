const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("💰 验证PAT代币分配情况");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("时间:", new Date().toISOString());
    
    // 获取账户
    const [deployer] = await ethers.getSigners();
    console.log("查询账户:", deployer.address);
    
    // 加载部署信息
    let coreDeployment;
    try {
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        console.log("✅ 已加载部署信息");
    } catch (error) {
        console.error("❌ 未找到部署文件");
        process.exit(1);
    }
    
    // 获取合约实例
    const PAToken = await ethers.getContractFactory("PAToken");
    const patToken = PAToken.attach(coreDeployment.contracts.PAToken.address);
    
    const PXToken = await ethers.getContractFactory("PXToken");
    const pxtToken = PXToken.attach(coreDeployment.contracts.PXToken.address);
    
    console.log("\n=== 1. PAT代币基础信息 ===");
    
    // 获取PAT代币基础信息
    const patName = await patToken.name();
    const patSymbol = await patToken.symbol();
    const patDecimals = await patToken.decimals();
    const patTotalSupply = await patToken.totalSupply();
    const patTotalMinted = await patToken.totalMinted();
    const patTotalBurned = await patToken.totalBurned();
    
    console.log("📊 PAT代币信息:");
    console.log(`- 名称: ${patName}`);
    console.log(`- 符号: ${patSymbol}`);
    console.log(`- 精度: ${patDecimals}`);
    console.log(`- 总供应量: ${ethers.formatEther(patTotalSupply)} PAT`);
    console.log(`- 总铸造量: ${ethers.formatEther(patTotalMinted)} PAT`);
    console.log(`- 总销毁量: ${ethers.formatEther(patTotalBurned)} PAT`);
    
    console.log("\n=== 2. 池子分配验证 ===");
    
    // 检查各个池子的PAT余额
    const pools = coreDeployment.pools;
    let totalPoolBalance = 0n;
    
    console.log("💰 池子PAT余额:");
    
    for (const [poolName, poolInfo] of Object.entries(pools)) {
        const balance = await patToken.balanceOf(poolInfo.address);
        totalPoolBalance += balance;
        
        console.log(`- ${poolInfo.description}: ${ethers.formatEther(balance)} PAT`);
        console.log(`  地址: ${poolInfo.address}`);
        console.log(`  预期分配: ${poolInfo.allocation}`);
        console.log(`  备注: ${poolInfo.note}`);
        console.log("");
    }
    
    console.log(`📊 池子总余额: ${ethers.formatEther(totalPoolBalance)} PAT`);
    
    console.log("\n=== 3. 关键账户余额 ===");
    
    // 检查关键账户的PAT余额
    const keyAccounts = [
        { name: "部署者", address: coreDeployment.deployer },
        { name: "国库", address: coreDeployment.treasury },
        { name: "操作员", address: coreDeployment.operator }
    ];
    
    let totalKeyAccountBalance = 0n;
    
    for (const account of keyAccounts) {
        const balance = await patToken.balanceOf(account.address);
        totalKeyAccountBalance += balance;
        
        console.log(`💰 ${account.name}:`);
        console.log(`- 地址: ${account.address}`);
        console.log(`- PAT余额: ${ethers.formatEther(balance)} PAT`);
        console.log("");
    }
    
    console.log(`📊 关键账户总余额: ${ethers.formatEther(totalKeyAccountBalance)} PAT`);
    
    console.log("\n=== 4. 分配比例验证 ===");
    
    // 计算分配比例
    const totalCirculating = totalPoolBalance + totalKeyAccountBalance;
    
    console.log("📊 分配比例分析:");
    console.log(`- 总流通量: ${ethers.formatEther(totalCirculating)} PAT`);
    console.log(`- 池子占比: ${((totalPoolBalance * 10000n) / totalCirculating / 100n).toString()}%`);
    console.log(`- 关键账户占比: ${((totalKeyAccountBalance * 10000n) / totalCirculating / 100n).toString()}%`);
    
    // 验证总供应量一致性
    const calculatedTotal = totalCirculating;
    const actualTotal = patTotalSupply;
    const isConsistent = calculatedTotal === actualTotal;
    
    console.log("\n=== 5. 一致性验证 ===");
    console.log("🔍 供应量一致性检查:");
    console.log(`- 计算总量: ${ethers.formatEther(calculatedTotal)} PAT`);
    console.log(`- 实际总量: ${ethers.formatEther(actualTotal)} PAT`);
    console.log(`- 一致性: ${isConsistent ? '✅ 一致' : '❌ 不一致'}`);
    
    if (!isConsistent) {
        const difference = actualTotal - calculatedTotal;
        console.log(`- 差异: ${ethers.formatEther(difference)} PAT`);
        console.log("⚠️  可能原因: 存在其他持有者或合约余额");
    }
    
    console.log("\n=== 6. 增发机制验证 ===");
    
    // 检查增发相关信息
    const inflationRate = await patToken.inflationRate();
    // 注意：这些函数可能不存在，我们用try-catch包装
    let lastInflationTime, maxInflationPerPeriod;
    try {
        lastInflationTime = await patToken.lastInflationTime();
    } catch {
        lastInflationTime = 0n;
    }
    try {
        maxInflationPerPeriod = await patToken.maxInflationPerPeriod();
    } catch {
        maxInflationPerPeriod = 0n;
    }
    
    console.log("📈 增发机制信息:");
    console.log(`- 当前增发率: ${inflationRate} 基点 (${(Number(inflationRate) / 100).toFixed(2)}%)`);
    console.log(`- 上次增发时间: ${new Date(Number(lastInflationTime) * 1000).toLocaleString()}`);
    console.log(`- 最大单期增发: ${ethers.formatEther(maxInflationPerPeriod)} PAT`);
    
    // 检查铸造权限
    const deployerIsMinter = await patToken.isMinter(deployer.address);
    console.log(`- 部署者铸造权限: ${deployerIsMinter ? '✅ 有权限' : '❌ 无权限'}`);
    
    console.log("\n=== 7. PXT代币对比 ===");
    
    // 获取PXT代币信息作为对比
    const pxtTotalSupply = await pxtToken.totalSupply();
    const deployerPxtBalance = await pxtToken.balanceOf(deployer.address);
    
    console.log("📊 PXT代币对比:");
    console.log(`- PXT总供应量: ${ethers.formatEther(pxtTotalSupply)} PXT`);
    console.log(`- 部署者PXT余额: ${ethers.formatEther(deployerPxtBalance)} PXT`);
    console.log(`- PAT/PXT供应比: ${(Number(patTotalSupply) / Number(pxtTotalSupply)).toFixed(2)}:1`);
    
    console.log("\n=== 8. 经济模型验证 ===");
    
    // 验证经济模型参数
    const expectedInitialSupply = ethers.parseEther("300000000"); // 3亿PAT
    const supplyMatchesExpected = patTotalSupply >= expectedInitialSupply;
    
    console.log("🏦 经济模型验证:");
    console.log(`- 预期初始供应量: ${ethers.formatEther(expectedInitialSupply)} PAT`);
    console.log(`- 实际供应量: ${ethers.formatEther(patTotalSupply)} PAT`);
    console.log(`- 供应量符合预期: ${supplyMatchesExpected ? '✅ 符合' : '❌ 不符合'}`);
    
    // 检查池子分配是否符合预期
    const expectedPoolAllocation = ethers.parseEther("300000000"); // 预期池子总分配
    const poolAllocationCorrect = totalPoolBalance >= expectedPoolAllocation * 90n / 100n; // 允许10%误差
    
    console.log(`- 预期池子分配: ${ethers.formatEther(expectedPoolAllocation)} PAT`);
    console.log(`- 实际池子分配: ${ethers.formatEther(totalPoolBalance)} PAT`);
    console.log(`- 池子分配正确: ${poolAllocationCorrect ? '✅ 正确' : '❌ 异常'}`);
    
    console.log("\n=== 9. 安全性检查 ===");
    
    // 检查合约安全性
    const contractOwner = await patToken.owner();
    const isPaused = await patToken.paused();
    
    console.log("🔒 安全性状态:");
    console.log(`- 合约所有者: ${contractOwner}`);
    console.log(`- 所有者正确: ${contractOwner === deployer.address ? '✅ 正确' : '❌ 异常'}`);
    console.log(`- 合约暂停状态: ${isPaused ? '⚠️ 已暂停' : '✅ 正常运行'}`);
    
    console.log("\n=== 10. 总结报告 ===");
    
    const allChecksPass = isConsistent && supplyMatchesExpected && poolAllocationCorrect && (contractOwner === deployer.address) && !isPaused;
    
    console.log("📋 验证结果总结:");
    console.log(`✅ 供应量一致性: ${isConsistent ? '通过' : '失败'}`);
    console.log(`✅ 经济模型符合: ${supplyMatchesExpected ? '通过' : '失败'}`);
    console.log(`✅ 池子分配正确: ${poolAllocationCorrect ? '通过' : '失败'}`);
    console.log(`✅ 权限配置正确: ${contractOwner === deployer.address ? '通过' : '失败'}`);
    console.log(`✅ 合约运行正常: ${!isPaused ? '通过' : '失败'}`);
    
    console.log("\n🎉 PAT代币分配验证完成！");
    console.log("================================================");
    console.log(`📊 总体状态: ${allChecksPass ? '✅ 全部通过' : '⚠️ 存在问题'}`);
    console.log(`💰 PAT总供应量: ${ethers.formatEther(patTotalSupply)} PAT`);
    console.log(`🏦 池子总分配: ${ethers.formatEther(totalPoolBalance)} PAT`);
    console.log(`👥 关键账户总持有: ${ethers.formatEther(totalKeyAccountBalance)} PAT`);
    
    if (!allChecksPass) {
        console.log("\n⚠️ 建议操作:");
        if (!isConsistent) console.log("- 检查是否存在未统计的持有者");
        if (!supplyMatchesExpected) console.log("- 验证初始供应量配置");
        if (!poolAllocationCorrect) console.log("- 检查池子分配逻辑");
        if (contractOwner !== deployer.address) console.log("- 验证合约所有权");
        if (isPaused) console.log("- 检查合约暂停原因");
    }
    
    // 保存验证报告
    const reportDir = path.join(__dirname, "../../test-reports");
    if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const verificationReport = {
        network: network.name,
        timestamp: new Date().toISOString(),
        patToken: {
            address: patToken.target,
            totalSupply: ethers.formatEther(patTotalSupply),
            totalMinted: ethers.formatEther(patTotalMinted),
            totalBurned: ethers.formatEther(patTotalBurned)
        },
        pools: Object.fromEntries(
            await Promise.all(
                Object.entries(pools).map(async ([name, info]) => [
                    name,
                    {
                        address: info.address,
                        balance: ethers.formatEther(await patToken.balanceOf(info.address)),
                        allocation: info.allocation
                    }
                ])
            )
        ),
        keyAccounts: await Promise.all(
            keyAccounts.map(async (account) => ({
                name: account.name,
                address: account.address,
                balance: ethers.formatEther(await patToken.balanceOf(account.address))
            }))
        ),
        verification: {
            supplyConsistent: isConsistent,
            economicModelCorrect: supplyMatchesExpected,
            poolAllocationCorrect: poolAllocationCorrect,
            ownershipCorrect: contractOwner === deployer.address,
            contractRunning: !isPaused,
            allChecksPass: allChecksPass
        }
    };
    
    const reportFile = path.join(reportDir, `pat-allocation-verification-${network.name}-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(verificationReport, null, 2));
    console.log(`📊 验证报告已保存: ${reportFile}`);
    
    return verificationReport;
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("PAT分配验证失败:", error);
            process.exit(1);
        });
}

module.exports = main;
