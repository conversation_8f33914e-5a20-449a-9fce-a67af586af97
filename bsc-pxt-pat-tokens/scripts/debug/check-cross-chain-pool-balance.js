const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
    console.log("🔍 跨链池状态详细检查");
    console.log("================================================");
    console.log("网络:", network.name);
    console.log("时间:", new Date().toISOString());
    
    const [deployer] = await ethers.getSigners();
    console.log("查询账户:", deployer.address);
    
    // 加载部署信息
    let coreDeployment, bridgeDeployment;
    try {
        const coreFile = path.join(__dirname, "../../deployments", network.name, "core-deployment.json");
        const bridgeFile = path.join(__dirname, "../../deployments", network.name, "bridge-deployment.json");
        
        coreDeployment = JSON.parse(fs.readFileSync(coreFile, 'utf8'));
        bridgeDeployment = JSON.parse(fs.readFileSync(bridgeFile, 'utf8'));
        console.log("✅ 已加载部署信息");
    } catch (error) {
        console.error("❌ 未找到部署文件");
        process.exit(1);
    }
    
    // 获取合约实例
    const PAToken = await ethers.getContractFactory("PAToken");
    const patToken = PAToken.attach(coreDeployment.contracts.PAToken.address);
    
    const PXToken = await ethers.getContractFactory("PXToken");
    const pxtToken = PXToken.attach(coreDeployment.contracts.PXToken.address);
    
    const TokenBridge = await ethers.getContractFactory("TokenBridge");
    const tokenBridge = TokenBridge.attach(bridgeDeployment.contracts.TokenBridge.address);
    
    console.log("\n=== 1. 跨链池基础信息 ===");
    
    const crossChainPoolAddress = coreDeployment.pools.crossChainPool.address;
    const crossChainPoolPrivateKey = coreDeployment.pools.crossChainPool.privateKey;
    
    console.log("📊 跨链池配置:");
    console.log(`- 池子地址: ${crossChainPoolAddress}`);
    console.log(`- 私钥配置: ${crossChainPoolPrivateKey ? '已配置' : '未配置'}`);
    
    // 创建跨链池钱包实例
    let crossChainPoolWallet;
    if (crossChainPoolPrivateKey) {
        crossChainPoolWallet = new ethers.Wallet(crossChainPoolPrivateKey, ethers.provider);
        console.log(`- 钱包地址验证: ${crossChainPoolWallet.address === crossChainPoolAddress ? '✅ 匹配' : '❌ 不匹配'}`);
    }
    
    console.log("\n=== 2. 跨链池余额详情 ===");
    
    // 检查各种代币余额
    const patBalance = await patToken.balanceOf(crossChainPoolAddress);
    const pxtBalance = await pxtToken.balanceOf(crossChainPoolAddress);
    const bnbBalance = await ethers.provider.getBalance(crossChainPoolAddress);
    
    console.log("💰 代币余额:");
    console.log(`- PAT余额: ${ethers.formatEther(patBalance)} PAT`);
    console.log(`- PXT余额: ${ethers.formatEther(pxtBalance)} PXT`);
    console.log(`- BNB余额: ${ethers.formatEther(bnbBalance)} BNB`);
    
    // 计算美元价值（假设价格）
    const patUsdValue = Number(ethers.formatEther(patBalance)) * 0.1; // 假设PAT = $0.1
    const pxtUsdValue = Number(ethers.formatEther(pxtBalance)) * 1.0; // 假设PXT = $1.0
    const bnbUsdValue = Number(ethers.formatEther(bnbBalance)) * 300; // 假设BNB = $300
    
    console.log("💵 估算价值 (假设价格):");
    console.log(`- PAT价值: ~$${patUsdValue.toFixed(2)} (假设$0.1/PAT)`);
    console.log(`- PXT价值: ~$${pxtUsdValue.toFixed(2)} (假设$1.0/PXT)`);
    console.log(`- BNB价值: ~$${bnbUsdValue.toFixed(2)} (假设$300/BNB)`);
    console.log(`- 总价值: ~$${(patUsdValue + pxtUsdValue + bnbUsdValue).toFixed(2)}`);
    
    console.log("\n=== 3. 跨链池授权状态 ===");
    
    // 检查跨链池对桥接合约的授权
    const patAllowance = await patToken.allowance(crossChainPoolAddress, bridgeDeployment.contracts.TokenBridge.address);
    const pxtAllowance = await pxtToken.allowance(crossChainPoolAddress, bridgeDeployment.contracts.TokenBridge.address);
    
    console.log("🔐 授权状态:");
    console.log(`- PAT授权给桥接合约: ${ethers.formatEther(patAllowance)} PAT`);
    console.log(`- PXT授权给桥接合约: ${ethers.formatEther(pxtAllowance)} PXT`);
    
    const patAuthSufficient = patAllowance >= patBalance;
    const pxtAuthSufficient = pxtAllowance >= pxtBalance;
    
    console.log("✅ 授权充足性:");
    console.log(`- PAT授权充足: ${patAuthSufficient}`);
    console.log(`- PXT授权充足: ${pxtAuthSufficient}`);
    
    console.log("\n=== 4. 跨链能力评估 ===");
    
    // 评估可执行的跨链操作
    const minBridgeAmount = ethers.parseEther("100"); // 最小跨链金额
    const maxBridgeAmount = ethers.parseEther("10000"); // 最大跨链金额
    
    console.log("🌉 跨链能力评估:");
    
    // PAT跨链能力
    const patCanBridgeMin = patBalance >= minBridgeAmount;
    const patCanBridgeMax = patBalance >= maxBridgeAmount;
    const patMaxBridgeable = patBalance;
    
    console.log(`- PAT最小跨链(100): ${patCanBridgeMin ? '✅ 可以' : '❌ 不足'}`);
    console.log(`- PAT大额跨链(10K): ${patCanBridgeMax ? '✅ 可以' : '❌ 不足'}`);
    console.log(`- PAT最大可跨链: ${ethers.formatEther(patMaxBridgeable)} PAT`);
    
    // 估算跨链费用
    try {
        const bridgeFee1K = await tokenBridge.calculateBridgeFee(327, ethers.parseEther("1000"));
        const bridgeFee10K = await tokenBridge.calculateBridgeFee(327, ethers.parseEther("10000"));
        
        console.log("💸 跨链费用估算:");
        console.log(`- 1K PAT跨链费用: ${ethers.formatEther(bridgeFee1K)} BNB`);
        console.log(`- 10K PAT跨链费用: ${ethers.formatEther(bridgeFee10K)} BNB`);
        
        const canAfford1K = bnbBalance >= bridgeFee1K;
        const canAfford10K = bnbBalance >= bridgeFee10K;
        
        console.log("💰 费用承担能力:");
        console.log(`- 可承担1K跨链: ${canAfford1K ? '✅ 可以' : '❌ BNB不足'}`);
        console.log(`- 可承担10K跨链: ${canAfford10K ? '✅ 可以' : '❌ BNB不足'}`);
        
    } catch (error) {
        console.log("⚠️  无法计算跨链费用:", error.message);
    }
    
    console.log("\n=== 5. 历史交易分析 ===");
    
    // 获取最近的转账事件
    try {
        const currentBlock = await ethers.provider.getBlockNumber();
        const fromBlock = Math.max(0, currentBlock - 10000); // 最近10000个区块
        
        console.log("📈 交易历史分析:");
        console.log(`- 查询区块范围: ${fromBlock} - ${currentBlock}`);
        
        // 查询PAT转入事件
        const patTransferFilter = patToken.filters.Transfer(null, crossChainPoolAddress);
        const patTransferEvents = await patToken.queryFilter(patTransferFilter, fromBlock);
        
        // 查询PAT转出事件
        const patTransferOutFilter = patToken.filters.Transfer(crossChainPoolAddress, null);
        const patTransferOutEvents = await patToken.queryFilter(patTransferOutFilter, fromBlock);
        
        console.log(`- PAT转入交易: ${patTransferEvents.length} 笔`);
        console.log(`- PAT转出交易: ${patTransferOutEvents.length} 笔`);
        
        // 计算总转入转出金额
        let totalPatIn = 0n;
        let totalPatOut = 0n;
        
        patTransferEvents.forEach(event => {
            totalPatIn += event.args.value;
        });
        
        patTransferOutEvents.forEach(event => {
            totalPatOut += event.args.value;
        });
        
        console.log(`- PAT总转入: ${ethers.formatEther(totalPatIn)} PAT`);
        console.log(`- PAT总转出: ${ethers.formatEther(totalPatOut)} PAT`);
        console.log(`- PAT净流入: ${ethers.formatEther(totalPatIn - totalPatOut)} PAT`);
        
    } catch (error) {
        console.log("⚠️  无法分析历史交易:", error.message);
    }
    
    console.log("\n=== 6. 健康状态评估 ===");
    
    // 综合健康评估
    const healthChecks = {
        hasPatBalance: patBalance > 0,
        hasBnbForFees: bnbBalance > ethers.parseEther("0.1"),
        hasProperAuth: patAuthSufficient,
        canBridgeMin: patBalance >= minBridgeAmount,
        walletConfigured: !!crossChainPoolPrivateKey
    };
    
    const healthScore = Object.values(healthChecks).filter(check => check).length;
    const totalChecks = Object.keys(healthChecks).length;
    
    console.log("🏥 健康状态评估:");
    console.log(`- 健康评分: ${healthScore}/${totalChecks} (${Math.round(healthScore/totalChecks*100)}%)`);
    
    Object.entries(healthChecks).forEach(([check, passed]) => {
        const status = passed ? '✅' : '❌';
        const checkName = check.replace(/([A-Z])/g, ' $1').toLowerCase();
        console.log(`- ${checkName}: ${status}`);
    });
    
    console.log("\n=== 7. 建议操作 ===");
    
    if (healthScore === totalChecks) {
        console.log("🎉 跨链池状态完美！");
        console.log("🚀 建议操作:");
        console.log("   - 可以执行小额跨链测试 (100-1000 PAT)");
        console.log("   - 可以执行大额跨链操作 (1000+ PAT)");
    } else {
        console.log("⚠️  跨链池需要优化:");
        
        if (!healthChecks.hasPatBalance) {
            console.log("   - 需要向跨链池充值PAT代币");
        }
        if (!healthChecks.hasBnbForFees) {
            console.log("   - 需要向跨链池充值BNB作为Gas费用");
        }
        if (!healthChecks.hasProperAuth) {
            console.log("   - 需要授权PAT代币给桥接合约");
        }
        if (!healthChecks.canBridgeMin) {
            console.log("   - PAT余额不足，无法执行最小跨链操作");
        }
        if (!healthChecks.walletConfigured) {
            console.log("   - 跨链池私钥未配置");
        }
    }
    
    // 保存检查报告
    const reportData = {
        network: network.name,
        timestamp: new Date().toISOString(),
        crossChainPool: {
            address: crossChainPoolAddress,
            balances: {
                pat: ethers.formatEther(patBalance),
                pxt: ethers.formatEther(pxtBalance),
                bnb: ethers.formatEther(bnbBalance)
            },
            authorizations: {
                patAllowance: ethers.formatEther(patAllowance),
                pxtAllowance: ethers.formatEther(pxtAllowance)
            },
            healthChecks: healthChecks,
            healthScore: `${healthScore}/${totalChecks}`
        }
    };
    
    const reportDir = path.join(__dirname, "../../test-reports");
    if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const reportFile = path.join(reportDir, `cross-chain-pool-status-${network.name}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(reportData, null, 2));
    
    console.log(`\n📊 详细报告已保存: ${reportFile}`);
    console.log("================================================");
    
    return reportData;
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("检查失败:", error);
            process.exit(1);
        });
}

module.exports = main;
