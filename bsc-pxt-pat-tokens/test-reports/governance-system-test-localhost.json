{"network": "localhost", "timestamp": "2025-08-05T02:22:16.557Z", "testResults": {"dualGovernance": "✅ 通过", "treasury": "✅ 通过", "proposalManager": "✅ 通过", "votingWeights": "✅ 通过", "proposalCreation": "✅ 通过", "daoContract": "✅ 通过"}, "contracts": {"StakingGovernance": "0x62cAD11248876091000E0F01704F43bd24c7f7F7", "Voting": "0xbD1955775302b8fDf7F2FA303356A5b6a9fceDDD", "ProposalManager": "0x036D812EBa1811Fc504A0Ff3De76cF81d2F79603", "DAO": "0x8579D33815880Ecda0b0808d3b1e93287b7eABB3", "Treasury": "0x9eE35b0Be3AADFBBf97Cd9122a3f0D5212e47Bf7"}, "configuration": {"proposalThreshold": "1000.0 PXT", "quorumThreshold": "40%", "votingDelay": "1 天", "votingPeriod": "7 天", "executionDelay": "2 天", "gracePeriod": "14 天", "timelockDelay": "24 小时"}, "summary": "双重治理架构功能完整，质押治理 + 传统治理协同工作"}