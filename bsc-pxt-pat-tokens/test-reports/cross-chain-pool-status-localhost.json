{"network": "localhost", "timestamp": "2025-08-05T04:02:48.312Z", "crossChainPool": {"address": "******************************************", "balances": {"pat": "99997000.0", "pxt": "0.0", "bnb": "1.7"}, "authorizations": {"patAllowance": "115792089237316195423570985008687907853269984665640564039457.584007913129639935", "pxtAllowance": "0.0"}, "healthChecks": {"hasPatBalance": true, "hasBnbForFees": true, "hasProperAuth": true, "canBridgeMin": true, "walletConfigured": true}, "healthScore": "5/5"}}