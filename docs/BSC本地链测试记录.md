j<PERSON><PERSON><PERSON><PERSON><PERSON>@jzy local-bsc-chain % ./start-interactive.sh
🚀 启动BSC链（交互式）...
🧹 清理旧数据...
🔑 生成验证者节点密钥...


[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = ******************************************
BLS Public key       = 0x8839128fb0d439f37817f67b0d7f067e5da88f9423680f095310210bb127d430ca47affdfff3e1c3ac77e344afc53f5e
Node ID              = 16Uiu2HAmGfBa54BmDswtbGVUeRDfzXMrp8eYBdszExnoEL4jD7LE



[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = 0xAC4DF8ac06045915a959ac0f0B2714e439cCf6C1
BLS Public key       = 0xae8b1a2611d5a8b2f7f373d7ca7d191c18e3b2a52bad375fa367d5ffd42aa6ddc1b2d83716e66638b50fab985bdfbd0c
Node ID              = 16Uiu2HAmE4Ma3rgMMLfw5EJVF6UdZd25nd66wNnisfrrN3pWdPvs



[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = 0xE00CEbaBCEb6464d81f4D7b479CA1586f409c5eC
BLS Public key       = 0x81eb635bd1d07d687b100841ac362757fc2493d28e06cdae47fa44c4715fada0199ba697ff9202d60cc99d7dd3e0faa8
Node ID              = 16Uiu2HAmBHoRMdzg86bNyx83mGfuLjD4LHT3mRoSYLThhsokRHqP



[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = 0x966A3E94137AF55f66B7A2b76E4eCF2E7D7Da912
BLS Public key       = 0x8ad4d3948e369d36eac04bdb37bb8b7036579fc878085f201bd55893b7a7d2390a8fbcfd7470b6297d1f4002891874cb
Node ID              = 16Uiu2HAkxSQmR9fkPnzxrMK3sQey6aiTDqLcgcS35v7dvtvps7F9

🔑 获取节点、公钥和私钥...
✅ 节点ID: 16Uiu2HAmGfBa54BmDswtbGVUeRDfzXMrp8eYBdszExnoEL4jD7LE
✅ 预分配地址 (公钥): ******************************************
🔑 私钥 (用于MetaMask导入): 31a2cdab06d5094e3676849126d28da83567603795154ccfb9f981d1bccf398b
   [警告] 这是一个开发密钥，请勿在主网使用!
🔧 更新主.env文件中的部署者私钥...
✅ 已更新主.env文件中的DEPLOYER_PRIVATE_KEY
✅ 已创建bsc-local.env文件
💡 提示: BSC本地链私钥已同步到主.env文件，hardhat将使用正确的私钥
📜 创建创世文件...
✅ 创世文件已创建（包含预分配余额）
🔥 启动BSC节点...
▶️ 启动主节点...
▶️ 启动节点2...
▶️ 启动节点3...
▶️ 启动节点4...
✅ 所有节点已启动
🌐 主节点 RPC: http://127.0.0.1:18545
💰 预分配地址: ******************************************
⏳ 等待链启动...
🧪 测试链状态...
✅ 链状态正常，当前区块高度: 0x3
💰 检查预分配账户余额...
💰 预分配账户余额: 10000.0000 BNB
====================================
🎉 BSC链启动成功！
====================================
MetaMask网络配置:
网络名称: BSC Local Chain
RPC URL: http://127.0.0.1:18545
链ID: 97
货币符号: BNB
====================================
🛑 停止链: kill `cat bsc-chain.pid` 或按Ctrl+C
====================================
按Enter键停止链，或按Ctrl+C后台运行...


junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/01-deploy-core-tokens.js --network localhost

=== 开始部署核心代币系统 ===
网络: localhost
链ID: 97
部署账户: ******************************************
国库账户: ******************************************
操作员账户: ******************************************
部署账户余额: 10000.0 ETH

=== 生成池子地址 ===
中国大陆池子地址: ******************************************
国际池子地址: ******************************************
质押池地址: ******************************************
跨链池地址: ******************************************

=== 1. 部署 PXT 治理代币 ===
预估Gas消耗: 5144437
✅ PXT代币部署成功: ******************************************
- 名称: Paper x Token, 符号: PXT, 总供应量: 100000000.0

=== 2. 部署 PAT 功能代币 ===
✅ PAT代币部署成功: ******************************************
- 名称: Paper Author Token, 符号: PAT, 总供应量: 300000000.0

=== 3. 部署代币注册表 ===
✅ 代币注册表部署成功: ******************************************
注册表合约owner: ******************************************
是否为部署者: true

=== 4. 配置代币注册表 ===
正在添加工厂权限...
✅ 部署者已添加为工厂
验证工厂权限: true

=== 5. 注册代币到注册表 ===
注册PXT代币:
- 地址: ******************************************
- 信息: Paper x Token (PXT), 100000000.0 总量
✅ PXT代币已注册
注册PAT代币:
- 地址: ******************************************
- 信息: Paper Author Token (PAT), 300000000.0 总量
✅ PAT代币已注册

=== 6. 验证部署结果 ===
📊 代币供应量验证:
- PXT总供应量: 100000000.0 PXT
- PAT总供应量: 300000000.0 PAT
- 部署者PXT余额: 55002000.0 PXT
- 部署者PAT余额: 0.0 PAT
📋 注册表验证:
- 总代币数: 2
- PX代币数: 1
- PA代币数: 1
💰 池子余额验证:
- 中国大陆池: 100000000.0 PAT
- 国际池: 100000000.0 PAT
- 跨链池: 100000000.0 PAT

=== 7. 保存部署信息 ===

=== 🎉 核心代币系统部署成功 ===
部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/core-deployment.json

📋 部署摘要:
✅ PXT治理代币: ******************************************
✅ PAT功能代币: ******************************************
✅ 代币注册表: ******************************************
✅ 4个资金池地址已生成

🔒 安全特性:
- 池子地址采用确定性生成，无私钥泄露风险
- 团队代币锁定40个季度
- 基于OpenZeppelin的访问控制

junziliuyi@jzy bsc-pxt-pat-tokens % 
junziliuyi@jzy bsc-pxt-pat-tokens % clear
junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/02-deploy-staking-system.js --network localhost
=== 开始部署完整质押系统 ===
网络: localhost
链ID: 97
部署账户: ******************************************
国库账户: ******************************************
操作员账户: ******************************************
✅ 已加载核心合约地址
- PXT代币: ******************************************
- PAT代币: ******************************************
- 代币注册表: ******************************************

=== 1. 部署质押保险库 ===
✅ 质押保险库部署成功: 0x305DA3Ba8373Ef53f7EaC0e01566B2e82F01C38c

=== 2. 部署质押池 ===
✅ 质押池部署成功: ******************************************

=== 3. 部署奖励分配器 ===
✅ 奖励分配器部署成功: 0xaAAD792f06a0C2427569272590D23d7E254D527b

=== 4. 部署质押工厂 ===
✅ 质押工厂部署成功: 0x22B3b0F2c1b053071E20EFB3A83D4c7d6BA3DF6D

=== 5. 部署质押奖励合约 ===
✅ 质押奖励合约部署成功: 0x22dc74D4E24E4360c8C8d063De15e2E113800f62

=== 6. 部署质押治理合约 ===
✅ 质押治理合约部署成功: ******************************************

=== 7. 部署质押分析合约 ===
✅ 质押分析合约部署成功: ******************************************

=== 8. 初始化质押池 ===
✅ 质押池初始化完成

=== 9. 配置保险库 ===
✅ 保险库配置完成

=== 10. 配置奖励系统 ===
✅ 质押池已添加到奖励分配器
✅ 奖励池配置完成

=== 11. 配置工厂合约 ===
✅ 工厂合约配置完成

=== 12. 配置权限系统 ===
✅ 保险库守护者配置完成
✅ 治理权限配置完成
✅ 分析权限配置完成

=== 13. 验证质押系统 ===
最小质押金额: 1.0 PXT
基础年化收益率: 0 基点
当前总质押量: 0.0 PXT
保险库总余额: 0.0 代币
治理提案数量: 0

=== 14. 质押等级配置 ===
- 丁级: 100.0 PXT (倍数: 120%)
- 丙级: 1000.0 PXT (倍数: 130%)
- 乙级: 5000.0 PXT (倍数: 150%)
- 甲级: 20000.0 PXT (倍数: 190%)
- 十绝: 100000.0 PXT (倍数: 225%)
- 双十绝: 250000.0 PXT (倍数: 300%)
- 至尊: 500000.0 PXT (倍数: 450%)

=== 🎉 完整质押系统部署成功 ===
部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/staking-deployment.json

📋 部署摘要:
✅ 核心合约: StakingPool, RewardDistributor, StakingFactory
✅ 增强合约: StakingRewards, StakingGovernance, StakingAnalytics
✅ 安全合约: StakingVault
✅ 权限配置: 多重签名、治理权限、数据权限
✅ 系统验证: 所有合约正常工作

📊 合约地址:
- StakingVault: 0x305DA3Ba8373Ef53f7EaC0e01566B2e82F01C38c
- StakingPool: ******************************************
- RewardDistributor: 0xaAAD792f06a0C2427569272590D23d7E254D527b
- StakingFactory: 0x22B3b0F2c1b053071E20EFB3A83D4c7d6BA3DF6D
- StakingRewards: 0x22dc74D4E24E4360c8C8d063De15e2E113800f62
- StakingGovernance: ******************************************
- StakingAnalytics: ******************************************

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/03-deploy-governance.js --network localhost

=== 开始部署增强治理系统 ===
网络: localhost
链ID: 97
部署账户: ******************************************
国库账户: ******************************************
操作员账户: ******************************************
部署账户余额: 10000.0 ETH
✅ 已加载依赖合约地址
- PXT代币: ******************************************
- PAT代币: ******************************************
- 质押池: ******************************************
- 质押治理: ******************************************

=== 1. 检查现有质押治理合约 ===
✅ 发现已部署的质押治理合约: ******************************************
ℹ️  质押治理合约已包含基础治理功能

=== 2. 部署投票合约 ===
预估Gas消耗: 2001981
✅ 投票合约部署成功: ******************************************
✅ 投票合约初始化完成

=== 3. 部署提案管理器 ===
✅ 提案管理器部署成功: ******************************************
✅ 提案管理器初始化完成

=== 4. 部署DAO主合约 ===
✅ DAO主合约部署成功: ******************************************
✅ DAO主合约初始化完成

=== 5. 部署国库合约 ===
✅ 国库合约部署成功: ******************************************

=== 6. 配置治理系统关系 ===
✅ Treasury DAO地址更新完成
✅ 提案管理器配置完成
✅ 权限配置完成

=== 7. 验证治理系统 ===
📊 投票合约验证:
- PXT代币地址: ******************************************
- 质押池地址: ******************************************
- 最低参与率: 10%
📊 DAO合约验证:
- 国库地址: ******************************************
📊 国库合约验证:
- DAO地址: ******************************************
- ETH余额: 0.0 ETH

=== 8. 保存部署信息 ===

=== 🎉 增强治理系统部署成功 ===
部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/governance-deployment.json

📋 部署摘要:
✅ 传统治理合约: Voting, ProposalManager, DAO, Treasury
✅ 质押治理合约: StakingGovernance (已存在)
🔗 双重治理架构: 质押治理 + 传统治理
✅ 合约关系配置: 所有合约正确关联
✅ 系统验证: 所有合约正常工作

🏛️ 治理特性:
- 基于质押权重的投票系统
- 多类型提案支持 (参数/资金/升级/成员/紧急)
- 时间锁保护的执行机制
- 多重签名保护的国库管理

📊 合约地址:
- StakingGovernance: ******************************************
- Voting: ******************************************
- ProposalManager: ******************************************
- DAO: ******************************************
- Treasury: ******************************************

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/04-deploy-content-system.js --network localhost

=== 开始部署内容上链系统 ===
网络: localhost
链ID: 97
部署账户: ******************************************
国库账户: ******************************************
操作员账户: ******************************************
部署者余额: 10000.0 ETH
✅ 已加载依赖合约地址
- PAT代币: ******************************************
- 国库合约: ******************************************

=== 1. 部署ContentRegistry合约 ===
预估Gas消耗: 3878218
正在部署ContentRegistry...
✅ ContentRegistry部署成功: ******************************************
验证ContentRegistry配置...
- PAT代币地址: ******************************************
- 国库地址: ******************************************
✅ ContentRegistry配置验证通过

=== 2. 部署ContentCharacter合约 ===
正在部署ContentCharacter...
✅ ContentCharacter部署成功: ******************************************
- NFT名称: PXPAT Character
- NFT符号: PXPAT-CHAR

=== 3. 部署ContentMint合约 ===
正在部署ContentMint...
✅ ContentMint部署成功: ******************************************
验证ContentMint配置...
- ContentRegistry地址: ******************************************
- 国库地址: ******************************************
- NFT名称: PXPAT Content NFT
- NFT符号: PXPAT-CONTENT
✅ ContentMint配置验证通过

=== 4. 测试基础功能 ===
📊 测试内容类型费用...
- 视频费用: 1.0 PAT
- 文章费用: 0.05 PAT
- 小说费用: 0.1 PAT
✅ 内容类型费用查询正常
📊 测试统计信息...
- 总内容数: 0
- 活跃内容数: 0
- 总PAT消耗: 0.0 PAT
- 总铸造次数: 0
✅ 统计信息查询正常
📊 测试内容类型管理...
- 总内容类型数: 8
- 活跃内容类型数: 8
- 支持的内容类型: video, novel, short_drama, anime, manga, music, article, short_video
✅ 内容类型管理正常

=== 5. 配置合约关系 ===
检查合约配置需求...
配置操作员权限...
✅ 权限配置完成
验证合约部署状态...
- ContentRegistry所有者: ******************************************
- ContentMint所有者: ******************************************
✅ 合约关系验证通过

=== 6. 生成验证命令 ===
📋 合约验证命令:
- ContentRegistry: npx hardhat verify --network bscTestnet ****************************************** "******************************************" "******************************************"
- ContentCharacter: npx hardhat verify --network bscTestnet ******************************************
- ContentMint: npx hardhat verify --network bscTestnet ****************************************** "******************************************" "******************************************"

=== 7. 保存部署信息 ===
✅ 部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/content-deployment.json

=== 🎉 内容上链系统部署成功 ===

📋 部署摘要:
✅ 内容注册合约: ContentRegistry
✅ 创作者身份NFT: ContentCharacter
✅ 内容铸造NFT: ContentMint
✅ 合约关系配置: 所有合约正确关联
✅ 功能验证: 基础功能正常工作

🌟 系统特性:
- IPFS + 链上元数据的混合存储
- 支持视频、小说、文章等多种内容类型
- 创作者身份Character NFT系统
- 内容NFT铸造和交易功能
- PAT代币经济激励机制

📊 合约地址:
- ContentRegistry: ******************************************
- ContentCharacter: ******************************************
- ContentMint: ******************************************

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/deploy/05-deploy-token-bridge.js --network localhost

🌉 开始部署BSC端TokenBridge合约
================================================
网络: localhost
链ID: 97
时间: 2025-08-05T01:41:22.849Z
部署账户: ******************************************
国库账户: ******************************************
操作员账户: ******************************************
✅ 已加载核心代币地址
PXT代币地址: ******************************************
PAT代币地址: ******************************************

=== 1. 部署TokenBridge合约 ===
✅ TokenBridge部署成功: 0xB783112ad496Bf6C6b6902Dee2195fC2cDd1017b

=== 2. 设置管理员权限 ===
✅ 已设置部署者为管理员

=== 3. 配置PXA链支持 ===
✅ 已设置PXA链名称: PXA
正在添加PXA链支持...
等待交易确认...
交易确认成功，区块: 508
✅ 已添加PXA链支持
  链ID: 327
  验证者数量: 3
  需要确认数: 2
  基础费用: 0.001 BNB
  百分比费用: 0.5 %
  最小费用: 0.0005 BNB
  最大费用: 0.1 BNB

=== 4. 验证配置 ===
PXA链支持状态: ✅ 支持
PXA链验证者数量: 3
验证者地址:
  1. ******************************************
  2. ******************************************
  3. ******************************************
PXA链费用配置:
  基础费用: 0.001 BNB
  百分比费用: 0.5 %
  最小费用: 0.0005 BNB
  最大费用: 0.1 BNB

=== 部署完成 ===
部署信息已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/deployments/localhost/bridge-deployment.json

📋 重要信息:
TokenBridge地址: 0xB783112ad496Bf6C6b6902Dee2195fC2cDd1017b
支持的目标链: PXA (链ID: 327)
验证者数量: 3
需要确认数: 2
费用接收地址: ******************************************

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/bridge/setup-bridge-connection.js --network localhost

🔗 设置BSC-PXA跨链桥连接
================================================
网络: localhost
链ID: 97
时间: 2025-08-05T01:42:01.441Z
部署者地址: ******************************************
国库地址: ******************************************
操作员地址: ******************************************
✅ 已加载部署信息

📝 合约地址:
- PAT代币: ******************************************
- TokenBridge: 0xB783112ad496Bf6C6b6902Dee2195fC2cDd1017b

=== 1. 检查桥接合约状态 ===
PXA链支持状态: ✅ 支持
PXA链验证者数量: 3
验证者列表:
  1. ******************************************
  2. ******************************************
  3. ******************************************

=== 2. 检查费用配置 ===
跨链费用示例 (1000 PAT): 0.1 BNB
费用配置:
- 基础费用: 0.001 BNB
- 百分比费用: 50 基点
- 最小费用: 0.0005 BNB
- 最大费用: 0.1 BNB

=== 3. 检查代币支持 ===
PAT代币地址: ******************************************
✅ PAT代币跨链支持: 默认支持（合约内置）
合约PAT地址: ******************************************
地址匹配: ✅ 匹配

=== 4. 准备跨链池资金 ===
跨链池地址: ******************************************
跨链池PAT余额: 100000000.0 PAT
跨链池BNB余额: 0.0 BNB

💰 跨链池BNB余额不足，正在转账...
转账金额: 2.0 BNB
✅ BNB转账成功，跨链池新余额: 2.0 BNB

=== 5. 桥接连接状态总结 ===
🎉 BSC-PXA跨链桥连接配置完成!
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 配置状态:
✅ PXA链支持: 已启用
✅ PAT代币支持: 已启用
✅ 验证者配置: 3 个验证者
✅ 费用配置: 已设置
✅ 跨链池资金: 已准备
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📋 PXA链操作命令:
cd ../pxa-chain/local-chain
./local-chain-setup.sh
cd ..
npx hardhat run scripts/bridge/setup-bsc-local-bridge.js --network localhost

⚠️ 重要提示:
- 确保PXA链正常运行后再执行跨链操作
- 跨链需要PXA链验证者确认，通常需要几分钟
- 建议先用小额测试跨链功能

jzy:bsc-pxt-pat-tokens junziliuyi$ npx hardhat run scripts/test/core-system-test.js --network localhost
=== 开始测试核心代币系统 ===
网络: localhost
✅ 已加载部署信息
测试账户: ******************************************

=== 1. 基础信息验证 ===
PXT代币信息:
- 名称: Paper x Token
- 符号: PXT
- 总供应量: 100000000.0 PXT
- 部署者余额: 55001900.0 PXT

PAT代币信息:
- 名称: Paper Author Token
- 符号: PAT
- 总供应量: 300001000.0 PAT
- 部署者余额: 0.0 PAT

=== 2. 代币注册表验证 ===
注册表统计:
- 总代币数: 2
- PX代币数: 1
- PA代币数: 1

=== 3. 转账功能测试 ===
转账测试: 向 ****************************************** 转账 100.0 PXT
✅ 转账成功，用户余额: 15000200.0 PXT
✅ 授权成功，授权额度: 100.0 PXT

=== 4. 锁定功能测试 ===
锁定测试: 锁定 50.0 PXT，解锁时间: 2025/8/6 09:44:45
✅ 锁定成功，锁定余额: 100.0 PXT
锁定记录数: 2

=== 5. PAT代币铸造测试 ===
部署者是否为铸造者: true
铸造测试: 向 ****************************************** 铸造 1000.0 PAT
✅ 铸造成功，用户PAT余额: 2000.0 PAT

=== 6. 池子余额验证 ===
中国大陆池: 100000000.0 PAT
国际池: 100000000.0 PAT
质押池: 0.0 PAT
跨链池（到PXPAC）: 100000000.0 PAT

=== 7. 通胀机制测试 ===
通胀率: 150 (基点)
总铸造量: 300002000.0 PAT
总销毁量: 0.0 PAT
部署者是否为铸造者: true
执行通胀铸造测试...
✅ 通胀铸造执行完成
铸造后总量: 300002093.750625 PAT
✅ 通胀机制测试完成

=== 8. 权限系统测试 ===
PXT合约所有者: ******************************************
PXT合约是否暂停: false
PAT合约所有者: ******************************************
PAT合约是否暂停: false
注册表所有者: ******************************************
部署者是否为工厂: true

=== 🎉 核心代币系统测试完成 ===
所有基础功能正常工作！
测试报告已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/test-reports/core-system-test-localhost.json

junziliuyi@jzy bsc-pxt-pat-tokens % npx hardhat run scripts/test/core-system-test.js --network localhost
=== 开始测试核心代币系统 ===
网络: localhost
✅ 已加载部署信息
测试账户: ******************************************

=== 1. 基础信息验证 ===
PXT代币信息:
- 名称: Paper x Token
- 符号: PXT
- 总供应量: 100000000.0 PXT
- 部署者余额: 55001800.0 PXT

PAT代币信息:
- 名称: Paper Author Token
- 符号: PAT
- 总供应量: 300002093.750625 PAT
- 部署者余额: 93.750625 PAT

=== 2. 代币注册表验证 ===
注册表统计:
- 总代币数: 2
- PX代币数: 1
- PA代币数: 1

=== 3. 转账功能测试 ===
转账测试: 向 ****************************************** 转账 100.0 PXT
✅ 转账成功，用户余额: 14999900.0 PXT
✅ 授权成功，授权额度: 100.0 PXT

=== 4. 锁定功能测试 ===
锁定测试: 锁定 50.0 PXT，解锁时间: 2025/8/6 10:08:45
✅ 锁定成功，锁定余额: 150.0 PXT
锁定记录数: 3

=== 5. PAT代币铸造测试 ===
部署者是否为铸造者: true
铸造测试: 向 ****************************************** 铸造 1000.0 PAT
✅ 铸造成功，用户PAT余额: 3000.0 PAT

=== 6. 池子余额验证 ===
中国大陆池: 100000000.0 PAT
国际池: 100000000.0 PAT
质押池: 0.0 PAT
跨链池（到PXPAC）: 100000000.0 PAT

=== 7. 通胀机制测试 ===
通胀率: 150 (基点)
总铸造量: 300003093.750625 PAT
总销毁量: 0.0 PAT
部署者是否为铸造者: true
执行通胀铸造测试...
✅ 通胀铸造执行完成
铸造后总量: 300003299.232196062071917808 PAT
✅ 通胀机制测试完成

=== 8. 权限系统测试 ===
PXT合约所有者: ******************************************
PXT合约是否暂停: false
PAT合约所有者: ******************************************
PAT合约是否暂停: false
注册表所有者: ******************************************
部署者是否为工厂: true

=== 🎉 核心代币系统测试完成 ===
所有基础功能正常工作！
测试报告已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/test-reports/core-system-test-localhost.json

jzy:bsc-pxt-pat-tokens junziliuyi$ npx hardhat run scripts/test/governance-system-test.js --network localhost
=== 开始测试增强治理系统 ===
网络: localhost
测试账户: ******************************************
✅ 已加载部署信息

=== 1. 双重治理架构验证 ===
📊 质押治理配置:
- 投票延迟: 1 天
- 投票期间: 7 天
- 提案门槛: 1000.0 PXT
- 法定人数: 40%
- 紧急模式: false
📊 传统治理配置:
- 投票代币: ******************************************
- 质押池: ******************************************
- 最低参与率: 10%
✅ 双重治理架构验证通过

=== 2. 国库系统测试 ===
💰 国库状态:
- DAO合约地址: ******************************************
- ETH余额: 0.0 ETH
- PAT余额: 0.0 PAT
- 支出记录数: 0
- 国库所有者: ******************************************
✅ 国库系统验证通过

=== 3. 提案管理系统测试 ===
📋 提案总数: 0
📋 支持的提案类型:
- PARAMETER (0)
- FUNDING (1)
- UPGRADE (2)
- MEMBERSHIP (3)
- EMERGENCY (4)
✅ 提案管理系统验证通过

=== 4. 投票权重系统测试 ===
📊 投票权重测试:
- 部署者投票权重: 0.0 PXT
📊 等级投票权重:
- 丁级: 1x
- 丙级: 2x
- 乙级: 3x
- 甲级: 5x
- 十绝: 7x
- 双十绝: 9x
- 至尊: 10x
✅ 投票权重系统验证通过

=== 5. 创建测试提案 ===
⚠️ 用户投票权重不足，无法创建提案
- 需要: 1000.0 PXT
- 当前: 0.0 PXT

=== 6. DAO主合约测试 ===
🏛️ DAO合约配置:
📋 实际配置 (contracts mapping):
- PXT代币: ******************************************
- PAT代币: ******************************************
- 质押池: ******************************************
- 提案管理器: ******************************************
📋 公共变量 (可能为零地址):
- 国库地址: ******************************************
- 提案管理器: ******************************************

📊 配置验证:
- PXT代币匹配: true
- PAT代币匹配: true
- 质押池匹配: true
- 提案管理器匹配: true
✅ DAO合约配置完全正确

ℹ️  说明：公共变量为零地址是合约设计问题
   实际配置存储在contracts mapping中，功能正常
- 活跃成员数: 0
- 总成员数: undefined
- 国库ETH余额: 0.0 ETH
✅ DAO主合约验证通过

=== 🎉 增强治理系统测试完成 ===
测试报告已保存到: /Users/<USER>/Desktop/PXA/bsc-pxt-pat-tokens/test-reports/governance-system-test-localhost.json

🏛️ 双重治理系统包含5个核心合约，功能完整！
📊 质押治理 + 传统治理 = 完整的去中心化治理生态
jzy:bsc-pxt-pat-tokens junziliuyi$ 

jzy:bsc-pxt-pat-tokens junziliuyi$ PINATA_JWT=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZY3Opz1JUuPf-XtgMS9u56Q9iTz_MBV64_-hB903dNU IPFS_GATEWAY_URL=https://gateway.pinata.cloud npx hardhat run scripts/test/bsc-ipfs-content-upload.js --network localhost
🌐 开始BSC链IPFS内容上链测试
================================================
网络: localhost
时间: 2025-08-05T02:56:44.853Z
测试账户:
- 部署者: ******************************************
- 用户1: ******************************************
✅ 已加载部署信息
- PAT代币: ******************************************
- 内容注册器: ******************************************
- 内容角色: ******************************************
- 内容铸造: ******************************************

=== 1. 准备测试环境 ===
用户1 PAT余额: 2996.0 PAT
执行无上限PAT授权...
✅ 无上限授权完成
✅ 用户已有内容创作者角色，ID: 1

=== 2. 准备IPFS内容 ===
📤 开始真实IPFS上传到Pinata...
✅ Pinata配置检查通过
- 使用网关: https://gateway.pinata.cloud
- 认证方式: JWT Token
📤 上传元数据到Pinata IPFS...
🎉 真实IPFS上传成功!
- IPFS哈希: bafkreibpp4iwxkdvcyfsdehhcciza5nvwlmprabakp4humxwzjybmmn4ja
- Pinata URL: https://gateway.pinata.cloud/ipfs/bafkreibpp4iwxkdvcyfsdehhcciza5nvwlmprabakp4humxwzjybmmn4ja
- 文件大小: 1058 bytes
- 上传时间: 2025-08-05T02:56:54.110Z

=== 3. 注册内容到BSC链 ===
激活的内容类型: Result(8) [
  'video',
  'novel',
  'short_drama',
  'anime',
  'manga',
  'music',
  'article',
  'short_video'
]
video内容费用: 1.0 PAT
🔍 检查注册前状态...
- 用户PAT余额: 2996.0 PAT
- 授权额度: 115792089237316195423570985008687907853269984665640564039457.584007913129639935 PAT
- 需要费用: 1.0 PAT
📝 注册内容到BSC链...
⏳ 等待交易确认...
🎉 内容注册成功！
- 交易哈希: 0x834c72f7274d265eafb4b354ca52e42d68b78eaabe133e83e900b8f7f9c65a05
- 内容ID: 5
- Gas使用: 436153
- Gas费用: 0.0 BNB

=== 4. 验证链上内容 ===
✅ 链上内容验证:
- 标题: PXPAC生态BSC智能合约开发教程 - 1754362611249
- 类型: video
- 创建者: ******************************************
- IPFS哈希: bafkreibpp4iwxkdvcyfsdehhcciza5nvwlmprabakp4humxwzjybmmn4ja
- 元数据URI: bafkreibpp4iwxkdvcyfsdehhcciza5nvwlmprabakp4humxwzjybmmn4ja
- 创建时间: 2025/8/5 10:56:57

=== 5. 检查用户余额变化 ===
用户1最终PAT余额: 2995.0 PAT
本次消耗PAT: 1.0 PAT

=== 6. 测试内容铸造NFT ===
📊 铸造价格: 1.0 BNB
💰 用户1 BNB余额: 4.9 BNB
📊 用户是否已铸造: false
📝 铸造内容NFT...
🎉 内容NFT铸造成功！
- NFT ID: 2
- 所有者: ******************************************
- 铸造费用: 1.0 BNB
- Gas使用: 474043
💰 用户1最终BNB余额: 4.8 BNB

=== 7. 测试结果汇总 ===
🎊 BSC链IPFS上链测试成功！
================================================
✅ 内容已成功注册到BSC链
✅ 使用PAT代币支付费用
✅ 使用BNB支付Gas费用
✅ 内容NFT铸造成功
✅ PXPAC生态完整功能验证

🚀 下一步操作:
1. 查看内容统计:
   npx hardhat run scripts/query/content-stats.js --network localhost
2. 测试治理投票:
   npx hardhat run scripts/test/governance-system-test.js --network localhost
3. 测试质押奖励:
   npx hardhat run scripts/test/staking-system-test.js --network localhost

📊 生态系统对比:
- BSC链: PAT代币 + BNB Gas费用（成熟生态）
- PXA链: PXA-T代币 + PXA Gas费用（原生生态）
- 跨链桥: 支持资产在两链间自由流动

🔗 内容访问:
- IPFS: https://gateway.pinata.cloud/ipfs/bafkreibpp4iwxkdvcyfsdehhcciza5nvwlmprabakp4humxwzjybmmn4ja
- 内容ID: 5
- BSC浏览器: https://bscscan.com/tx/0x834c72f7274d265eafb4b354ca52e42d68b78eaabe133e83e900b8f7f9c65a05
jzy:bsc-pxt-pat-tokens junziliuyi$ 