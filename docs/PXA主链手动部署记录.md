# 🚀 PXA链本地测试完整手动执行指南

## 📋 完整部署流程概览

### 🎯 部署步骤总览
1. **环境准备** - 清理环境，确保干净启动
2. **启动PXA链** - 启动本地区块链网络
3. **验证链功能** - 检查基础RPC和账户功能
4. **编译合约** - 编译所有智能合约
5. **部署核心合约** - GasFeeManager、TokenFactory等
6. **测试TokenFactory** - 验证代币创建功能
7. **部署治理系统** - 治理生态和RPC接口
8. **配置验证者奖励** - 挖矿奖励和区块奖励
9. **部署wPAT系统** - 跨链代币和桥接功能
10. **部署内容系统** - ContentRegistry、ContentCharacter、ContentMint
11. **测试内容上链** - IPFS上传、内容注册、NFT铸造
12. **测试跨链功能** - BSC到PXA的跨链测试
13. **配置管理工具** - 费用分配、钱包管理

### 🔧 核心功能模块
- **💰 代币经济**: PXA原生代币 + wPAT跨链代币
- **🏭 代币工厂**: 动态费用的代币创建系统
- **📝 内容上链**: IPFS + 区块链的内容注册和NFT铸造
- **🌉 跨链桥**: BSC ↔ PXA 的资产跨链
- **🏛️ 治理系统**: 去中心化治理和验证者奖励
- **💸 费用管理**: 多钱包的费用分配机制

---

## 📋 测试前准备

### 环境要求
- Node.js 18+
- npm 或 yarn
- Git
- 至少 4GB 可用内存

### 环境变量配置
```bash
# 在 pxa-chain 目录下创建 .env 文件
export DEPLOYER_PRIVATE_KEY="0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80"
export FEE_WALLET_1="******************************************"
export FEE_WALLET_2="******************************************"
export FEE_WALLET_3="******************************************"
# 根据实际需要添加更多钱包地址
```

### 目录结构确认
```
pxa-chain/
├── local-chain/           # 本地链启动脚本
├── scripts/              # 部署和测试脚本
├── contracts/            # 智能合约
├── hardhat.config.js     # Hardhat配置
└── package.json          # 项目依赖
```

---

## 🔄 第1步：清理环境（重新开始时）

```bash
# 进入PXA链目录
cd pxa-chain

# 停止可能运行的链
cd local-chain
./stop-pxa-chain.sh

# 清理所有数据
./clean-pxa-chain.sh

# 清理编译缓存
cd ../
npx hardhat clean
rm -rf deployments/localhost
```

**✅ 验证点：** 确保没有 `polygon-edge` 进程在运行
```bash
ps aux | grep polygon-edge
```

---

## 🚀 第2步：启动PXA本地链

```bash
cd local-chain

# 方式1：交互式启动（推荐调试时使用）
./local-chain-setup.sh

# 方式2：后台启动（推荐测试时使用）
./start-background.sh

# 方式3：重启现有链（保留数据）
./restart-local-chain.sh
```

**⏰ 等待时间：** 启动后等待 10-15 秒让链稳定

**✅ 验证点：** 链启动成功
```bash
# 检查区块高度（应该返回十六进制数字）
curl -X POST http://127.0.0.1:8545 \
  -H "Content-Type: application/json" \
  -d '{"method":"eth_blockNumber","params":[],"id":1,"jsonrpc":"2.0"}'

# 预期返回：{"jsonrpc":"2.0","id":1,"result":"0x1"}
```

---

## 🔧 第3步：验证链基础功能

### 3.1 检查链信息
```bash
# 检查链ID（应该返回 0x9，即十进制的9）
curl -X POST http://127.0.0.1:8545 \
  -H "Content-Type: application/json" \
  -d '{"method":"eth_chainId","params":[],"id":1,"jsonrpc":"2.0"}'

# 检查节点版本
curl -X POST http://127.0.0.1:8545 \
  -H "Content-Type: application/json" \
  -d '{"method":"web3_clientVersion","params":[],"id":1,"jsonrpc":"2.0"}'
```

### 3.2 检查预分配账户
```bash
# 检查第一个预分配账户余额
curl -X POST http://127.0.0.1:8545 \
  -H "Content-Type: application/json" \
  -d '{"method":"eth_getBalance","params":["******************************************","latest"],"id":1,"jsonrpc":"2.0"}'

# 预期返回：大量余额（十六进制）
```

**✅ 验证点：** 所有检查都应该返回正常数据，无错误信息

---

## 📦 第4步：编译智能合约

```bash
cd ../  # 回到 pxa-chain 根目录

# 清理并重新编译
npx hardhat clean
npx hardhat compile
```

**✅ 验证点：** 编译成功，无错误信息
```bash
# 检查编译产物
ls -la artifacts/contracts/
```

---

## 🚀 第5步：部署核心合约系统

### 5.1 部署核心合约（必须第一步！）
```bash
npx hardhat run scripts/deployment/complete-deployment.js --network localhost
```

**✅ 验证点：** 
- 看到 "✅ 所有合约部署完成" 消息
- 生成 `deployments/localhost/` 目录

### 5.2 修复所有脚本（必须第二步！）
```bash
npx hardhat run scripts/fix-all-scripts.js --network localhost
```

**✅ 验证点：** 看到 "✅ 所有脚本修复完成" 消息

### 5.3 配置费用分配钱包（推荐）
```bash
npx hardhat run scripts/management/update-fee-distribution-from-env.js --network localhost
```

**✅ 验证点：** 费用分配配置成功

---

## 🧪 第6步：测试TokenFactory系统

### 6.1 基础功能测试
```bash
npx hardhat run scripts/testing/test-token-factory.js --network localhost
```

**✅ 验证点：** 
- 代币创建成功
- 费用计算正确
- 事件正常触发

### 6.2 动态费用管理测试
```bash
npx hardhat run scripts/management/manage-token-factory-fees.js --network localhost
```

**✅ 验证点：** 费用配置更新成功

---

## 🏛️ 第7步：部署治理生态系统
```bash
# 部署和配置治理生态
npx hardhat run scripts/deployment/deploy-governance-ecosystem.js --network localhost

# 测试治理生态
npx hardhat run scripts/testing/test-governance-ecosystem.js --network localhost

# 测试RPC接口
npx hardhat run scripts/testing/test-rpc-interfaces.js --network localhost
```

**✅ 验证点：**
- 治理合约部署成功
- RPC接口响应正常
- 治理功能测试通过

---

## ⛏️ 第8步：验证者奖励和区块奖励
```bash
# 测试验证者挖矿奖励
npx hardhat run scripts/testing/test-validator-rewards.js --network localhost

# 配置区块奖励机制（可选）
npx hardhat run scripts/management/configure-block-rewards.js --network localhost

# 检查RPC限制
npx hardhat run scripts/testing/check-rpc-limits.js --network localhost
```

**✅ 验证点：**
- 验证者奖励机制正常
- 区块奖励配置成功
- RPC限制检查通过

---

## 🪙 第9步：部署跨链wPAT系统
```bash
# 部署专门的wPAT代币合约（跨链功能）
npx hardhat run scripts/deploy/deploy-wpat-token.js --network localhost

# 测试wPAT代币功能
npx hardhat run scripts/test/test-wpat-functionality.js --network localhost

# 执行跨链铸造wPAT（需要先在BSC端执行跨链）
npx hardhat run scripts/bridge/mint-bridged-tokens.js --network localhost

# 查询跨链统计信息
npx hardhat run scripts/query/cross-chain-stats.js --network localhost
```

**✅ 验证点：**
- wPAT代币部署成功
- 跨链功能正常
- 代币铸造和转移正常

---

## 📝 第10步：部署内容上链系统
```bash
# 部署内容系统（IPFS上链功能）
npx hardhat run scripts/deployment/deploy-content-system.js --network localhost

# 修复内容系统脚本地址
npx hardhat run scripts/fix-content-scripts.js --network localhost
```

**✅ 验证点：**
- ContentRegistry部署成功
- ContentCharacter部署成功
- ContentMint部署成功
- 脚本地址修复完成

---

## 🧪 第11步：测试内容上链功能

### 11.1 测试分享内容IPFS上链
```bash
# 测试IPFS内容上链
npx hardhat run scripts/test/pxa-ipfs-content-upload.js --network localhost
```

**✅ 验证点：**
- 内容注册成功
- IPFS上传正常
- wPAT代币支付成功

### 11.2 测试内容铸造NFT
```bash
# 测试内容铸造NFT
npx hardhat run scripts/test/test-content-mint.js --network localhost
```

**✅ 验证点：**
- NFT铸造成功
- 元数据正确
- 所有权转移正常

### 11.3 查询内容系统统计
```bash
# 查询内容系统统计
npx hardhat run scripts/query/content-stats.js --network localhost
```

**✅ 验证点：**
- 统计数据正确
- 内容数量统计准确
- 费用统计正常

---

## 🌉 第12步：测试跨链功能（可选）

### 12.1 启动BSC测试链（如需要）
```bash
# 在新终端窗口中
cd ../bsc-pxt-pat-tokens/local-bsc-chain
./start-background.sh
```

### 12.2 测试跨链接收
```bash
# 回到PXA链目录
cd ../../pxa-chain
npx hardhat run scripts/bridge/mint-bridged-tokens.js --network localhost
```

**✅ 验证点：** 跨链代币铸造成功

---

## 🔧 第13步：管理工具和配置

### 13.1 钱包和费用分配管理
```bash
# 显示所有环境变量中的钱包配置
npx hardhat run scripts/query/show-wallet-config.js --network localhost

# 使用环境变量更新GasFeeManager的费用分配
npx hardhat run scripts/management/update-fee-distribution-from-env.js --network localhost

# 查看当前费用配置和分配钱包
npx hardhat run scripts/query/check-gas-fee-manager.js --network localhost

# 测试完整的Gas费用收取和分配流程
npx hardhat run scripts/testing/test-gas-fees-collection.js --network localhost
```

### 13.2 TokenFactory动态费用管理
```bash
# 查看当前动态费用配置和建议
npx hardhat run scripts/management/manage-token-factory-fees.js --network localhost
```

**✅ 验证点：**
- 钱包配置正确
- 费用分配正常
- 动态费用机制工作正常

---

## 🔧 常用管理命令参考

### 链状态检查
```bash
# 检查区块高度
curl -X POST http://127.0.0.1:8545 -H "Content-Type: application/json" -d '{"method":"eth_blockNumber","params":[],"id":1,"jsonrpc":"2.0"}'

# 检查链ID
curl -X POST http://127.0.0.1:8545 -H "Content-Type: application/json" -d '{"method":"eth_chainId","params":[],"id":1,"jsonrpc":"2.0"}'

# 检查Gas价格
curl -X POST http://127.0.0.1:8545 -H "Content-Type: application/json" -d '{"method":"eth_gasPrice","params":[],"id":1,"jsonrpc":"2.0"}'

# 检查网络状态
curl -X POST http://127.0.0.1:8545 -H "Content-Type: application/json" -d '{"method":"net_listening","params":[],"id":1,"jsonrpc":"2.0"}'
```

### 链管理操作
```bash
# 停止链
cd local-chain
./stop-pxa-chain.sh

# 重启链（保留数据）
./restart-local-chain.sh

# 后台启动
./start-background.sh

# 检查进程状态
ps aux | grep polygon-edge

# 查看日志（如果后台运行）
tail -f chain.log
```

### 合约管理
```bash
# 查看已部署合约
cat deployments/localhost/.chainId
ls -la deployments/localhost/

# 重新部署特定合约
npx hardhat run scripts/deployment/deploy-token-factory.js --network localhost

# 验证合约
npx hardhat verify --network localhost <CONTRACT_ADDRESS>
```

---

## 🌐 MetaMask配置

### 网络配置
```
网络名称: PXA Local Chain
RPC URL: http://127.0.0.1:8545
链ID: 9
货币符号: PXA
区块浏览器: (留空)
```

### 导入测试账户
```
私钥: 0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
地址: 0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266
余额: 10000 PXA
```

---

## 🚨 故障排除指南

### 常见问题及解决方案

#### 1. 端口被占用
```bash
# 检查端口占用
lsof -i :8545

# 强制释放端口
kill -9 <PID>

# 或者使用不同端口
export RPC_PORT=8546
```

#### 2. 进程残留
```bash
# 查找残留进程
ps aux | grep polygon-edge

# 强制杀死所有相关进程
pkill -f polygon-edge

# 清理PID文件
rm -f local-chain/chain.pid
```

#### 3. 数据损坏
```bash
cd local-chain
./clean-pxa-chain.sh
./local-chain-setup.sh
```

#### 4. 编译错误
```bash
# 清理并重新安装
npx hardhat clean
rm -rf node_modules
rm package-lock.json
npm install
npx hardhat compile
```

#### 5. 部署失败
```bash
# 清理部署记录
rm -rf deployments/localhost

# 检查网络连接
curl -X POST http://127.0.0.1:8545 -H "Content-Type: application/json" -d '{"method":"eth_blockNumber","params":[],"id":1,"jsonrpc":"2.0"}'

# 重新部署
npx hardhat run scripts/deployment/complete-deployment.js --network localhost
```

#### 6. Gas费用问题
```bash
# 检查Gas价格
curl -X POST http://127.0.0.1:8545 -H "Content-Type: application/json" -d '{"method":"eth_gasPrice","params":[],"id":1,"jsonrpc":"2.0"}'

# 检查账户余额
curl -X POST http://127.0.0.1:8545 -H "Content-Type: application/json" -d '{"method":"eth_getBalance","params":["YOUR_ADDRESS","latest"],"id":1,"jsonrpc":"2.0"}'
```

### 完全重置流程
```bash
# 1. 停止所有服务
cd local-chain
./stop-pxa-chain.sh

# 2. 清理所有数据
./clean-pxa-chain.sh
cd ../
npx hardhat clean
rm -rf deployments/localhost
rm -rf node_modules

# 3. 重新安装依赖
npm install

# 4. 重新启动
cd local-chain
./local-chain-setup.sh

# 5. 重新部署
cd ../
npx hardhat run scripts/deployment/complete-deployment.js --network localhost
```

---

## ✅ 测试验证清单

### 基础功能验证
- [ ] 链启动成功（区块高度递增）
- [ ] RPC接口响应正常
- [ ] 链ID正确（9）
- [ ] 预分配账户有余额
- [ ] 9秒出块时间正常

### 核心合约功能验证
- [ ] 合约编译成功
- [ ] GasFeeManager部署成功
- [ ] TokenFactory部署成功
- [ ] ValidatorRegistry部署成功
- [ ] BridgeReceiver部署成功

### 治理和奖励系统验证
- [ ] 治理生态系统部署成功
- [ ] 验证者奖励机制正常
- [ ] 区块奖励配置成功
- [ ] RPC接口限制检查通过

### wPAT跨链系统验证
- [ ] wPAT代币部署成功
- [ ] 跨链铸造功能正常
- [ ] 跨链统计数据正确
- [ ] 代币转移功能正常

### 内容上链系统验证
- [ ] ContentRegistry部署成功
- [ ] ContentCharacter部署成功
- [ ] ContentMint部署成功
- [ ] IPFS内容上传正常
- [ ] 内容注册功能正常
- [ ] 内容铸造NFT功能正常
- [ ] wPAT代币支付正常

### 费用管理系统验证
- [ ] 费用分配钱包配置正确
- [ ] Gas费用收取和分配正常
- [ ] TokenFactory动态费用正常
- [ ] 环境变量钱包配置正确

### 集成功能验证
- [ ] MetaMask连接正常
- [ ] 交易发送成功
- [ ] 事件监听正常
- [ ] 跨链功能正常（如启用）

### 性能验证
- [ ] 交易确认时间 < 30秒
- [ ] Gas费用合理
- [ ] 内存使用正常
- [ ] CPU使用正常

---

## 📞 技术支持

### 日志位置
```bash
# 链日志
tail -f local-chain/chain.log

# Hardhat日志
npx hardhat node --verbose

# 系统日志
journalctl -f -u polygon-edge
```

### 调试模式
```bash
# 启用详细日志
export DEBUG=true
./start-background.sh

# Hardhat调试
npx hardhat run scripts/xxx.js --network localhost --verbose
```

### 监控命令
```bash
# 实时监控区块
watch -n 1 'curl -s -X POST http://127.0.0.1:8545 -H "Content-Type: application/json" -d "{\"method\":\"eth_blockNumber\",\"params\":[],\"id\":1,\"jsonrpc\":\"2.0\"}" | jq'

# 监控内存使用
watch -n 5 'ps aux | grep polygon-edge'
```

---

## 🎯 下一步计划

完成本地测试后，你可以：

1. **部署到测试网络**
   - 配置公网节点
   - 部署到云服务器
   - 开放公共RPC

2. **开发区块浏览器**
   - 参考 `docs/block-explorer-roadmap.md`
   - 实现数据索引
   - 构建用户界面

3. **集成前端应用**
   - 连接Web3钱包
   - 实现代币交互
   - 构建用户界面

4. **性能优化**
   - 调整区块参数
   - 优化Gas费用
   - 提升TPS

---

**🎉 祝你测试顺利！如有问题，请参考故障排除部分或查看相关日志。**