# BSC测试网部署指南

## 📋 部署前准备清单

### 1. 🔑 账户准备

#### 必需账户 (至少3个)
- **部署者账户**: 用于部署合约，需要足够的tBNB
- **国库账户**: 用于接收和管理代币
- **操作员账户**: 用于日常操作和管理

#### 推荐账户 (可选)
- **测试用户账户**: 用于功能测试 (3个)

### 2. 💰 tBNB准备

#### 最低要求
- **部署者账户**: 至少 **0.5 tBNB** (推荐 1 tBNB)
- **其他账户**: 每个至少 **0.1 tBNB**

#### 获取tBNB方式
1. **BSC测试网水龙头**: https://testnet.binance.org/faucet-smart
2. **备用水龙头**: https://testnet.bnbchain.org/faucet-smart
3. **社区水龙头**: 搜索 "BSC testnet faucet"

### 3. 🔧 开发环境

#### 必需工具
- Node.js (推荐 v18.x 或 v20.x)
- npm 或 yarn
- Git

#### 验证安装
```bash
node --version  # 应该显示 v18.x 或更高
npm --version   # 应该正常显示版本号
```

### 4. 🌐 网络配置

#### RPC端点 (已配置)
- 主要: `https://data-seed-prebsc-1-s1.binance.org:8545`
- 备用: `https://data-seed-prebsc-2-s1.binance.org:8545`

#### 网络信息
- **网络名称**: BSC Testnet
- **链ID**: 97
- **符号**: tBNB
- **区块浏览器**: https://testnet.bscscan.com

### 5. 📊 API密钥

#### BSCScan API密钥 (用于合约验证)
1. 访问: https://testnet.bscscan.com/apis
2. 注册账户并创建API密钥
3. 将密钥添加到环境变量

## 🚀 部署步骤

### 步骤1: 环境配置

1. **复制环境配置文件**
```bash
cp .env.bsc-testnet .env
```

2. **编辑环境变量**
```bash
# 编辑 .env 文件，填入真实的私钥和API密钥
nano .env
```

3. **必需配置项**
```env
DEPLOYER_PRIVATE_KEY=你的部署者私钥
TREASURY_PRIVATE_KEY=你的国库私钥
OPERATOR_PRIVATE_KEY=你的操作员私钥
BSCSCAN_API_KEY=你的BSCScan_API密钥
```

### 步骤2: 检查账户余额

```bash
# 检查所有账户的tBNB余额
npm run check:balances
```

### 步骤3: 编译合约

```bash
# 编译所有合约
npm run compile
```

### 步骤4: 部署核心代币系统

```bash
# 部署PXT和PAT代币
npx hardhat run scripts/bsc-testnet/deploy/01-deploy-core-tokens.js --network bscTestnet
```

### 步骤5: 部署质押系统

```bash
# 部署质押相关合约
npx hardhat run scripts/bsc-testnet/deploy/02-deploy-staking-system.js --network bscTestnet
```

### 步骤6: 部署治理系统

```bash
# 部署DAO治理合约
npx hardhat run scripts/bsc-testnet/deploy/03-deploy-governance.js --network bscTestnet
```

### 步骤7: 验证合约

```bash
# 在BSCScan上验证合约代码
npm run verify:bsc-testnet
```

## 🧪 部署后测试

### 基础功能测试
```bash
npm run test:bsc-testnet:basic
```

### 奖励系统测试
```bash
npm run test:bsc-testnet:rewards
```

### 治理系统测试
```bash
npm run test:bsc-testnet:governance
```

## 📁 部署文件结构

```
deployments/
└── bscTestnet/
    ├── core-deployment.json      # 核心代币部署信息
    ├── staking-deployment.json   # 质押系统部署信息
    ├── governance-deployment.json # 治理系统部署信息
    └── complete-deployment.json  # 完整部署信息
```

## ⚠️ 安全注意事项

### 私钥安全
1. **永远不要**将私钥提交到Git仓库
2. 使用环境变量存储敏感信息
3. 定期轮换测试网私钥
4. 生产环境使用硬件钱包

### 部署验证
1. 部署前仔细检查所有配置
2. 小额测试后再进行大额操作
3. 保存所有部署信息和交易哈希
4. 在区块浏览器上验证所有交易

### 备份策略
1. 备份所有私钥和助记词
2. 保存部署配置文件
3. 记录所有合约地址
4. 导出重要交易记录

## 🔧 故障排除

### 常见问题

#### 1. Gas费用不足
```
Error: insufficient funds for gas * price + value
```
**解决方案**: 向部署账户添加更多tBNB

#### 2. 网络连接问题
```
Error: network timeout
```
**解决方案**: 
- 检查网络连接
- 尝试备用RPC端点
- 增加超时时间

#### 3. 合约大小超限
```
Error: contract code size exceeds limit
```
**解决方案**:
- 启用编译器优化
- 拆分大型合约
- 使用代理模式

#### 4. Nonce错误
```
Error: nonce too low/high
```
**解决方案**:
- 重置MetaMask账户
- 手动设置nonce
- 等待网络同步

## 📞 支持联系

如果遇到问题，请：
1. 检查本指南的故障排除部分
2. 查看项目文档
3. 在GitHub上创建Issue
4. 联系开发团队

## 📚 相关资源

- [BSC测试网文档](https://docs.bnbchain.org/docs/testnet)
- [Hardhat文档](https://hardhat.org/docs)
- [OpenZeppelin文档](https://docs.openzeppelin.com/)
- [BSCScan测试网](https://testnet.bscscan.com)
