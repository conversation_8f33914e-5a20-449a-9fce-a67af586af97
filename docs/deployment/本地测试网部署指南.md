# 本地测试网部署与测试指南

这份指南将帮助您在本地测试网络上部署和全面测试代币系统。支持两种本地测试网络：
- **Hardhat网络**：适合快速开发和单元测试
- **Polygon-Edge BSC链**：模拟真实BSC环境，适合跨链桥测试

## 🚀 一键启动（推荐）

### 方式一：Hardhat网络快速启动

```bash
# 清空并重新测试
pkill -f "hardhat node" 2>/dev/null || true
rm -rf deployments/localhost/
npx hardhat clean

# 然后在新终端启动节点
npx hardhat node

# 在另一个终端运行测试
npm run quick-start
```

这将自动完成：
- ✅ 清理和编译合约
- ✅ 部署所有系统（核心代币、质押、治理、安全增强）
- ✅ 随机奖励系统（已默认启用）
- ✅ 运行完整系统演示和单元测试

### 方式二：BSC本地链启动（跨链桥测试）

```bash
# 启动BSC本地测试链
npm run bsc:setup

# 在另一个终端部署合约到BSC链
npm run deploy:bsc-local

# 运行跨链桥测试
npm run test:bridge
```

这将自动完成：
- ✅ 启动4节点BSC测试链（链ID: 97）
- ✅ 部署所有合约到BSC链
- ✅ 配置跨链桥功能
- ✅ 运行跨链桥测试

## 🔧 分步部署（开发调试用）

如果您需要逐步部署和测试：

### 第1步：启动测试网络

#### 选项A：Hardhat网络（推荐用于开发）

```bash
# 启动本地Hardhat网络
npm run node:start
```

输出示例：
```
Started HTTP and WebSocket JSON-RPC server at http://127.0.0.1:8545/

Accounts
========
Account #0: ****************************************** (10000 ETH)
Account #1: ****************************************** (10000 ETH)
...
```

#### 选项B：BSC本地链（用于跨链桥测试）

```bash
# 设置并启动BSC本地测试链
npm run bsc:setup
```

输出示例：
```
🚀 设置本地BSC测试链用于跨链桥测试
================================================
🔑 生成BSC测试链验证者节点密钥...
✅ BSC链节点ID: 16Uiu2HAm...
✅ BSC链预分配地址: 0x1234...
🔑 BSC链私钥: 0xabcd...
📜 创建BSC测试链创世文件...
🔥 启动BSC测试链节点...
✅ BSC测试链所有节点已启动
🌐 BSC测试链主节点 RPC: http://127.0.0.1:18545
```

或者重启已有的BSC链：
```bash
# 重启BSC链（保留数据）
npm run bsc:restart

# 清理并重新设置BSC链
npm run bsc:clean && npm run bsc:setup
```

### 第2步：部署合约系统

#### 部署到Hardhat网络

```bash
# 一键部署所有合约到Hardhat网络
npm run deploy:local
```

或分步部署：
```bash
# 部署核心代币系统
npm run deploy:core

# 部署质押系统
npm run deploy:staking

# 部署治理系统
npm run deploy:governance

# 部署安全增强系统
npm run deploy:security

# 部署自动化管理系统
npm run deploy:auto-chain
```

#### 部署到BSC本地链

```bash
# 一键部署所有合约到BSC本地链
npm run deploy:bsc-local
```

或分步部署到BSC链：
```bash
# 部署核心代币系统到BSC链
npm run deploy:core:bsc

# 部署质押系统到BSC链
npm run deploy:staking:bsc

# 部署治理系统到BSC链
npm run deploy:governance:bsc

# 部署安全增强系统到BSC链
npm run deploy:security:bsc

# 部署自动化管理系统到BSC链
npm run deploy:auto-chain:bsc
```

### 第3步：系统配置（可选）

```bash
# 随机奖励机制（已默认启用，无需手动激活）
# npm run admin:activate-random  # ✅ 已默认启用

# 调整奖励参数（可选）
npm run admin:adjust-ranges

# 验证核心功能
npm run test:unit:core
```

## 🧪 测试流程（按顺序执行）

部署完成后，按以下顺序进行全面测试：

### 1. 核心功能单元测试

```bash
# 测试核心代币功能（推荐先运行）
npm run test:unit:core

# 测试安全功能
npm run test:unit:security

# 测试自动化功能
npm run test:unit:automation

# 运行所有单元测试
npm run test:unit
```

### 2. 基础功能集成测试

```bash
# 测试代币基本功能、转账、授权
npm run test:basic
```

验证项目：
- ✅ PXT总供应量：100,000,000
- ✅ PAT总供应量：300,000,000
- ✅ 代币转账和授权功能
- ✅ 合约权限设置
- ✅ 随机奖励系统默认启用

### 3. 质押系统测试

```bash
# 测试质押池、奖励计算、等级系统
npm run test:rewards
```

验证项目：
- ✅ 质押和解质押功能
- ✅ 7个质押等级（丁级到至尊）
- ✅ 随机奖励计算和分发（已默认启用）
- ✅ 解锁期管理
- ✅ 惩罚机制

### 4. 治理系统测试

```bash
# 测试DAO、投票、提案功能
npm run test:governance
```

验证项目：
- ✅ 提案创建和管理
- ✅ 投票机制
- ✅ 治理权限控制
- ✅ 时间锁机制
- ✅ 多签治理

### 5. 安全系统测试

```bash
# 测试安全增强功能
npm run test:security

# 测试自动化管理
npm run test:auto-chain
```

验证项目：
- ✅ 时间锁控制器
- ✅ 多签治理机制
- ✅ 紧急暂停功能
- ✅ 自动转账管理
- ✅ 权限控制系统

### 6. 经济模型测试

```bash
# 测试完整经济模型和参数
npm run test:economic
```

验证项目：
- ✅ 代币分配策略
- ✅ 随机奖励机制
- ✅ 激励模型
- ✅ 长期经济平衡

### 7. 完整系统演示

```bash
# 运行完整的用户场景演示
npm run demo:full
```

包含场景：
- 👥 多用户质押（不同等级）
- 💰 随机奖励累积和领取
- 🏛️ 治理提案和投票
- 🔄 解质押流程
- 📊 系统数据统计
- 🔒 安全功能演示

### 8. 特殊功能测试

```bash
# 测试随机奖励系统（已默认启用）
npm run demo:random

# 测试惩罚机制
npm run demo:penalty

# 测试解锁期功能
npm run demo:unlock

# 测试安全功能
npm run test:security

# 测试自动化功能
npm run test:auto-chain
```

### 7. 系统状态调试

```bash
# 查看详细的系统状态和数据
npm run debug
```

### 8. 经济模型仿真

```bash
# 运行长期经济模型仿真
npm run simulate
```

## 📊 质押等级配置

| 等级 | 名称 | 最小金额 | 固定倍数 | 随机奖励范围 | 推荐期限 |
|------|------|----------|----------|-------------|----------|
| 1 | 丁级 | 100 PXT | 1.3x | 1.2x - 1.3x | 30天 |
| 2 | 丙级 | 1,000 PXT | 1.4x | 1.3x - 1.4x | 90天 |
| 3 | 乙级 | 5,000 PXT | 1.6x | 1.5x - 1.6x | 180天 |
| 4 | 甲级 | 20,000 PXT | 2.0x | 1.9x - 2.0x | 270天 |
| 5 | 十绝 | 100,000 PXT | 2.5x | 2.0x - 2.5x | 365天 |
| 6 | 双十绝 | 250,000 PXT | 3.0x | 3.0x - 4.0x | 540天 |
| 7 | 至尊 | 500,000 PXT | 5.0x | 4.0x - 6.0x | 730天 |

**注意**：随机奖励系统已默认启用，用户将在对应范围内随机获得奖励倍数。

## 📋 代币分配详情

### PXT治理代币（总量：100,000,000）
- 🏛️ 社区挖矿与激励：45% (25,002,000 全球 + 19,998,000 中国大陆)
- 👥 团队持有：10% (10年递进解锁)
- 🌱 平台生态建设：15%
- 💼 私募投资者：15% (1年锁定+2年线性解锁)
- 🤝 战略合作伙伴：5%
- 📢 市场营销：5%
- 🛡️ 安全储备：5%

### PAT功能代币（总量：300,000,000）
- 🇨🇳 中国大陆池：50,000,000
- 🌍 全球池：50,000,000
- 🏆 质押奖励池：200,000,000

## 🔧 系统配置参数

### 治理配置
- 投票延迟：1天
- 投票期限：14天
- 提案门槛：10,000 PXT
- 最低投票率：10%
- 时间锁延迟：48小时

### 随机奖励配置（已默认启用）
- 更新频率：每小时
- 随机算法：基于用户种子+时间戳
- 丁级随机范围：1.2x - 1.3x
- 丙级随机范围：1.3x - 1.4x
- 乙级随机范围：1.5x - 1.6x
- 甲级随机范围：1.9x - 2.0x
- 十绝随机范围：2.0x - 2.5x
- 双十绝随机范围：3.0x - 4.0x
- 至尊随机范围：4.0x - 6.0x

## 📁 部署文件位置

成功部署后，合约信息保存在：

### Hardhat网络部署文件

```
deployments/localhost/
├── core-deployment.json       # 核心代币部署信息
├── staking-deployment.json    # 质押系统部署信息
├── governance-deployment.json # 治理系统部署信息
├── security-deployment.json   # 安全增强部署信息
├── auto-chain-deployment.json # 自动化管理部署信息
└── complete-deployment.json   # 完整部署信息（主要）
```

### BSC本地链部署文件

```
deployments/bsc-local/
├── core-deployment.json       # 核心代币部署信息
├── staking-deployment.json    # 质押系统部署信息
├── governance-deployment.json # 治理系统部署信息
├── security-deployment.json   # 安全增强部署信息
├── auto-chain-deployment.json # 自动化管理部署信息
└── complete-deployment.json   # 完整部署信息（主要）
```

### BSC链配置文件

```
local-bsc-chain/
├── polygon-edge              # BSC链二进制文件
├── bsc-genesis.json          # BSC链创世文件
├── bsc-chain-1/              # 节点1数据目录
├── bsc-chain-2/              # 节点2数据目录
├── bsc-chain-3/              # 节点3数据目录
├── bsc-chain-4/              # 节点4数据目录
├── bsc-chain.pid             # BSC链进程ID文件
├── setup-local-bsc.sh        # BSC链设置脚本
├── restart-local-bsc.sh      # BSC链重启脚本
├── stop-bsc-chain.sh         # BSC链停止脚本
└── clean-bsc-chain.sh        # BSC链清理脚本
```

## 🚨 常见问题解决

### Q: 部署时显示"insufficient funds"
**A:**
- **Hardhat网络**：每个账户默认有10000 ETH，通常足够使用。检查网络是否正常运行。
- **BSC本地链**：预分配地址有10000 BNB测试币，检查BSC链是否正常启动。

### Q: 质押功能测试失败
**A:** 确保：
1. 用户有足够PXT余额
2. 已授权质押池使用PXT
3. 质押金额≥100 PXT（丁级最小值）

### Q: 随机奖励系统无效
**A:** 随机奖励系统已默认启用。如需检查状态，运行 `npm run debug` 查看 `randomRewardEnabled: true`。

### Q: 治理投票失败
**A:** 确保：
1. 用户有足够的PXT余额（≥10,000 提案门槛）
2. 已质押获得投票权
3. 在投票期限内

### Q: 奖励计算为0
**A:** 正常现象，奖励需要时间累积。可使用以下命令推进时间：
```javascript
// 在Hardhat console中
await ethers.provider.send("evm_increaseTime", [24 * 60 * 60]); // 推进1天
await ethers.provider.send("evm_mine");
```

### Q: BSC本地链启动失败
**A:** 检查：
1. 确保polygon-edge二进制文件存在
2. 端口18545、19632、20001-20004未被占用
3. 运行 `npm run bsc:clean` 清理旧数据后重试

### Q: BSC链连接失败
**A:** 确保：
1. BSC链正在运行（检查 `http://127.0.0.1:18545`）
2. 网络配置正确（链ID: 97）
3. 使用正确的私钥和地址

### Q: 跨链桥测试失败
**A:** 确保：
1. 两条链都正常运行
2. 合约已部署到两条链
3. 桥接合约地址配置正确

## 🔄 重新部署

### Hardhat网络重新部署

```bash
# 停止当前网络 (Ctrl+C)
# 重新启动
npm run node:start

# 删除部署文件（新终端）
rm -rf deployments/localhost/

# 重新部署
npm run deploy:local
```

### BSC本地链重新部署

```bash
# 停止BSC链
npm run bsc:stop

# 清理数据并重新设置
npm run bsc:clean
npm run bsc:setup

# 删除BSC部署文件（新终端）
rm -rf deployments/bsc-local/

# 重新部署到BSC链
npm run deploy:bsc-local
```

## 📚 下一步

测试成功后，您可以：

1. **开发前端应用**：
   - Hardhat网络：使用 `deployments/localhost/complete-deployment.json` 中的合约地址
   - BSC本地链：使用 `deployments/bsc-local/complete-deployment.json` 中的合约地址
2. **集成Web3**：连接钱包和合约进行交互
3. **跨链桥开发**：使用BSC本地链测试跨链桥功能
4. **压力测试**：测试高并发和大量用户场景
5. **部署测试网**：使用 `npm run deploy:testnet` 部署到BSC测试网
6. **安全审计**：在主网部署前进行专业安全审计
7. **随机奖励验证**：验证随机奖励系统在生产环境的表现
8. **自动化管理测试**：测试自动转账和管理功能

## 🌐 网络配置信息

### Hardhat网络配置
- **网络名称**: Hardhat Local
- **RPC URL**: http://127.0.0.1:8545
- **链ID**: 31337
- **货币符号**: ETH
- **出块时间**: 即时

### BSC本地链配置
- **网络名称**: Local BSC Test
- **RPC URL**: http://127.0.0.1:18545
- **链ID**: 97
- **货币符号**: BNB
- **出块时间**: 3秒

## 🔄 BSC本地链迁移指南

如果您已有BSC本地链文件夹（如 `pxpac-chain/local-bsc-chain`），可以按以下步骤迁移：

### 步骤1：复制BSC链文件

```bash
# 在项目根目录执行
cp -r /path/to/pxpac-chain/local-bsc-chain ./local-bsc-chain

# 或者如果在同一父目录下
cp -r ../pxpac-chain/local-bsc-chain ./local-bsc-chain
```

### 步骤2：更新package.json脚本

在 `package.json` 中添加BSC链管理脚本：

```json
{
  "scripts": {
    "bsc:setup": "cd local-bsc-chain && ./setup-local-bsc.sh",
    "bsc:restart": "cd local-bsc-chain && ./restart-local-bsc.sh",
    "bsc:stop": "cd local-bsc-chain && ./stop-bsc-chain.sh",
    "bsc:clean": "cd local-bsc-chain && ./clean-bsc-chain.sh",
    "deploy:bsc-local": "hardhat run scripts/deployments/deploy-all.js --network bsc-local",
    "test:bridge": "hardhat test test/bridge/ --network bsc-local"
  }
}
```

### 步骤3：更新hardhat.config.js

添加BSC本地网络配置：

```javascript
module.exports = {
  networks: {
    // ... 其他网络配置
    "bsc-local": {
      url: "http://127.0.0.1:18545",
      chainId: 97,
      accounts: [
        // 从 bsc-local.env 文件中读取私钥
        process.env.BSC_DEPLOYER_PRIVATE_KEY || "0x..."
      ],
      gas: ********,
      gasPrice: ***********
    }
  }
};
```

### 步骤4：配置环境变量

BSC链启动后会自动生成 `bsc-local.env` 文件，包含：
- `BSC_DEPLOYER_PRIVATE_KEY`: BSC链部署私钥
- `BSC_DEPLOYER_ADDRESS`: BSC链部署地址

将这些变量添加到您的 `.env` 文件中。

## 📖 相关文档

- `contracts/` - 智能合约源码
- `scripts/` - 部署和测试脚本
- `docs/` - 详细技术文档
- `test/` - 单元测试文件
- `local-bsc-chain/` - BSC本地链配置和脚本

---

🎉 **恭喜！您的PXT/PAT代币系统已成功部署并测试完成！**