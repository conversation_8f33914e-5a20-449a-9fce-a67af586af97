jun<PERSON><PERSON><PERSON><PERSON>@jzy local-chain % 
jun<PERSON><PERSON><PERSON><PERSON>@jzy local-chain % clear
jun<PERSON><PERSON><PERSON><PERSON>@jzy local-chain % ./local-chain-setup.sh
🚀 启动 PXA 链...
🧹 清理旧数据...
🔑 生成验证者节点密钥...


[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = 0x6E233dF17C5616b16B1038625b54DeEB296a8fb6
BLS Public key       = 0x96ad08007ae90933247fbabc0c26d35507f6c49760bac5bb4fbdd0d4a69880a0d02b51b576dfc5969bc8ada546f64f64
Node ID              = 16Uiu2HAkyHsvFSasxvczPnkMo6nMg3Wg3KrS27wXdgDztbErBVG5



[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = 0xd025cced4484Be0C398f5bc60d63ab601bDDbca4
BLS Public key       = 0x87a726bf13b76166c832fae3b33d4efc6c9e1c214a7e4b356894467220702ed396d2bca20cdafe9c906aee6e3d9a9490
Node ID              = 16Uiu2HAmJ3EAfxk9NLZf8qeYKt4e9rtiN5tUtVtaPJsYxuoe9cZ4



[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = 0x774203569016A43cB033D6F2Ac397b32Ea488D5F
BLS Public key       = 0xa7aea5ac3ecc4baafc816ecfef5f0da2ecf87e14eb858a8b59a20c88455f2a483ef7d35a3ffbf8401a0369442b48b875
Node ID              = 16Uiu2HAmBTnvJ3vhNRXeCJ4AQFqRydYcYM79jNZZPV6g1aR51rf9



[WARNING: INSECURE LOCAL SECRETS - SHOULD NOT BE RUN IN PRODUCTION]

[SECRETS INIT]
Public key (address) = 0x5715bfc46689FA76D0DbdF81cDBCaE9faDB11d42
BLS Public key       = 0x866373977abdcdf20f6fb9f45f4fd2b887fad9cdc1b80b2ca289e03d2abf1b4ef037b170f676dcd852d21e0473b964a1
Node ID              = 16Uiu2HAmVMZUSdyR5xwtQJVzswYoZspyVD16PNkrD4pAcLSBYaiZ

🔑 获取节点、公钥和私钥...
✅ 节点ID: 16Uiu2HAkyHsvFSasxvczPnkMo6nMg3Wg3KrS27wXdgDztbErBVG5
✅ 预分配地址 (公钥): 0x6E233dF17C5616b16B1038625b54DeEB296a8fb6
🔑 私钥 (用于MetaMask导入): 90116a36286c6b00ba3fdaae7289a039d22c20a7dae6a2f0310cbe1956dbc871
   [警告] 这是一个开发密钥，请勿在主网使用!
🔧 更新.env文件中的部署者私钥...
✅ 已更新.env文件中的DEPLOYER_PRIVATE_KEY
💡 提示: 如果你有其他环境变量配置，它们已被保留
📜 创建创世文件...


[GENESIS SUCCESS]

Genesis written to ./genesis.json

✅ 创世文件创建完成
🔍  调试: 正在检查最终的创世文件内容...
{
  "name": "PXA Chain",
  "genesis": {
    "nonce": "0x0000000000000000",
    "timestamp": "0x0",
    "extraData": "0x0000000000000000000000000000000000000000000000000000000000000000f90129f90120f846946e233df17c5616b16b1038625b54deeb296a8fb6b096ad08007ae90933247fbabc0c26d35507f6c49760bac5bb4fbdd0d4a69880a0d02b51b576dfc5969bc8ada546f64f64f84694d025cced4484be0c398f5bc60d63ab601bddbca4b087a726bf13b76166c832fae3b33d4efc6c9e1c214a7e4b356894467220702ed396d2bca20cdafe9c906aee6e3d9a9490f84694774203569016a43cb033d6f2ac397b32ea488d5fb0a7aea5ac3ecc4baafc816ecfef5f0da2ecf87e14eb858a8b59a20c88455f2a483ef7d35a3ffbf8401a0369442b48b875f846945715bfc46689fa76d0dbdf81cdbcae9fadb11d42b0866373977abdcdf20f6fb9f45f4fd2b887fad9cdc1b80b2ca289e03d2abf1b4ef037b170f676dcd852d21e0473b964a180c28080c080",
    "gasLimit": "0x1312d00",
    "difficulty": "0x1",
    "mixHash": "0x0000000000000000000000000000000000000000000000000000000000000000",
    "coinbase": "0x0000000000000000000000000000000000000000",
    "alloc": {
      "0x6E233dF17C5616b16B1038625b54DeEB296a8fb6": {
        "balance": "0x295BE96E64066972000000"
      }
    },
    "number": "0x0",
    "gasUsed": "0x70000",
    "parentHash": "0x0000000000000000000000000000000000000000000000000000000000000000",
    "baseFee": "0x0",
    "baseFeeEM": "0x0",
    "baseFeeChangeDenom": "0x0"
  },
  "params": {
    "forks": {
      "EIP150": {
        "block": 0
      },
      "EIP155": {
        "block": 0
      },
      "EIP158": {
        "block": 0
      },
      "byzantium": {
        "block": 0
      },
      "constantinople": {
        "block": 0
      },
      "homestead": {
        "block": 0
      },
      "istanbul": {
        "block": 0
      },
      "londonfix": {
        "block": 0
      },
      "petersburg": {
        "block": 0
      },
      "quorumcalcalignment": {
        "block": 0
      },
      "txHashWithType": {
        "block": 0
      }
    },
    "chainID": 327,
    "engine": {
      "ibft": {
        "blockTime": 9000000000,
        "epochSize": 100000,
        "type": "PoA",
        "validator_type": "bls"
      }
    },
    "blockGasTarget": 0,
    "burnContract": null,
    "burnContractDestinationAddress": "0x0000000000000000000000000000000000000000"
  },
  "bootnodes": [
    "/ip4/127.0.0.1/tcp/10001/p2p/16Uiu2HAkyHsvFSasxvczPnkMo6nMg3Wg3KrS27wXdgDztbErBVG5"
  ]
}

🔍  调试结束。
🔥 启动所有验证者节点...
▶️ 启动节点1...
▶️ 启动节点2...
▶️ 启动节点3...
▶️ 启动节点4...
✅ 所有节点已启动
🌐 主节点 RPC: http://127.0.0.1:8545
💰 预分配地址: 0x6E233dF17C5616b16B1038625b54DeEB296a8fb6
⏳ 等待链启动...
🧪 测试链状态...
✅ 链状态正常，当前区块高度: 0x1
💰 检查账户余额...
🔍 调试: 余额查询响应: {"jsonrpc":"2.0","id":1,"result":"0x295be96e64066972000000"}
💰 账户余额: 50000000.0000 PXA
====================================
🎉 PXA链启动成功！
====================================
MetaMask网络配置:
网络名称: PXA Chain
RPC URL: http://127.0.0.1:8545
链ID: 327
货币符号: PXA
====================================
🛑 停止链: kill `cat chain.pid` 或按Ctrl+C
====================================
按Enter键停止链，或按Ctrl+C后台运行...
